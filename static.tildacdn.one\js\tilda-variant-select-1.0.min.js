function t_variants__translate(e){var t={checkboxes_hint:{RU:"*Отметьте один или несколько вариантов",EN:"*Select one or more options",DE:"*Wählen Sie eine oder mehrere Optionen",ES:"*Seleccione una o varias opciones",PT:"*Selecionar uma ou mais opções",FR:"*Sélectionner une ou plusieurs options",JA:"*1つ以上のオプションを選択",ZH:"*选择一个或多个选项",UK:"*Виберіть одну або декілька опцій",PL:"*<PERSON><PERSON><PERSON>rz jedną lub więcej opcji",KK:"*Бір немесе бірнеше опцияны таңдаңыз",IT:"*Selezionare una o più opzioni",LV:"*Izvēlieties vienu vai vairākas opcijas"}};if(!t[e])return e;var n=t_variants__getLang()||"EN";return t[e][n]}function t_variants__getLang(){var e,t=document.getElementById("allrecords").getAttribute("data-tilda-project-lang"),n;return t||(window.navigator.userLanguage||window.navigator.language).toUpperCase().slice(0,2)}function t_variants__applyTranslation(e){var t=e.querySelector(".t-quiz__variants-hint");t&&(t.textContent=t_variants__translate("checkboxes_hint"))}!function(){function e(e,t){var n=document.getElementById("rec"+e);if(n){var a=n.querySelector('[data-input-lid="'+t+'"]');if(a){var r=a.querySelector(".t-checkboxes__hiddeninput"),i=a.querySelectorAll(".t-checkbox"),o=Array.from(i).filter((function(e){return e.checked})).map((function(e){return e.value})),l=r.value,c=o.join("; ");r.value=c,l!==c&&r.dispatchEvent(new Event("change",{bubbles:!0}))}}}function t(t,n){var a=document.getElementById("rec"+t);if(a){var r=a.querySelector('[data-input-lid="'+n+'"]');if(r){var i=r.querySelector(".t-checkboxes__ownvariant-wrapper"),o=i.querySelector(".t-input-ownanswer"),l="",c=r.querySelector(".t-input-block_rd-flex"),u=o.closest(".t-ownvariant-wrapper_flex").querySelector("span"),s=i.querySelector(".t-checkbox-ownanswer");s.setAttribute("data-own-variant-title",s.value),s.addEventListener("click",(function(){s.checked?(o.style.display="flex",o.classList.add("t-input-ownanswer_active"),o.focus(),u&&c&&(u.style.display="none")):(o.classList.remove("t-input-ownanswer_active"),o.value.trim()||(o.style.display="none"))})),o.addEventListener("input",(function(){l=o.value})),o.addEventListener("focus",(function(){u&&c&&(u.style.display="none")})),o.addEventListener("blur",(function(){var a=""!==l.trim()?": "+l:"",r=s.getAttribute("data-own-variant-title");s.value=r+a,""===l.trim()&&(o.style.display="none"),u&&c&&(u.style.display="block"),e(t,n)})),s.addEventListener("reset",(function(){l="",o.value="",o.style.display="none"}))}}}function n(n,a){var r=document.getElementById("rec"+n);if(r){var i=r.querySelector('[data-input-lid="'+a+'"]');if(i){var o=i.querySelectorAll(".t-checkbox__control"),l;Array.prototype.forEach.call(o,(function(t){t.addEventListener("click",(function(){e(n,a)}))})),e(n,a),i.querySelector(".t-checkboxes__ownvariant-wrapper")&&t(n,a),t_variants__applyTranslation(i),i.dispatchEvent(new CustomEvent("inputReady")),i.setAttribute("data-input-ready","true")}}}function a(e){var t="",n=e.querySelector(".t-radio__ownvariant-wrapper"),a=n.querySelector(".t-input-ownanswer"),r=e.querySelector(".t-input-block_rd-flex"),i=a.closest(".t-ownvariant-wrapper_flex").querySelector("span"),o=n.querySelector(".t-radio-ownanswer");o.setAttribute("data-own-variant-title",o.value),o.addEventListener("click",(function(){o.checked&&(a.style.display="flex",a.classList.add("t-input-ownanswer_active"),a.focus(),i&&r&&(i.style.display="none"))}));var l=e.querySelectorAll(".t-radio:not(.t-radio-ownanswer)");Array.prototype.forEach.call(l,(function(e){e.addEventListener("click",(function(){a.classList.remove("t-input-ownanswer_active"),a.value.trim()||(a.style.display="none")}))})),a.addEventListener("input",(function(){t=a.value})),a.addEventListener("focus",(function(){i&&r&&(i.style.display="none")})),a.addEventListener("blur",(function(){var e=o.getAttribute("data-own-variant-title"),n=t.trim();n?o.value=e+": "+n:(o.value=e,a.style.display="none"),i&&r&&(i.style.display="block"),o.dispatchEvent(new Event("change",{bubbles:!0}))})),o.addEventListener("reset",(function(){a.value="",a.style.display="none"}))}function r(e,t){var n=document.getElementById("rec"+e);if(n){var r=n.querySelector('[data-input-lid="'+t+'"]'),i;if(r)r.querySelector(".t-radio__ownvariant-wrapper")&&a(r),r.dispatchEvent(new CustomEvent("inputReady")),r.setAttribute("data-input-ready","true")}}window.t_input_checkboxes_init=n,window.t_input_radiobuttons_init=r}();
