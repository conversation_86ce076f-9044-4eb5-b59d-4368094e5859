window.isMobile=!1;if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)){window.isMobile=!0}
window.isSafari=!1;if(/^((?!chrome|android).)*safari/i.test(navigator.userAgent)){window.isSafari=!0}
window.isSafariVersion='';if(window.isSafari){var version=(navigator.appVersion).match(/Version\/(\d+)\.(\d+)\.?(\d+)? Safari/);if(version!==null){window.isSafariVersion=[parseInt(version[1],10),parseInt(version[2],10),parseInt(version[3]||0,10)]}}
window.isIE=!!document.documentMode;function t_throttle(fn,threshhold,scope){var last;var deferTimer;threshhold||(threshhold=250);return function(){var context=scope||this;var now=+new Date();var args=arguments;if(last&&now<last+threshhold){clearTimeout(deferTimer);deferTimer=setTimeout(function(){last=now;fn.apply(context,args)},threshhold)}else{last=now;fn.apply(context,args)}}}
window.requestAnimationFrame=(function(){return(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(callback){window.setTimeout(callback,1000/60)})})();function t270_scroll(hash,offset){if(!hash)return;t270_checkLoad(hash,offset);var excludeHashes=['#!/tproduct/','#!/tab/','#opencart'];if(excludeHashes.includes(hash)){return!0}
var isHistoryChangeAllowed=window.location.hash!==hash;var wrapperBlock=document.querySelector('.t270');var dontChangeHistory=wrapperBlock?Boolean(wrapperBlock.getAttribute('data-history-disabled')):!1;t270_scrollToEl(hash,offset);if(!dontChangeHistory&&isHistoryChangeAllowed){if(history.pushState){history.pushState(null,null,hash)}else{window.location.hash=hash}
isHistoryChangeAllowed=!1}
return!0}
function t270_checkLoad(hash,offset){if(window.t270_loadChecked)return;var sliderWrappers=document.body.querySelectorAll('.t-slds__items-wrapper');if(!sliderWrappers.length)return;var lastWrapper=sliderWrappers[sliderWrappers.length-1];var sliderImgs=lastWrapper?lastWrapper.querySelectorAll('.t-slds__bgimg'):[];var lastImg=sliderImgs[sliderImgs.length-1];var imageUrl=lastImg?window.getComputedStyle(lastImg).backgroundImage:'';imageUrl=imageUrl.substring(5,imageUrl.length-2);var preloaderImg=document.createElement('img');preloaderImg.src=imageUrl?imageUrl:'';preloaderImg.addEventListener('load',function(){t270_scroll(hash,offset);window.t270_loadChecked=!0})}
function t270_scrollToEl(hash,offset){var SCROLL_DURATION_MS=500;var body=document.body;if(body.getAttribute('data-scroll'))return;var scrollTargetY=t270_getTarget(hash,offset);if(isNaN(scrollTargetY))return;var canSmoothScroll=window.CSS&&window.CSS.supports('scroll-behavior','smooth')&&'scrollBehavior' in document.documentElement.style;var userAgent=navigator.userAgent.toLowerCase();var isAndroid=userAgent.indexOf('android')!==-1;if(window.isMobile&&!isAndroid&&canSmoothScroll){body.setAttribute('data-scroll','true');window.scrollTo({left:0,top:scrollTargetY,behavior:'smooth',});setTimeout(function(){body.removeAttribute('data-scroll')},SCROLL_DURATION_MS)}else{t270_smoothScrollTo(scrollTargetY,SCROLL_DURATION_MS)}}
function t270_smoothScrollTo(targetY,duration=500){var body=document.body;var startY=window.scrollY||window.pageYOffset;var deltaY=targetY-startY;var startTime=performance.now();function easeInOutQuad(t){return Math.pow(t,2)}
function scroll(){var currentTime=performance.now();var elapsedTime=Math.min((currentTime-startTime)/duration,1);var ease=easeInOutQuad(elapsedTime);window.scrollTo(0,startY+deltaY*ease);if(elapsedTime<1){requestAnimationFrame(scroll)}else{body.removeAttribute('data-scroll');body.removeAttribute('data-scrollable');window.scrollTo(0,targetY)}}
body.setAttribute('data-scroll','true');body.setAttribute('data-scrollable','true');requestAnimationFrame(scroll)}
function t270_getTarget(hash,offset){var target;try{if(hash.substring(0,1)==='#'){target=document.getElementById(hash.substring(1))}else{target=document.querySelector(hash)}}catch(event){console.log('Exception t270: '+event.message);return}
if(!target){target=document.querySelector('a[name="'+hash.substr(1)+'"], div[id="'+hash.substr(1)+'"]');if(!target)return}
target=parseInt(target.getBoundingClientRect().top+window.pageYOffset-offset,10);target=Math.max(target,0);return target}
function t890_init(recId,offset){var rec=document.getElementById('rec'+recId);if(!rec)return;var container=rec.querySelector('.t890');if(!container)return;var windowOffset=offset||window.innerHeight;rec.setAttribute('data-animationappear','off');rec.style.opacity=1;window.addEventListener('scroll',t_throttle(function(){if(window.pageYOffset>windowOffset){container.style.display='block'}else{t890__fadeOut(container)}},200));var isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);rec.addEventListener('click',function(e){if(e.target.closest('.t890__arrow')){isSafari?t890__animateScrollPolyfill(0):t890__scrollToTop()}})}
function t890__fadeOut(el){if(el.style.display==='none')return;var opacity=1;var timer=setInterval(function(){el.style.opacity=opacity;opacity-=0.1;if(opacity<=0.1){clearInterval(timer);el.style.display='none';el.style.opacity=null}},20)}
function t890__scrollToTop(){if(window.isIE){window.scrollTo(0,0)}else{window.scrollTo({left:0,top:0,behavior:'smooth',})}}
function t890__animateScrollPolyfill(target){var documentHeight=Math.max(document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight,document.body.clientHeight,document.documentElement.clientHeight);var bottomViewportPoint=documentHeight-document.documentElement.clientHeight;if(target>bottomViewportPoint)target=bottomViewportPoint;if(target===window.pageYOffset)return!1;var currentPosition=window.pageYOffset;var step=(target-currentPosition)/30;var difference=window.pageYOffset;var timerID=setInterval(function(){difference+=step;window.scrollTo(0,difference);document.body.setAttribute('data-scrollable','true');if((target-currentPosition<0&&window.pageYOffset<=target)||(target-currentPosition>0&&window.pageYOffset>=target)){clearInterval(timerID);document.body.removeAttribute('data-scrollable')}},10)}