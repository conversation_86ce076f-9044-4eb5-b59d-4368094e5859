<!DOCTYPE html> <html> 
<!-- Mirrored from multivitshakes.com/aboutus by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 14:08:45 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head> <meta charset="utf-8" /> <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> <meta name="viewport" content="width=device-width, initial-scale=1.0" /> <!--metatextblock--> <title>About Us Multi Shake</title> <meta property="og:url" content="aboutus.html" /> <meta property="og:title" content="About Us Multi Shake" /> <meta property="og:description" content="" /> <meta property="og:type" content="website" /> <link rel="canonical" href="aboutus.html"> <!--/metatextblock--> <meta name="format-detection" content="telephone=no" /> <meta http-equiv="x-dns-prefetch-control" content="on"> <link rel="dns-prefetch" href="https://ws.tildacdn.com/"> <link rel="dns-prefetch" href="https://static.tildacdn.one/"> <link rel="icon" type="image/png" sizes="32x32" href="../static.tildacdn.one/tild6264-6461-4239-b432-623166343462/Frame_32.png" media="(prefers-color-scheme: light)"/> <link rel="icon" type="image/png" sizes="32x32" href="../static.tildacdn.one/tild3536-3436-4163-b762-613933623963/Frame_32.png" media="(prefers-color-scheme: dark)"/> <link rel="icon" type="image/svg+xml" sizes="any" href="https://static.tildacdn.one/tild3232-6434-4466-b763-356263396233/Group_64.svg"> <link rel="apple-touch-icon" type="image/png" href="../static.tildacdn.one/tild3030-3563-4935-a432-323630656431/Frame_32.png"> <link rel="icon" type="image/png" sizes="192x192" href="../static.tildacdn.one/tild3030-3563-4935-a432-323630656431/Frame_32.png"> <!-- Assets --> <script src="../neo.tildacdn.com/js/tilda-fallback-1.0.min.js" async charset="utf-8"></script> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-grid-3.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';"/> <link rel="stylesheet" href="../static.tildacdn.one/ws/project13569789/tilda-blocks-page70808095.min8dec.css?t=1752237705" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-animation-2.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-popup-1.1.min.css" type="text/css" media="print" onload="this.media='all';" onerror="this.loaderr='y';" /> <noscript><link rel="stylesheet" href="../static.tildacdn.one/css/tilda-popup-1.1.min.css" type="text/css" media="all" /></noscript> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-quiz-form-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-forms-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-cart-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <script nomodule src="../static.tildacdn.one/js/tilda-polyfill-1.0.min.js" charset="utf-8"></script> <script type="text/javascript">function t_onReady(func) {if(document.readyState!='loading') {func();} else {document.addEventListener('DOMContentLoaded',func);}}
function t_onFuncLoad(funcName,okFunc,time) {if(typeof window[funcName]==='function') {okFunc();} else {setTimeout(function() {t_onFuncLoad(funcName,okFunc,time);},(time||100));}}function t_throttle(fn,threshhold,scope) {return function() {fn.apply(scope||this,arguments);};}function t396_initialScale(t){t=document.getElementById("rec"+t);if(t){t=t.querySelector(".t396__artboard");if(t){var e,r=document.documentElement.clientWidth,a=[];if(i=t.getAttribute("data-artboard-screens"))for(var i=i.split(","),l=0;l<i.length;l++)a[l]=parseInt(i[l],10);else a=[320,480,640,960,1200];for(l=0;l<a.length;l++){var n=a[l];n<=r&&(e=n)}var o="edit"===window.allrecords.getAttribute("data-tilda-mode"),d="center"===t396_getFieldValue(t,"valign",e,a),c="grid"===t396_getFieldValue(t,"upscale",e,a),s=t396_getFieldValue(t,"height_vh",e,a),u=t396_getFieldValue(t,"height",e,a),g=!!window.opr&&!!window.opr.addons||!!window.opera||-1!==navigator.userAgent.indexOf("OPR/index.html");if(!o&&d&&!c&&!s&&u&&!g){for(var _=parseFloat((r/e).toFixed(3)),f=[t,t.querySelector(".t396__carrier"),t.querySelector(".t396__filter")],l=0;l<f.length;l++)f[l].style.height=Math.floor(parseInt(u,10)*_)+"px";t396_scaleInitial__getElementsToScale(t).forEach(function(t){t.style.zoom=_})}}}}function t396_scaleInitial__getElementsToScale(t){return t?Array.prototype.slice.call(t.children).filter(function(t){return t&&(t.classList.contains("t396__elem")||t.classList.contains("t396__group"))}):[]}function t396_getFieldValue(t,e,r,a){var i=a[a.length-1],l=r===i?t.getAttribute("data-artboard-"+e):t.getAttribute("data-artboard-"+e+"-res-"+r);if(!l)for(var n=0;n<a.length;n++){var o=a[n];if(!(o<=r)&&(l=o===i?t.getAttribute("data-artboard-"+e):t.getAttribute("data-artboard-"+e+"-res-"+o)))break}return l}</script> <script src="../static.tildacdn.one/js/jquery-1.10.2.min.js" charset="utf-8" onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-scripts-3.0.min.js" charset="utf-8" defer onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/ws/project13569789/tilda-blocks-page70808095.min8dec.js?t=1752237705" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-lazyload-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-animation-2.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-zero-1.1.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-popup-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-step-manager-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-quiz-form-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-forms-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-cart-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-widget-positions-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-animation-ext-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-animation-sbs-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-zero-scale-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-events-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script type="text/javascript">window.dataLayer=window.dataLayer||[];</script> <script type="text/javascript">(function() {if((/bot|google|yandex|baidu|bing|msn|duckduckbot|teoma|slurp|crawler|spider|robot|crawling|facebook/i.test(navigator.userAgent))===false&&typeof(sessionStorage)!='undefined'&&sessionStorage.getItem('visited')!=='y'&&document.visibilityState){var style=document.createElement('style');style.type='text/css';style.innerHTML='@media screen and (min-width: 980px) {.t-records {opacity: 0;}.t-records_animated {-webkit-transition: opacity ease-in-out .2s;-moz-transition: opacity ease-in-out .2s;-o-transition: opacity ease-in-out .2s;transition: opacity ease-in-out .2s;}.t-records.t-records_visible {opacity: 1;}}';document.getElementsByTagName('head')[0].appendChild(style);function t_setvisRecs(){var alr=document.querySelectorAll('.t-records');Array.prototype.forEach.call(alr,function(el) {el.classList.add("t-records_animated");});setTimeout(function() {Array.prototype.forEach.call(alr,function(el) {el.classList.add("t-records_visible");});sessionStorage.setItem("visited","y");},400);}
document.addEventListener('DOMContentLoaded',t_setvisRecs);}})();</script></head> <body class="t-body" style="margin:0;"> <!--allrecords--> <div id="allrecords" class="t-records" data-hook="blocks-collection-content-node" data-tilda-project-id="13569789" data-tilda-page-id="70808095" data-tilda-page-alias="aboutus" data-tilda-formskey="2a7fbf90afb3bba69f857fbe13569789" data-tilda-cookie="no" data-tilda-lazy="yes" data-tilda-root-zone="one"> <!--header--> <header id="t-header" class="t-records" data-hook="blocks-collection-content-node" data-tilda-project-id="13569789" data-tilda-page-id="71000039" data-tilda-page-alias="header" data-tilda-formskey="2a7fbf90afb3bba69f857fbe13569789" data-tilda-cookie="no" data-tilda-lazy="yes" data-tilda-root-zone="one"> <div id="rec1109929906" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1109929906 .t396__artboard {height:60px;background-color:#f5f5f5;}#rec1109929906 .t396__filter {height:60px;}#rec1109929906 .t396__carrier{height:60px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}#rec1109929906 .tn-elem[data-elem-id="1749132440124"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 1053px);width:127px;height:40px;}#rec1109929906 .tn-elem[data-elem-id="1749132440124"] .tn-atom{color:#f5f5f5;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] .tn-atom:hover {background-color:#6377d4;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;left:calc(50% - 480px + 813px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:12px;left:calc(50% - 320px + 493px);width:127px;height:40px;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:12px;left:calc(50% - 207px + 227px);}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:-63px;left:calc(50% - 195px + 500px);}}#rec1109929906 .tn-elem[data-elem-id="1749132440127"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 456px);width:76px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440127"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;top:10px;left:calc(50% - 480px + 336px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;top:141px;left:calc(50% - 320px + 731px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132440129"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 543px);width:97px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440129"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;top:10px;left:calc(50% - 480px + 423px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;top:141px;left:calc(50% - 320px + 818px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132440133"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 651px);width:92px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440133"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;top:10px;left:calc(50% - 480px + 531px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;top:141px;left:calc(50% - 320px + 926px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132446265"]{z-index:3;top:11px;left:calc(50% - 600px + 20px);width:106px;height:auto;}#rec1109929906 .tn-elem[data-elem-id="1749132446265"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109929906 .tn-elem[data-elem-id="1749132446265"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}#rec1109929906 .tn-elem[data-elem-id="1749132721781"]{z-index:3;top:-70px;left:calc(50% - 600px + -261px);width:100px;height:100px;}#rec1109929906 .tn-elem[data-elem-id="1749132721781"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;box-shadow:0px 3px 0px 0px rgba(128,145,226,1);}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;top:12px;left:calc(50% - 320px + 443px);width:40px;height:40px;border-radius:10px 10px 10px 10px;}#rec1109929906 .tn-elem[data-elem-id="1749132721781"] .tn-atom {background-size:cover;border-radius:10px 10px 10px 10px;box-shadow:0px 2px 0px 0px rgba(128,145,226,1);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;top:12px;left:calc(50% - 207px + 359px);width:40px;height:40px;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;left:calc(50% - 195px + 335px);}}#rec1109929906 .tn-elem[data-elem-id="1749132882190"]{z-index:3;top:-34px;left:calc(50% - 600px + -243px);width:26px;height:auto;}#rec1109929906 .tn-elem[data-elem-id="1749132882190"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109929906 .tn-elem[data-elem-id="1749132882190"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;top:24px;left:calc(50% - 320px + 450px);height:auto;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;top:24px;left:calc(50% - 207px + 366px);width:26px;height:auto;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;left:calc(50% - 195px + 342px);height:auto;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1109929906" data-artboard-screens="390,414,640,960,1200" data-artboard-height="60" data-artboard-valign="center" data-artboard-upscale="window"> <div class="t396__carrier" data-artboard-recid="1109929906"></div> <div class="t396__filter" data-artboard-recid="1109929906"></div> <div class='t396__elem tn-elem tn-elem__11099299061749132440124' data-elem-id='1749132440124' data-elem-type='button' data-field-top-value="10" data-field-left-value="1053" data-field-height-value="40" data-field-width-value="127" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="-63" data-field-left-res-390-value="500" data-field-top-res-414-value="12" data-field-left-res-414-value="227" data-field-top-res-640-value="12" data-field-left-res-640-value="493" data-field-height-res-640-value="40" data-field-width-res-640-value="127" data-field-container-res-640-value="grid" data-field-left-res-960-value="813"> <a class='tn-atom' href="#popup:quiz">Quiz →</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440127' data-elem-id='1749132440127' data-elem-type='button' data-field-top-value="10" data-field-left-value="456" data-field-height-value="36" data-field-width-value="76" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="731" data-field-top-res-960-value="10" data-field-left-res-960-value="336"> <a class='tn-atom' href="catalog.html">Shop</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440129' data-elem-id='1749132440129' data-elem-type='button' data-field-top-value="10" data-field-left-value="543" data-field-height-value="36" data-field-width-value="97" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="818" data-field-top-res-960-value="10" data-field-left-res-960-value="423"> <a class='tn-atom' href="aboutus.html">About Us</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440133' data-elem-id='1749132440133' data-elem-type='button' data-field-top-value="10" data-field-left-value="651" data-field-height-value="36" data-field-width-value="92" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="926" data-field-top-res-960-value="10" data-field-left-res-960-value="531"> <a class='tn-atom' href="contact.html">Contact</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132446265' data-elem-id='1749132446265' data-elem-type='image' data-field-top-value="11" data-field-left-value="20" data-field-height-value="37" data-field-width-value="106" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="107" data-field-fileheight-value="37" data-field-heightmode-value="hug" data-field-height-res-390-value="35" data-field-left-res-414-value="15" data-field-height-res-414-value="35" data-field-height-res-640-value="35" data-field-height-res-960-value="35"> <a class='tn-atom' href="index.html"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3664-3030-4365-b563-663263663965/photo.svg'
src='https://static.tildacdn.one/tild3664-3030-4365-b563-663263663965/photo.svg'
alt='' imgfield='tn_img_1749132446265'
/> </a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132721781' data-elem-id='1749132721781' data-elem-type='shape' data-field-top-value="-70" data-field-left-value="-261" data-field-height-value="100" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="335" data-field-top-res-414-value="12" data-field-left-res-414-value="359" data-field-height-res-414-value="40" data-field-width-res-414-value="40" data-field-container-res-414-value="grid" data-field-top-res-640-value="12" data-field-left-res-640-value="443" data-field-height-res-640-value="40" data-field-width-res-640-value="40" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed"> <a class='tn-atom' href="#menu"> </a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132882190' data-elem-id='1749132882190' data-elem-type='image' data-field-top-value="-34" data-field-left-value="-243" data-field-height-value="15" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="26" data-field-fileheight-value="15" data-field-heightmode-value="hug" data-field-left-res-390-value="342" data-field-height-res-390-value="15" data-field-top-res-414-value="24" data-field-left-res-414-value="366" data-field-height-res-414-value="15" data-field-width-res-414-value="26" data-field-container-res-414-value="grid" data-field-top-res-640-value="24" data-field-left-res-640-value="450" data-field-height-res-640-value="15" data-field-height-res-960-value="15"> <a class='tn-atom' href="#menu"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3064-3832-4733-b035-663732393333/Group_184.svg'
src='https://static.tildacdn.one/tild3064-3832-4733-b035-663732393333/Group_184.svg'
alt='' imgfield='tn_img_1749132882190'
/> </a> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1109929906');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1109929906');});});</script> <!-- /T396 --> </div> <div id="rec1126836916" class="r t-rec t-screenmax-640px" style=" " data-animationappear="off" data-record-type="396" data-screen-max="640px"> <!-- T396 --> <style>#rec1126836916 .t396__artboard {height:648px;background-color:#f5f5f5;}#rec1126836916 .t396__filter {height:648px;}#rec1126836916 .t396__carrier{height:648px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:700px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:600px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:600px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}#rec1126836916 .tn-elem[data-elem-id="1749134179388"]{z-index:3;top:5px;left:calc(50% - 600px + 20px);width:1159px;height:546px;}#rec1126836916 .tn-elem[data-elem-id="1749134179388"] .tn-atom{border-width:1px;border-radius:38px 38px 38px 38px;background-color:#f8f4ed;background-position:center center;border-color:#eae6df ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;top:5px;left:calc(50% - 480px + 20px);width:920px;height:546px;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;top:10px;left:calc(50% - 320px + 15px);width:610px;height:640px;}#rec1126836916 .tn-elem[data-elem-id="1749134179388"] .tn-atom{background-color:#e2efd9;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;width:384px;height:575px;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;left:calc(50% - 195px + 10px);width:370px;}}#rec1126836916 .tn-elem[data-elem-id="1750859045018"]{z-index:3;top:11px;left:calc(50% - 600px + 20px);width:106px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859045018"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859045018"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;top:41px;left:calc(50% - 320px + 48px);width:121px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;left:calc(50% - 207px + 34px);width:158px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;top:34px;width:134px;height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1749134179407"]{color:#1a1919;text-align:LEFT;z-index:3;top:304px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:319px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:445px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:455px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859017698"]{color:#1a1919;text-align:LEFT;z-index:3;top:213px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:278px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:404px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:414px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859016682"]{color:#1a1919;text-align:LEFT;z-index:3;top:93px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:237px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:363px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:373px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750858994538"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 1053px);width:127px;height:40px;}#rec1126836916 .tn-elem[data-elem-id="1750858994538"] .tn-atom{color:#f5f5f5;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] .tn-atom:hover {background-color:#6377d4;background-image:none;}}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;left:calc(50% - 480px + 813px);}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:387px;left:calc(50% - 320px + 257px);width:127px;height:40px;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:117px;left:calc(50% - 207px + 34px);}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:-63px;left:calc(50% - 195px + 500px);}}#rec1126836916 .tn-elem[data-elem-id="1750859089207"]{z-index:3;top:419px;left:calc(50% - 600px + 465px);width:32px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089207"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089207"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:302px;left:calc(50% - 480px + 52px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:579px;left:calc(50% - 320px + 223px);width:38px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:524px;left:calc(50% - 207px + 40px);width:37px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:526px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089203"]{z-index:3;top:416px;left:calc(50% - 600px + 545px);width:36px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089203"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089203"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:299px;left:calc(50% - 480px + 132px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:576px;left:calc(50% - 320px + 318px);width:42px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:523px;left:calc(50% - 207px + 127px);width:37px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:523px;left:calc(50% - 195px + 122px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089195"]{z-index:3;top:418px;left:calc(50% - 600px + 506px);width:29px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089195"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089195"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:301px;left:calc(50% - 480px + 93px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:578px;left:calc(50% - 320px + 271px);width:34px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:525px;left:calc(50% - 207px + 87px);width:30px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:525px;left:calc(50% - 195px + 82px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089214"]{z-index:3;top:416px;left:calc(50% - 600px + 593px);width:37px;height:37px;}#rec1126836916 .tn-elem[data-elem-id="1750859089214"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:299px;left:calc(50% - 480px + 180px);}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:576px;left:calc(50% - 320px + 374px);width:43px;height:44px;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:523px;left:calc(50% - 207px + 174px);width:38px;height:40px;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:523px;left:calc(50% - 195px + 169px);}}#rec1126836916 .tn-elem[data-elem-id="1750859182344"]{z-index:3;top:0px;left:calc(50% - 600px + 0px);width:23px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859182344"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859182344"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;top:40px;left:calc(50% - 320px + 572px);height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;left:calc(50% - 207px + 346px);height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;left:calc(50% - 195px + 327px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859349346"]{z-index:3;top:-7px;left:calc(50% - 600px + 0px);width:100px;height:100px;}#rec1126836916 .tn-elem[data-elem-id="1750859349346"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;top:10px;left:calc(50% - 320px + 525px);}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;left:calc(50% - 207px + 299px);}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;left:calc(50% - 195px + 280px);}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1126836916" data-artboard-screens="390,414,640,960,1200" data-artboard-height="648" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-height-res-390="600" data-artboard-height-res-414="600" data-artboard-height-res-640="700"> <div class="t396__carrier" data-artboard-recid="1126836916"></div> <div class="t396__filter" data-artboard-recid="1126836916"></div> <div class='t396__elem tn-elem tn-elem__11268369161749134179388' data-elem-id='1749134179388' data-elem-type='shape' data-field-top-value="5" data-field-left-value="20" data-field-height-value="546" data-field-width-value="1159" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="10" data-field-width-res-390-value="370" data-field-widthmode-res-390-value="fixed" data-field-height-res-414-value="575" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="10" data-field-left-res-640-value="15" data-field-height-res-640-value="640" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-field-top-res-960-value="5" data-field-left-res-960-value="20" data-field-height-res-960-value="546" data-field-width-res-960-value="920" data-field-container-res-960-value="grid"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859045018' data-elem-id='1750859045018' data-elem-type='image' data-field-top-value="11" data-field-left-value="20" data-field-height-value="37" data-field-width-value="106" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="107" data-field-fileheight-value="37" data-field-heightmode-value="hug" data-field-top-res-390-value="34" data-field-height-res-390-value="44" data-field-width-res-390-value="134" data-field-left-res-414-value="34" data-field-height-res-414-value="52" data-field-width-res-414-value="158" data-field-top-res-640-value="41" data-field-left-res-640-value="48" data-field-height-res-640-value="42" data-field-width-res-640-value="121" data-field-height-res-960-value="35"> <a class='tn-atom' href="index.html"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3838-6263-4763-a636-643763323538/photo.svg'
src='https://static.tildacdn.one/tild3838-6263-4763-a636-643763323538/photo.svg'
alt='' imgfield='tn_img_1750859045018'
/> </a> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161749134179407 t-animate' data-elem-id='1749134179407' data-elem-type='text' data-field-top-value="304" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="455" data-field-left-res-390-value="34" data-field-top-res-414-value="445" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="319" data-field-left-res-640-value="83" data-field-height-res-640-value="76" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="contact.html"style="color: inherit">Contact</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859017698 t-animate' data-elem-id='1750859017698' data-elem-type='text' data-field-top-value="213" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="414" data-field-left-res-390-value="34" data-field-top-res-414-value="404" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="278" data-field-left-res-640-value="83" data-field-height-res-640-value="38" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="aboutus.html"style="color: inherit">About Us</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859016682 t-animate' data-elem-id='1750859016682' data-elem-type='text' data-field-top-value="93" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="373" data-field-left-res-390-value="34" data-field-top-res-414-value="363" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="237" data-field-left-res-640-value="83" data-field-height-res-640-value="38" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="catalog.html"style="color: inherit">Shop</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750858994538' data-elem-id='1750858994538' data-elem-type='button' data-field-top-value="10" data-field-left-value="1053" data-field-height-value="40" data-field-width-value="127" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="-63" data-field-left-res-390-value="500" data-field-top-res-414-value="117" data-field-left-res-414-value="34" data-field-top-res-640-value="387" data-field-left-res-640-value="257" data-field-height-res-640-value="40" data-field-width-res-640-value="127" data-field-container-res-640-value="grid" data-field-left-res-960-value="813"> <a class='tn-atom' href="#popup:quiz">Quiz →</a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089207' data-elem-id='1750859089207' data-elem-type='image' data-field-top-value="419" data-field-left-value="465" data-field-height-value="32" data-field-width-value="32" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="97" data-field-fileheight-value="96" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="526" data-field-left-res-390-value="34" data-field-height-res-390-value="37" data-field-top-res-414-value="524" data-field-left-res-414-value="40" data-field-height-res-414-value="37" data-field-width-res-414-value="37" data-field-top-res-640-value="579" data-field-left-res-640-value="223" data-field-height-res-640-value="38" data-field-width-res-640-value="38" data-field-top-res-960-value="302" data-field-left-res-960-value="52" data-field-height-res-960-value="32"> <a class='tn-atom' href="https://www.instagram.com/multivitshakes"> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild3535-3365-4635-a339-643534376531/skill-icons_instagra.png'
src='../thb.tildacdn.one/tild3535-3365-4635-a339-643534376531/-/resize/20x/skill-icons_instagra.png'
alt='' imgfield='tn_img_1750859089207'
/> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089203' data-elem-id='1750859089203' data-elem-type='vector' data-field-top-value="416" data-field-left-value="545" data-field-height-value="36" data-field-width-value="36" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="523" data-field-left-res-390-value="122" data-field-top-res-414-value="523" data-field-left-res-414-value="127" data-field-width-res-414-value="37" data-field-top-res-640-value="576" data-field-left-res-640-value="318" data-field-height-res-640-value="36" data-field-width-res-640-value="42" data-field-top-res-960-value="299" data-field-left-res-960-value="132"> <a class='tn-atom tn-atom__vector' href="https://www.facebook.com/people/Mulit-Vit-Shakes/pfbid034fSusqv8gRRzPwEAcadzPPAKZEPgYwfcz6JzaSi83xF56ytLjfNYUNKCss4nkzqxl/"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36.167598724365234 36.167598724365234" fill="none"> <path d="M36.167598724365234 18.083849248525688C36.167598724365234 8.096478650436401 28.071294676129572 0 18.083849248525688 0C8.096478650436401 0 0 8.096478650436401 0 18.083849248525688C0 27.109984845207478 6.613033409792144 34.591439715128935 15.258261833977535 35.94809881486564V23.311214650087027H10.666647988475932V18.083849248525688H15.258261833977535V14.099751288907282C15.258261833977535 9.567502191656704 17.958110720822564 7.064005951127677 22.08887452906115 7.064005951127677C24.0673668951416 7.064005951127677 26.13695172366438 7.417201260049754 26.13695172366438 7.417201260049754V11.867536981982527H23.856572152507386C21.610065408293625 11.867536981982527 20.90946160624537 13.261536009505699 20.90946160624537 14.69172757892477V18.083849248525688H25.924934765625L25.123261232520793 23.311214650087027H20.90946160624537V35.94809881486564C29.554665087259227 34.591439715128935 36.167598724365234 27.109984845207478 36.167598724365234 18.083849248525688Z" fill="#1877F2"></path> <path d="M25.123510664236132 23.311214650087027L25.92518419734034 18.083849248525688H20.909835753818378V14.691702635753236C20.909835753818378 13.261386350476496 21.610439555866638 11.867512038810993 23.8569463000804 11.867512038810993H26.137201155379724V7.41717631687822C26.137201155379724 7.41717631687822 24.067741042714612 7.0639810079561425 22.08909901760496 7.0639810079561425C17.958484868395576 7.0639810079561425 15.258635981550546 9.56747724848517 15.258635981550546 14.099751288907282V18.083849248525688H10.667022136048942V23.311214650087027H15.258635981550546V35.94809881486564C16.19338133478888 36.09451523177048 17.138103956640837 36.167848156080574 18.084223396098697 36.167598724365234C19.03036777872809 36.167848156080574 19.975090400580047 36.09451523177048 20.909835753818378 35.94809881486564V23.311214650087027H25.123510664236132Z" fill="white"></path> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089195' data-elem-id='1750859089195' data-elem-type='vector' data-field-top-value="418" data-field-left-value="506" data-field-height-value="33" data-field-width-value="29" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="525" data-field-left-res-390-value="82" data-field-top-res-414-value="525" data-field-left-res-414-value="87" data-field-width-res-414-value="30" data-field-top-res-640-value="578" data-field-left-res-640-value="271" data-field-height-res-640-value="33" data-field-width-res-640-value="34" data-field-top-res-960-value="301" data-field-left-res-960-value="93"> <a class='tn-atom tn-atom__vector' href="https://www.tiktok.com/@multivitshakes"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.131103515625 33" fill="none"> <g clip-path="url(#clip0_773_1045)"> <path d="M21.501244232438566 11.83427426862146C23.61802972610343 13.346676508663862 26.211270601712737 14.23654457673945 29.01208926365518 14.23654457673945V8.849954552909654C28.482002773187098 8.85015373994224 27.95316120167268 8.7948544400207 27.434776949368988 8.684952994791667V12.924972662301348C24.634157474459133 12.924972662301348 22.041265176156852 12.035229086121127 19.924006613289595 10.522926439595018V21.515535902026574C19.924006613289595 27.01474129440438 15.463785681256676 31.47229809987647 9.962264739625066 31.47229809987647C7.909492978557358 31.47229809987647 6.00148039342114 30.85183049337273 4.416549175138555 29.78817173936632C6.225516008321647 31.63687638555021 8.748269572774772 32.783695725661055 11.539153781467013 32.783695725661055C17.04112289392194 32.783695725661055 21.501468317850225 28.326138920188967 21.501468317850225 22.826784137536723V11.83427426862146H21.501244232438566ZM23.447127252145098 6.399854458592081C22.36526778303619 5.218600660223023 21.65491702807826 3.692056140867054 21.501244232438566 2.0043319645808295V1.311542036383213H20.006519841538125C20.38278414609208 3.4565423732138085 21.666245790556555 5.28911286975828 23.447127252145098 6.399854458592081ZM7.896247040890424 25.568394454043137C7.291789092130743 24.77637701572516 6.965047663553352 23.807332102196845 6.9665913630558896 22.8110234635834C6.9665913630558896 20.29598839664296 9.006664950796273 18.256736455411993 11.523617192925348 18.256736455411993C11.992603061147838 18.256462573242185 12.458825209293202 18.32844378714276 12.905900503931289 18.470140462448253V12.96306718228332C12.383457815838675 12.891534139206064 11.856234638964008 12.861058523220487 11.329260445880076 12.872287692182493V17.15866814152644C10.881961065830328 17.01694656784188 10.41548993389423 16.945015150699454 9.946279980260082 16.94538862638555C7.429427331647302 16.94538862638555 5.389478235802284 18.98441648220486 5.389478235802284 21.499800126452325C5.389478235802284 23.278415835545207 6.4091166556073045 24.81823119094718 7.896247040890424 25.568394454043137Z" fill="#FF004F"></path> <path d="M19.923682934361644 10.522826846078726C22.041041090745193 12.03510459422576 24.633709303635815 12.924873068785056 27.434278981787525 12.924873068785056V8.684828502896302C25.871158743573048 8.35198697144598 24.48718234279013 7.535544223257212 23.44667908132178 6.399854458592081C21.665698026216944 5.288988377862914 20.38246046716413 3.456442779697516 20.006196162610177 1.311542036383213H16.080095258455195V22.826560052125068C16.071131841988848 25.33484765833667 14.034568925697783 27.36555945554554 11.523069428585737 27.36555945554554C10.043184471612914 27.36555945554554 8.728325971137153 26.660686343983706 7.895699276550815 25.568145470252404C6.408792976679353 24.81823119094718 5.389054963358039 23.27829134364984 5.389054963358039 21.499899719968617C5.389054963358039 18.984765059511883 7.4290040592030575 16.945488219901844 9.945831809436767 16.945488219901844C10.428063615326188 16.945488219901844 10.89284165748531 17.020507036049345 11.328812275056757 17.158767735042733V12.872387285698784C5.923847247470953 12.984031617462941 1.5770583508196447 17.398016259556957 1.5770583508196447 22.82665964564136C1.5770583508196447 25.53652452882946 2.659470563943977 27.993247591980502 4.416325089726897 29.78842072315705C6.0012563080094825 30.85183049337273 7.909169299629407 31.472547083667198 9.962040654213409 31.472547083667198C15.463686087740385 31.472547083667198 19.92380742625701 27.014492310613647 19.92380742625701 21.515535902026574L19.923682934361644 10.522826846078726Z" fill="black"></path> <path d="M27.43452796557826 8.684529722347422V7.538283044955262C26.024781742454593 7.540349610418336 24.642921703892895 7.1457600988665195 23.44685336997529 6.399655271559495C24.50558224492521 7.558151951455662 25.899791879507212 8.357041342397835 27.43452796557826 8.684753807759082M20.006245959368325 1.3112332964827056C19.970392293502936 1.1062847789797008 19.942879584627068 0.8999668506276709 19.92373273111979 0.6927899281976162V0H14.502683350652712V21.515237121477696C14.494068511493388 24.0232508455195 12.457530493581396 26.05416182976095 9.945881606194913 26.05416182976095C9.233862659839074 26.055157764923877 8.531578979700853 25.88883659271501 7.895749073308961 25.568394454043137C8.7283757678953 26.660437360192976 10.04323426837106 27.365310471754807 11.523119225343883 27.365310471754807C14.034519128939635 27.365310471754807 16.07128123226329 25.334598674545937 16.08012015683427 22.82648535698785V1.3113478290264422L20.006245959368325 1.3112332964827056ZM11.32921064912193 12.872088505149907V11.651619759740585C10.876209540264423 11.589772186122795 10.419523471304085 11.558798602555756 9.962314536383213 11.55892309445112C4.460121338516626 11.55892309445112 0 16.016679086955794 0 21.515237121477696C0 24.962616891192575 1.7528832343207463 28.000717105702456 4.4165989718967005 29.78792275557559C2.659744446113782 27.99299860818977 1.577334722827357 25.536275545038727 1.577334722827357 22.826360865092482C1.577334722827357 17.397817072524372 5.9240215361244655 12.98370793853499 11.32921064912193 12.872088505149907Z" fill="#00F2EA"></path> </g> <defs> <clipPath id="clip0_773_1045"> <rect width="7.253146807809161" height="8.21646509415064" fill="white" transform="scale(4)"></rect> </clipPath> </defs> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089214' data-elem-id='1750859089214' data-elem-type='shape' data-field-top-value="416" data-field-left-value="593" data-field-height-value="37" data-field-width-value="37" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="523" data-field-left-res-390-value="169" data-field-top-res-414-value="523" data-field-left-res-414-value="174" data-field-height-res-414-value="40" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="576" data-field-left-res-640-value="374" data-field-height-res-640-value="44" data-field-width-res-640-value="43" data-field-top-res-960-value="299" data-field-left-res-960-value="180"> <a class='tn-atom t-bgimg' href="https://www.linkedin.com/company/mulit-vit-shakes" data-original="https://static.tildacdn.one/tild6337-3534-4563-b837-386261326333/skill-icons_linkedin.svg"
aria-label='' role="img"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859182344' data-elem-id='1750859182344' data-elem-type='image' data-field-top-value="0" data-field-left-value="0" data-field-height-value="23" data-field-width-value="23" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="23" data-field-fileheight-value="23" data-field-heightmode-value="hug" data-field-left-res-390-value="327" data-field-height-res-390-value="23" data-field-left-res-414-value="346" data-field-height-res-414-value="23" data-field-top-res-640-value="40" data-field-left-res-640-value="572" data-field-height-res-640-value="23" data-field-height-res-960-value="23"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3739-3162-4831-b965-303463626164/Group_182.svg'
src='https://static.tildacdn.one/tild3739-3162-4831-b965-303463626164/Group_182.svg'
alt='' imgfield='tn_img_1750859182344'
/> </div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859349346' data-elem-id='1750859349346' data-elem-type='shape' data-field-top-value="-7" data-field-left-value="0" data-field-height-value="100" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="280" data-field-left-res-414-value="299" data-field-top-res-640-value="10" data-field-left-res-640-value="525"> <div class='tn-atom'> </div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1126836916');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1126836916');});});</script> <!-- /T396 --> </div> <div id="rec**********" class="r t-rec" style=" " data-record-type="1040"> <!-- t1040 --> <div class="t1040 t-quiz t-quiz-test popup fixed-height without-panel "> <div
class="t-popup" data-tooltip-hook="#popup:quiz"
role="dialog"
aria-modal="true"
tabindex="-1"> <div class="t-popup__close t-popup__block-close t-popup__btn-close-popup"> <button
type="button"
class="t-popup__close-wrapper t-popup__block-close-button"
aria-label="Закрыть диалоговое окно"> <svg role="presentation" class="t-popup__close-icon" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#a)"> <path fill-rule="evenodd" clip-rule="evenodd" d="m12.4 0 1.378 1.392-5.442 5.496L14 12.608 12.622 14 6.958 8.28l-5.58 5.636L0 12.524l5.58-5.636L.222 1.476 1.6.084l5.358 5.412L12.4 0Z" fill="#fff"/> </g> <defs> <clipPath id="a"> <path fill="#fff" d="M0 0h14v14H0z"/> </clipPath> </defs> </svg> </button> </div> <div class="t-popup__container t-width t-width_10"> <div
class="t-quiz__quiz t-animate" data-animate-style="fadeinright" data-animate-group="yes"> <form
id="form**********" name='form**********' role="form" action='#' method='POST' data-formactiontype="2" data-inputbox=".t-input-group" 
class="t-form js-form-proccess t-form_inputs-total_20 " data-success-callback="t_quiz__onSuccess"> <input type="hidden" name="formservices[]" value="02f4ebbdbe15d673818532cbd05f0ede" class="js-formaction-services"> <input type="hidden" name="formservices[]" value="42beafd0361231338f85499263a54b7a" class="js-formaction-services"> <div
class="t-quiz__quiz-wrapper
t-quiz__quiz-wrapper_fixed-height t-quiz__quiz-published without-panel t-quiz__inputs-in-cols " data-quiz-height="500px" data-auto-step-change="true"> <div class="t-quiz__content-padding-container"> <div class="t-quiz__content-wrapper"> <div class="t-quiz__progress-bar-container"> <div class="t-quiz__progressbar" style="background-color:rgba(106,144,80,0.20)"> <div class="t-quiz__progress" style="background-color:#6a9050"></div> </div> </div> <div class="t-quiz__main"> <div class="t-form__inputsbox t-quiz__quiz-form-wrapper t-quiz__quiz-form-wrapper_withcheckbox t-quiz__quiz-form-wrapper_newcapturecondition"> <div
class="t-quiz__cover t-quiz__cover-test t-quiz__screen-wrapper t-step-form__step" data-step-index="0"> <div class="t-quiz__cover__side-container leftside"> <div class="t-quiz__cover__content-container justify-center align-start"> <div class="t-quiz__cover__text-wrapper align-start"> <div
class="t-quiz__cover__title t-title t-title_xxs"
field="li_cover_title__4211261180710">
If you don’t know what vitamins &amp; minerals you need, just take our quick quiz and we will guide you
</div> </div> <div class="t-quiz__cover__btns-container"> <button
type="button"
class="t-btn t-quiz__btn_start t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;color: #f6f6f6;background: #8191e2;border-radius: 30;"> <span>Start</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> </div> </div> <div class="t-quiz__cover__side-cover"> <div class="t-quiz__cover__container" style="height: 500px;"> <div class="t-quiz__cover__img t-bgimg" data-original="../static.tildacdn.one/tild3937-3533-4236-b834-613062383739/Group_188.png"
bgimgfield="li_cover_img__4211261180710"
style="
background-image:url('../thb.tildacdn.one/tild3937-3533-4236-b834-613062383739/-/resizeb/20x/Group_188.png');
background-size: cover;
background-repeat: no-repeat;
background-position: left center;
"
itemscope itemtype="http://schema.org/ImageObject"> <meta itemprop="image" content="../static.tildacdn.one/tild3937-3533-4236-b834-613062383739/Group_188.png"> </div> </div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="1"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180711"
role="radiogroup" aria-labelledby="field-title_4211261180711" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="What is your gender?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180711" data-redactor-toolbar="no"
field="li_title__4211261180711"
style="">What is your gender?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Male"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Male</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Female"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Female</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Prefer not to mention"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Prefer not to mention</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180711');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180711"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="2"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180712"
role="radiogroup" aria-labelledby="field-title_4211261180712" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="What is your age group?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180712" data-redactor-toolbar="no"
field="li_title__4211261180712"
style="">What is your age group?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="Under 20"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Under 20</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="20-29"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>20-29</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="30-39"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>30-39</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="40-49"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>40-49</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="50-59"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>50-59</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="60+"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>60+</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180712');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180712"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="3"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180713"
role="radiogroup" aria-labelledby="field-title_4211261180713" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you eating as healthy as you want? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180713" data-redactor-toolbar="no"
field="li_title__4211261180713"
style="">Are you eating as healthy as you want? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you eating as healthy as you want? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you eating as healthy as you want? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180713');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180713"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="4"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180714"
role="radiogroup" aria-labelledby="field-title_4211261180714" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180714" data-redactor-toolbar="no"
field="li_title__4211261180714"
style="">Are you too busy to buy fruits &amp; veggies regularly to get enough natural whole food nutrients that your body needs? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180714');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180714"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="5"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180715"
role="radiogroup" aria-labelledby="field-title_4211261180715" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180715" data-redactor-toolbar="no"
field="li_title__4211261180715"
style="">Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="Answer"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Answer</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180715');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180715"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="6"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180716"
role="radiogroup" aria-labelledby="field-title_4211261180716" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180716" data-redactor-toolbar="no"
field="li_title__4211261180716"
style="">It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="1 month (start to see improvement)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>1 month (start to see improvement)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="3 months (good improvement)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>3 months (good improvement)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="6 months (see major improvements)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>6 months (see major improvements)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="12 months (see long lasting improvements) "
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>12 months (see long lasting improvements) </span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180716');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180716"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="7"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180717"
role="radiogroup" aria-labelledby="field-title_4211261180717" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do u want overall better health & longevity? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180717" data-redactor-toolbar="no"
field="li_title__4211261180717"
style="">Do u want overall better health &amp; longevity? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do u want overall better health & longevity? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do u want overall better health & longevity? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180717');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180717"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="8"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180718"
role="radiogroup" aria-labelledby="field-title_4211261180718" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you need help with Weight Management & Metabolism? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180718" data-redactor-toolbar="no"
field="li_title__4211261180718"
style="">Do you need help with Weight Management &amp; Metabolism? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need help with Weight Management & Metabolism? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need help with Weight Management & Metabolism? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180718');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180718"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="9"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180719"
role="radiogroup" aria-labelledby="field-title_4211261180719" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to improve Energy & Endurance?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180719" data-redactor-toolbar="no"
field="li_title__4211261180719"
style="">Do you want to improve Energy &amp; Endurance?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to improve Energy & Endurance?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to improve Energy & Endurance?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180719');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180719"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="10"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807110"
role="radiogroup" aria-labelledby="field-title_2112611807110" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you facing issues with your Gut & Digestive Health? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807110" data-redactor-toolbar="no"
field="li_title__2112611807110"
style="">Are you facing issues with your Gut &amp; Digestive Health? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you facing issues with your Gut & Digestive Health? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you facing issues with your Gut & Digestive Health? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807110');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807110"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="11"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807111"
role="radiogroup" aria-labelledby="field-title_2112611807111" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you need an antioxidant Boost?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807111" data-redactor-toolbar="no"
field="li_title__2112611807111"
style="">Do you need an antioxidant Boost?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need an antioxidant Boost?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need an antioxidant Boost?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807111');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807111"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="12"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807112"
role="radiogroup" aria-labelledby="field-title_2112611807112" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you getting sick quickly and looking to improve your immunity? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807112" data-redactor-toolbar="no"
field="li_title__2112611807112"
style="">Are you getting sick quickly and looking to improve your immunity? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you getting sick quickly and looking to improve your immunity? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you getting sick quickly and looking to improve your immunity? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807112');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807112"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="13"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807113"
role="radiogroup" aria-labelledby="field-title_2112611807113" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you bones and joints in pain?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807113" data-redactor-toolbar="no"
field="li_title__2112611807113"
style="">Are you bones and joints in pain?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you bones and joints in pain?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you bones and joints in pain?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807113');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807113"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="14"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807115"
role="radiogroup" aria-labelledby="field-title_2112611807115" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you have trouble focusing and need Mental Clarity?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807115" data-redactor-toolbar="no"
field="li_title__2112611807115"
style="">Do you have trouble focusing and need Mental Clarity?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you have trouble focusing and need Mental Clarity?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you have trouble focusing and need Mental Clarity?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807115');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807115"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="15"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807116"
role="radiogroup" aria-labelledby="field-title_2112611807116" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807116" data-redactor-toolbar="no"
field="li_title__2112611807116"
style="">Is your hair falling and weak, is your skin looking dull &amp; your nails braking easily?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807116');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807116"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="16"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807117"
role="radiogroup" aria-labelledby="field-title_2112611807117" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you having trouble sleeping and relaxing?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807117" data-redactor-toolbar="no"
field="li_title__2112611807117"
style="">Are you having trouble sleeping and relaxing?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you having trouble sleeping and relaxing?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you having trouble sleeping and relaxing?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807117');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807117"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="17"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807118"
role="radiogroup" aria-labelledby="field-title_2112611807118" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to do a Detox & Liver Cleanse?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807118" data-redactor-toolbar="no"
field="li_title__2112611807118"
style="">Do you want to do a Detox &amp; Liver Cleanse?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to do a Detox & Liver Cleanse?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to do a Detox & Liver Cleanse?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807118');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807118"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="18"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807119"
role="radiogroup" aria-labelledby="field-title_2112611807119" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to live a longer and healthier life?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807119" data-redactor-toolbar="no"
field="li_title__2112611807119"
style="">Do you want to live a longer and healthier life?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to live a longer and healthier life?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to live a longer and healthier life?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807119');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807119"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="19"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="1751560815522"
role="radiogroup" aria-labelledby="field-title_1751560815522" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you suffer from Stress & Anxiety?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_1751560815522" data-redactor-toolbar="no"
field="li_title__1751560815522"
style="">Do you suffer from Stress &amp; Anxiety?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you suffer from Stress & Anxiety?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you suffer from Stress & Anxiety?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','1751560815522');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_1751560815522"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="20"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="1751560834966"
role="radiogroup" aria-labelledby="field-title_1751560834966" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to look younger, need Collagen & Skin Elasticity support? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_1751560834966" data-redactor-toolbar="no"
field="li_title__1751560834966"
style="">Do you want to look younger, need Collagen &amp; Skin Elasticity support? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to look younger, need Collagen & Skin Elasticity support? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to look younger, need Collagen & Skin Elasticity support? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','1751560834966');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_1751560834966"></div> </div> </div> </div> <div
class="t-quiz__contact-form t-quiz__screen-wrapper t-step-form__step" data-submit-title="Submit" data-step-index="21"> <div class="t-quiz__contact-form__layout row t-input-group_two-cols"> <div class="t-quiz__contact-form__header"> <div class="t-quiz__contact-form__header__text-wrapper"> <div
class="t-quiz__contact-form__header__title t-heading t-heading_xs"
field="li_contacts_title__2112611807120" data-redactor-nohref='yes'>
Leave your details and we’ll contact you soon
</div> </div> </div> <div class="t-quiz__contact-form__main"> <div class="t-quiz__inputs-wrapper"> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_nm t-input-group_one-col " data-input-lid="2112611807121" data-field-type="nm" data-field-name="Name"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <label for='input_2112611807121'
class="t-input-title t-descr t-descr_xs"
id="field-title_2112611807121" data-redactor-toolbar="no"
field="li_title__2112611807121"
style="">Your name</label> </div> </div> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="text"
autocomplete="name"
name="Name"
id="input_2112611807121"
class="t-input js-tilda-rule"
value=""
placeholder="Sarah" data-tilda-req="1" aria-required="true" data-tilda-rule="name"
aria-describedby="error_2112611807121"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807121"></div> </div> </div> </div> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_em t-input-group_one-col " data-input-lid="2112611807122" data-field-type="em" data-field-name="Email"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <label for='input_2112611807122'
class="t-input-title t-descr t-descr_xs"
id="field-title_2112611807122" data-redactor-toolbar="no"
field="li_title__2112611807122"
style="">Your Email</label> </div> </div> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="email"
autocomplete="email"
name="Email"
id="input_2112611807122"
class="t-input js-tilda-rule"
value=""
placeholder="<EMAIL>" data-tilda-req="1" aria-required="true" data-tilda-rule="email"
aria-describedby="error_2112611807122"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807122"></div> </div> </div> </div> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_ph t-input-group_one-col " data-input-lid="1751560280387" data-field-async="true" data-field-type="ph" data-field-name="Phone"> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="tel"
autocomplete="tel"
name="Phone"
id="input_1751560280387" data-phonemask-init="no" data-phonemask-id="**********" data-phonemask-lid="1751560280387"
class="t-input js-phonemask-input js-tilda-rule"
value=""
placeholder="+1(999)999-9999" data-tilda-req="1" aria-required="true" aria-describedby="error_1751560280387"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-phone-mask-1.1.min.js',function() {t_onFuncLoad('t_form_phonemask_load',function() {var phoneMasks=document.querySelectorAll('#rec********** [data-phonemask-lid="1751560280387"]');t_form_phonemask_load(phoneMasks);});})})});</script> </div> <div class="t-input-error" aria-live="polite" id="error_1751560280387"></div> </div> </div> </div> </div> </div> </div> </div> <div class="t-quiz__result t-quiz__result_ordinary t-quiz-hidden"> <div class="t-quiz__successbox-wrapper"> <div class="js-successbox t-form__successbox t-text t-text_md"
aria-live="polite"
style="display:none;" data-success-message="Thank you for submitting your information, We advise you to take the following products to improve all your health"></div> </div> </div> <div class="t-form__errorbox-middle"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> <div class="t-form__errorbox-bottom"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> </div> <div
class="t-quiz__btn-wrapper t-quiz__btn-wrapper_mobile"> <button
type="button"
class="t-btn t-quiz__btn_prev t-quiz__btn_sm"
style="color:#7579ed;border:1px solid #dddfff;border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <svg class="t-btn__icon t-btn__icon_arrow left" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="button"
class="t-btn t-quiz__btn_next t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Next</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="submit"
class="t-btn t-quiz__btn_submit t-quiz__btn_md t-submit t-quiz-hidden"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Go to the result</span> </button> </div> <div class="t-quiz__footer-sticky"> <div class="t-quiz__footer"> <div class="t-quiz__footer__text-container"> <div class="t-quiz__counter-container t-descr t-descr_xxs"> <span class="t-quiz__counter-title">Шаг:&nbsp;</span><span class="t-quiz__counter"></span> </div> </div> <div
class="t-quiz__btn-wrapper"> <button
type="button"
class="t-btn t-quiz__btn_prev t-quiz__btn_sm"
style="color:#7579ed;border:1px solid #dddfff;border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <svg class="t-btn__icon t-btn__icon_arrow left" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="button"
class="t-btn t-quiz__btn_next t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Next</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="submit"
class="t-btn t-quiz__btn_submit t-quiz__btn_md t-submit t-quiz-hidden"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Go to the result</span> </button> </div> </div> </div> </div> </div> </div> </div> </form> <style>#rec********** input::-webkit-input-placeholder {color:#1a1919;opacity:0.5;}#rec********** input::-moz-placeholder{color:#1a1919;opacity:0.5;}#rec********** input:-moz-placeholder {color:#1a1919;opacity:0.5;}#rec********** input:-ms-input-placeholder{color:#1a1919;opacity:0.5;}#rec********** textarea::-webkit-input-placeholder {color:#1a1919;opacity:0.5;}#rec********** textarea::-moz-placeholder{color:#1a1919;opacity:0.5;}#rec********** textarea:-moz-placeholder {color:#1a1919;opacity:0.5;}#rec********** textarea:-ms-input-placeholder{color:#1a1919;opacity:0.5;}</style> </div> </div> </div> </div> <style>.t-form__screen-hiderecord{opacity:0 !important;}</style> <script>t_onReady(function() {var rec=document.querySelector('#rec**********');rec&&rec.classList.add('t-form__screen-hiderecord');function showRecord() {rec&&rec.classList.remove('t-form__screen-hiderecord');}
var opacityTimeout=setTimeout(showRecord,1500);t_onFuncLoad('t_quiz__step_manager',function() {t_onFuncLoad('t_quiz__init',function() {t_quiz__init('**********');showRecord();clearTimeout(opacityTimeout);});})});</script> <style> #rec********** .t-quiz__cover__title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__cover__title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__cover__title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__cover__title{font-size:30px;line-height:0.85;}}#rec********** .t-quiz__contact-form__header__title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__contact-form__header__title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__contact-form__header__title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__contact-form__header__title{font-size:30px;line-height:0.85;}}#rec********** .t-quiz__result-title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__result-title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__result-title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__result-title{font-size:30px;line-height:0.85;}}</style> <style> #rec********** .t-quiz__contact-form__header__descr{color:#6377d4;}#rec********** .t-quiz__contact-form__footer__text{color:#6377d4;}#rec********** .t-rate__text{color:#6377d4;}#rec********** .t-range__interval-txt{color:#6377d4;}#rec********** .t-contact-method__type-label{color:#6377d4;}</style> <style> #rec********** .t-quiz__cover__title{color:#6377d4;font-weight:900;font-family:'GillSans';padding-bottom:40px;}@media screen and (min-width:900px){#rec********** .t-quiz__cover__title{font-size:40px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__cover__title{font-size:35px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__cover__title{font-size:35px;line-height:0.8;}}</style> <style> #rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:30px;line-height:0.85;}}</style> <style> #rec********** .t-step-form__step.t-quiz__contact-form .t-input-title{color:#6377d4;font-family:'GillSans';}</style> <style> @media screen and (min-width:900px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:35px;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:30px;}}</style> <style> #rec********** .t-radio__control{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}#rec********** .t-checkbox__control{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}#rec********** .t-input-ownanswer{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}</style> <style>#rec********** .t1040 .t-quiz__quiz {box-shadow:0px 30px 40px -10px rgba(0,0,0,0.1);}</style> <style>#rec********** .t-quiz{--quiz-background-color:#f6f6f6;--consultant-msg-bubble-bg:rgba(0,0,0,0.05);--panel-background-color:#f4f4f4;--btn-wrapper-background-color:rgba(246,246,246,0.95);--content-padding:0px;--border-size:1px;--border-radius:25px;--outer-border-radius:25px;--inner-border-radius:max(0px,var(--border-radius) - var(--border-size));--content-padding-radius:calc(var(--border-radius) - var(--content-padding));--btn-close-popup-icon-color:#101010;--btn-close-popup-icon-color-mob:#000000;--btn-close-popup-bg-color:rgba(255,255,255,0.7);--btn-close-popup-bg-color-mob:var(--btn-close-popup-bg-color);--secondary-text-font-size-mob:clamp(14px,var(--page-font-size) - 4px,16px);}#rec********** .t-quiz:has(.t-quiz__cover.t-step-form__step_active,.t-quiz__result.t-step-form__step_active){--content-padding:0px;}#rec********** .t-quiz.fullscreen{--outer-border-radius:0 !important;--inner-border-radius:0 !important;}#rec********** .t-quiz:not(.popup.fullscreen) .t-quiz__quiz,#rec********** .t-quiz.popup.fullscreen .t-quiz__quiz-wrapper{border-style:solid;border-width:1px;border-color:#e6e6e6;}#rec********** .t-quiz__quiz-wrapper{height:500px;}#rec********** .t-step-form__step.t-quiz__contact-form .t-input-title{font-size:clamp(14px,1em * 0.55,24px);}.t-quiz__btn-wrapper_mobile{display:none;}@media screen and (max-width:640px){#rec********** .t-quiz{--inner-border-radius:0 !important;--outer-border-radius:0 !important;--content-padding-radius:0 !important;--content-padding:0px;--prev-btn-border-radius:4px;}}</style> </div> <div id="rec1126402946" class="r t-rec" style=" " data-record-type="270"> <div class="t270"></div> <script>t_onReady(function() {var hash=window.location.hash;t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3);});setTimeout(function() {var curPath=window.location.pathname;var curFullPath=window.location.origin + curPath;var recs=document.querySelectorAll('.r');Array.prototype.forEach.call(recs,function(rec) {var selects='a[href^="#"]:not([href="#"]):not(.carousel-control):not(.t-carousel__control):not([href^="#price"]):not([href^="#submenu"]):not([href^="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([href^="#prodpopup"]):not([href^="#order"]):not([href^="#!"]):not([target="_blank"]),' +
'a[href^="' + curPath + '#"]:not([href*="#!/tfeeds/"]):not([href*="#!/tproduct/"]):not([href*="#!/tab/"]):not([href*="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([target="_blank"]),' +
'a[href^="' + curFullPath + '#"]:not([href*="#!/tfeeds/"]):not([href*="#!/tproduct/"]):not([href*="#!/tab/"]):not([href*="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([target="_blank"])';var elements=rec.querySelectorAll(selects);Array.prototype.forEach.call(elements,function(element) {element.addEventListener('click',function(event) {event.preventDefault();var hash=this.hash.trim();t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3);});});});});if(document.querySelectorAll('.js-store').length>0||document.querySelectorAll('.js-feed').length>0) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3,1);});}},500);setTimeout(function() {var hash=window.location.hash;if(hash&&document.querySelectorAll('a[name="' + hash.slice(1) + '"], div[id="' + hash.slice(1) + '"]').length>0) {if(window.isMobile) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});} else {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});}}},1000);window.addEventListener('popstate',function() {var hash=window.location.hash;if(hash&&document.querySelectorAll('a[name="' + hash.slice(1) + '"], div[id="' + hash.slice(1) + '"]').length>0) {if(window.isMobile) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});} else {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});}}});});</script> </div> <div id="rec1126862896" class="r t-rec" style=" " data-animationappear="off" data-record-type="131"> <!-- T123 --> <div class="t123"> <div class="t-container_100 "> <div class="t-width t-width_100 "> <!-- nominify begin --> <!--NOLIM--><!--NLM082--><!--settings{"comment":"","blockId":"#rec1126836916","openLink":"#menu","closeClass":"nolimClose082","animationSide":"0","t396overflow":"auto","darkBackground":"1","backgroundColor":"","opacity":"100","blockWidth":"300","widthPxOrPercent":"0","blocksZindex":"","addMobileMenuSize960":{"blockSize960":"","widthPxOrPercent960":"0"},"addMobileMenuSize640":{"blockSize640":"","widthPxOrPercent640":"0"},"addMobileMenuSize480":{"blockSize480":"","widthPxOrPercent480":"0"},"addMobileMenuSize320":{"blockSize320":"","widthPxOrPercent320":"0"},"scrollbarOff":"0","plusScrollbarWidth":"0","animEveryOpen":"1","notBlockScroll":"0"}settingsend--><!--ts1750859404595ts--> <script> (function() { if (typeof window.nlm082blocks == 'undefined') { window.nlm082blocks = []; window.nlm082blocks.push('#rec1126836916'); } else { window.nlm082blocks.push('#rec1126836916'); } if (typeof window.nlm082openLinks == 'undefined') { window.nlm082openLinks = []; window.nlm082openLinks.push('#menu'); } else { window.nlm082openLinks.push('#menu'); } window.nlm082open = false; function t_ready(e) { "loading" != document.readyState ? e() : document.addEventListener ? document.addEventListener("DOMContentLoaded", e) : document.attachEvent("onreadystatechange", (function() { "loading" != document.readyState && e() } )) } t_ready((function() { setTimeout(function() { window.t_animate__removeInlineAnimStyles = null; let blk = document.querySelector("#rec1126836916"); let isMac; if (window.navigator.userAgent.toLowerCase().indexOf('mac') !== -1) { isMac = true; } else { isMac = false; } let into = setInterval(function() { var c = document.querySelectorAll("[href='#menu']"); if (c.length > 0) { clearInterval(into); var menuId = "rec1126836916"; let menuBlock = document.querySelector("#rec1126836916 .t396"); let menuBlockArt = document.querySelector("#rec1126836916 .t396__artboard"); menuBlock.style.display = "none"; setTimeout(function() { menuBlock.style.display = "block"; }, 0); var scrollWidth = 0; function removeAnimation(blk) { let block = document.querySelector(blk); let elemList = block.querySelectorAll(".t396__elem"); elemList.forEach(function(el) { if (el.hasAttribute('data-animate-sbs-event') && el.getAttribute('data-animate-sbs-event') != "hover" && el.getAttribute('data-animate-sbs-event') != "click" && el.getAttribute('data-animate-sbs-event') != "scroll") { el.classList.remove('t-sbs-anim_started'); } if (el.classList.contains('t-sbs-anim_reversed')) { el.classList.remove('t-sbs-anim_reversed'); el.classList.remove('t-sbs-anim_started'); } if (el.classList.contains('t-sbs-anim_playing')) { el.classList.remove('t-sbs-anim_playing'); } if (el.hasAttribute('data-animate-style')) { el.classList.remove('t-animate_started'); } }); } function addAnimation(blk) { let block = document.querySelector(blk); let elemList = block.querySelectorAll(".t396__elem"); elemList.forEach(function(el) { if (el.hasAttribute('data-animate-sbs-event') && el.getAttribute('data-animate-sbs-event') != "hover" && el.getAttribute('data-animate-sbs-event') != "click" && el.getAttribute('data-animate-sbs-event') != "scroll") { el.classList.add('t-sbs-anim_started'); } if (el.hasAttribute('data-animate-style')) { el.classList.add('t-animate_started'); } }); } let isIos = function() { var agent = window.navigator.userAgent; var start = agent.indexOf( 'OS ' ); if( ( agent.indexOf( 'iPhone' ) > -1 || agent.indexOf( 'iPad' ) > -1 ) && start > -1 ){ return true; } return false; }; let isAndroid = function() { var agent = window.navigator.userAgent; return agent.toLowerCase().indexOf("android") > -1; }; var scrollTop; function iosLockScroll() { if (isIos()) { scrollTop = window.pageYOffset || document.documentElement.scrollTop; document.body.classList.add('locked'); document.body.style.top = -(scrollTop) + 'px'; } } function iosUnlockScroll(x = "nolink") { if (isIos()) { if (document.body.classList.contains('locked')) { document.body.classList.remove('locked'); window.scrollTo(0, scrollTop); if (x && x != "nolink" && !window.nlm082openLinks.includes(x)) { setTimeout(function() { document.querySelector(`a[href="${x}"]`).click(); }, 500); } } } } function androidScrollFix(x) { if (isAndroid()) { setTimeout(function() { if (document.querySelector(`${x}`)) { document.querySelector(`${x}`).scrollIntoView({ behavior: 'auto', block: 'start' }); } }, 500); } } blk.querySelectorAll('[href]').forEach(function(item) { item.addEventListener("click", function(e) { iosUnlockScroll(item.getAttribute("href")); androidScrollFix(item.getAttribute("href")); }); }); var isAnimOnce = false; menuBlock.style.transform = 'translateX(100%)'; menuBlock.style.overflow = "hidden"; window.addEventListener("click", function(event) { let clickId = event.target.closest(".tn-elem"); if (document.querySelector(".t-body.nolimPopUp") && document.querySelector(".nolimShow1126836916") && !clickId && !event.target.hasAttribute("nlm082") && !event.target.classList.contains("t-slds__arrow")) { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; iosUnlockScroll(); } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } }); window.addEventListener("click", function(event) { if (document.querySelector(".t-body.nolimPopUp") && event.target.hasAttribute("nlm082") && event.target.getAttribute("nlm082") != "1126836916") { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } }); c.forEach((function(item) { item.setAttribute("nlm082", "1126836916"); item.addEventListener("click", (function(e) { e.preventDefault(); if (document.querySelector(".nolimShow1126836916")) { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; iosUnlockScroll(); } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } else { removeAnimation("#rec1126836916"); blk.style.display = "block"; setTimeout(function() { isMenuOpen = true; window.nlm082open = true; menuBlock.style.opacity = "1"; menuBlock.style.pointerEvents = "auto"; menuBlock.style.transform = `translateX(-${scrollWidth}px)`; menuBlock.style.marginRight = `-${scrollWidth}px`; document.querySelector("html").style.overflow = "hidden"; iosLockScroll(); setTimeout(function() { menuBlock.classList.add("nolimShow1126836916"); document.querySelector(".t-body").classList.add("nolimPopUp"); if (!isAnimOnce) { addAnimation("#rec1126836916"); } isAnimOnce = false; }, 400); setTimeout(function() { "y" === window.lazy && t_lazyload_update(); typeof t_slds_updateSlider != "undefined" && t_slds_updateSlider("1126836916"); if (document.querySelector("#rec1126836916") && document.querySelector("#rec1126836916").getAttribute("data-record-type") == "396") { t396_doResize('1126836916'); } }, 300); }, 0); } })); })); document.querySelectorAll(".nolimClose082").forEach((function(item) { item.classList.add("nolim_popup_close"); })); const isYaBrowser=navigator.userAgent.includes("YaBrowser"); menuBlock.querySelectorAll(".nolimClose082, .nolim_popup_close").forEach((function(item) { item.addEventListener("click", (function() { const href='#menu'; const selector='[name='+href.substr(1)+']'; const tildaAnchorLink=document.querySelector(selector); if(isYaBrowser && tildaAnchorLink){ tildaAnchorLink.setAttribute('id', href.substr(1)); const newLink=document.createElement("a"); newLink.setAttribute('href', href); menuBlock.appendChild(newLink); newLink.click(); newLink.remove(); } isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; 							console.log('blk',blk); blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; if (!item.querySelector(".tn-atom[href]") && !item.querySelector(".tn-atom a[href]")) { iosUnlockScroll(); } } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); })); })); } }); },500); })); }()); </script> <style> body.locked { overflow-y: scroll; width: 100%; } #rec1126836916 { display: none; z-index: 999999; } #rec1126836916 .t396__filter, #rec1126836916 .t396__carrier { pointer-events: none!important; height: 0!important; min-height: 0!important; } #rec1126836916 .t396__artboard { min-height: 0px!important; height: 0px!important; overflow: visible !important; position: relative !important; } .nolimClose082 { cursor: pointer; } #rec1126836916 .t396 { position: fixed; top: 0; right: 0; left: 0; bottom: 0; z-index: 999999!important; transition: transform ease-in-out 0.4s; opacity: 0; pointer-events: none; } .nolimPopUp { height: 100vh; min-height: 100vh; overflow: visible !important; } .nolimShow1126836916 { overflow-y: auto !important; } </style> <!-- nominify end --> </div> </div> </div> </div> <div id="rec1127385721" class="r t-rec" style=" " data-animationappear="off" data-record-type="890"> <!-- t890 --> <div class="t890" style="display: none; opacity:1; position:fixed; z-index:99990; bottom:20px;right:20px;"> <button type="button"
class="t890__arrow"
aria-label="Вернуться к началу страницы"
style="box-shadow:0px 0px 10px rgba(0,0,0,0.2);"> <svg role="presentation" width="50" height="50" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="50" height="50" rx="50" fill="#8191e2" fill-opacity="0.90" stroke="none" /> <path d="M14 28L25 18l10 10" stroke="#f6f6f6" stroke-width="1" fill="none"/> </svg> </button> </div> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t890_init',function(){t890_init('1127385721','700');});});</script> <style>@media screen and (min-width:981px){#rec1127385721 .t890__arrow:hover svg path{stroke:#f6f6f6;stroke-width:1;}#rec1127385721 .t890__arrow:focus-visible svg path{stroke:#f6f6f6;stroke-width:1;}#rec1127385721 .t890__arrow:hover svg rect{fill:#6377d4;fill-opacity:1;}#rec1127385721 .t890__arrow:focus-visible svg rect{fill:#6377d4;fill-opacity:1;}}#rec1127385721 .t890__arrow{border-radius:53px;}</style> </div> <div id="rec1136479131" class="r t-rec" style=" " data-animationappear="off" data-record-type="706"> <!--tcart--> <!-- @classes: t-text t-text_xs t-name t-name_xs t-name_md t-btn t-btn_sm --> <script>t_onReady(function() {setTimeout(function() {t_onFuncLoad('tcart__init',function() {tcart__init('1136479131');});},50);var userAgent=navigator.userAgent.toLowerCase();var body=document.body;if(!body) return;if(userAgent.indexOf('instagram')!==-1&&userAgent.indexOf('iphone')!==-1) {body.style.position='relative';}
var rec=document.querySelector('#rec1136479131');if(!rec) return;var cartWindow=rec.querySelector('.t706__cartwin,.t706__cartpage');var allRecords=document.querySelector('.t-records');var currentMode=allRecords.getAttribute('data-tilda-mode');if(cartWindow&&currentMode!=='edit'&&currentMode!=='preview') {cartWindow.addEventListener('scroll',t_throttle(function() {if(window.lazy==='y'||document.querySelector('#allrecords').getAttribute('data-tilda-lazy')==='yes') {t_onFuncLoad('t_lazyload_update',function() {t_lazyload_update();});}},500));}});</script> <div class="t706" data-opencart-onorder="yes" data-project-currency="AED" data-project-currency-side="r" data-project-currency-sep="." data-project-currency-dec="00" data-project-currency-code="AED" data-payment-system="stripe"> <div class="t706__carticon" style="top:initial;bottom:80px;right:20px;"> <div class="t706__carticon-text t-name t-name_xs"></div> <div class="t706__carticon-wrapper"> <div class="t706__carticon-imgwrap" style="background-color:#8191e2;"> <svg role="img" style="stroke:#f6f6f6;" class="t706__carticon-img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64"> <path fill="none" stroke-width="2" stroke-miterlimit="10" d="M44 18h10v45H10V18h10z"/> <path fill="none" stroke-width="2" stroke-miterlimit="10" d="M22 24V11c0-5.523 4.477-10 10-10s10 4.477 10 10v13"/> </svg> </div> <div class="t706__carticon-counter js-carticon-counter" style="background-color:#f5e39c;color:#6377d4;"></div> </div> </div> <div class="t706__cartwin" style="display: none;"> <div class="t706__close t706__cartwin-close"> <button type="button" class="t706__close-button t706__cartwin-close-wrapper" aria-label="Закрыть корзину"> <svg role="presentation" class="t706__close-icon t706__cartwin-close-icon" width="23px" height="23px" viewBox="0 0 23 23" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"> <g stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd"> <rect transform="translate(11.313708, 11.313708) rotate(-45.000000) translate(-11.313708, -11.313708) " x="10.3137085" y="-3.6862915" width="2" height="30"></rect> <rect transform="translate(11.313708, 11.313708) rotate(-315.000000) translate(-11.313708, -11.313708) " x="10.3137085" y="-3.6862915" width="2" height="30"></rect> </g> </svg> </button> </div> <div class="t706__cartwin-content"> <div class="t706__cartwin-top"> <div class="t706__cartwin-heading t-name t-name_xl"></div> </div> <div class="t706__cartwin-products"></div> <div class="t706__cartwin-bottom"> <div class="t706__cartwin-prodamount-wrap t-descr t-descr_sm"> <span class="t706__cartwin-prodamount-label"></span> <span class="t706__cartwin-prodamount"></span> </div> </div> <div class="t706__orderform t-input_nomargin"> <form
id="form1136479131" name='form1136479131' role="form" action='#' method='POST' data-formactiontype="2" data-inputbox=".t-input-group" 
class="t-form js-form-proccess t-form_inputs-total_3 t-form_bbonly" data-formsended-callback="t706_onSuccessCallback"> <input type="hidden" name="formservices[]" value="2516383d1e8f37b6028e03c607bef9ee" class="js-formaction-services"> <input type="hidden" name="formservices[]" value="42beafd0361231338f85499263a54b7a" class="js-formaction-services"> <input type="hidden" name="tildaspec-formname" tabindex="-1" value="Cart"> <!-- @classes t-title t-text t-btn --> <div class="js-successbox t-form__successbox t-text t-text_md"
aria-live="polite"
style="display:none;color:#8191e2;background-color:#f5e39c;" data-success-message="Payment Successful!&lt;br /&gt;&lt;br /&gt;Thank you for your order — it&#039;s been received and is now being processed. We&#039;ll be in touch soon with the next steps!"></div> <div class="t-form__inputsbox t-form__inputsbox_inrow"> <div
class=" t-input-group t-input-group_nm " data-input-lid="3311158466360" data-field-type="nm" data-field-name="Name"> <label
for='input_3311158466360'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466360" data-redactor-toolbar="no"
field="li_title__3311158466360"
style="color:;font-weight:900;font-family: 'GillSans';">Your Name</label> <div class="t-input-block "> <input
type="text"
autocomplete="name"
name="Name"
id="input_3311158466360"
class="t-input js-tilda-rule t-input_bbonly"
value="" data-tilda-req="1" aria-required="true" data-tilda-rule="name"
aria-describedby="error_3311158466360"
style="color:#000000;border:1px solid #000000;"> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466360"></div> </div> <div
class=" t-input-group t-input-group_em " data-input-lid="3311158466361" data-field-type="em" data-field-name="Email"> <label
for='input_3311158466361'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466361" data-redactor-toolbar="no"
field="li_title__3311158466361"
style="color:;font-weight:900;font-family: 'GillSans';">Your Email</label> <div class="t-input-block "> <input
type="email"
autocomplete="email"
name="Email"
id="input_3311158466361"
class="t-input js-tilda-rule t-input_bbonly"
value="" data-tilda-req="1" aria-required="true" data-tilda-rule="email"
aria-describedby="error_3311158466361"
style="color:#000000;border:1px solid #000000;"> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466361"></div> </div> <div
class=" t-input-group t-input-group_ph " data-input-lid="3311158466362" data-field-async="true" data-field-type="ph" data-field-name="Phone"> <label
for='input_3311158466362'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466362" data-redactor-toolbar="no"
field="li_title__3311158466362"
style="color:;font-weight:900;font-family: 'GillSans';">Phone</label> <div class="t-input-block "> <input
type="tel"
autocomplete="tel"
name="Phone"
id="input_3311158466362" data-phonemask-init="no" data-phonemask-id="1136479131" data-phonemask-lid="3311158466362" data-phonemask-maskcountry="AE" class="t-input js-phonemask-input js-tilda-rule t-input_bbonly"
value=""
placeholder="+1(999)999-9999" data-tilda-req="1" aria-required="true" aria-describedby="error_3311158466362"
style="color:#000000;border:1px solid #000000;"> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-phone-mask-1.1.min.js',function() {t_onFuncLoad('t_form_phonemask_load',function() {var phoneMasks=document.querySelectorAll('#rec1136479131 [data-phonemask-lid="3311158466362"]');t_form_phonemask_load(phoneMasks);});})})});</script> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466362"></div> </div> <div class="t-form__errorbox-middle"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> <div class="t-form__submit"> <button
type="submit"
class="t-submit"
style="color:#ffffff;background-color:#8191e2;border-radius:40px; -moz-border-radius:40px; -webkit-border-radius:40px;font-family:GillSans;font-weight:400;" data-field="buttontitle" data-buttonfieldset="button">
Order Now </button> </div> </div> <div class="t-form__errorbox-bottom"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> </form> <style> #rec1136479131 .t-form__successbox,#rec1136479131 .t-form__errorbox-wrapper{border-radius:15px;}</style> <style>#rec1136479131 input::-webkit-input-placeholder {color:#000000;opacity:0.5;}#rec1136479131 input::-moz-placeholder{color:#000000;opacity:0.5;}#rec1136479131 input:-moz-placeholder {color:#000000;opacity:0.5;}#rec1136479131 input:-ms-input-placeholder{color:#000000;opacity:0.5;}#rec1136479131 textarea::-webkit-input-placeholder {color:#000000;opacity:0.5;}#rec1136479131 textarea::-moz-placeholder{color:#000000;opacity:0.5;}#rec1136479131 textarea:-moz-placeholder {color:#000000;opacity:0.5;}#rec1136479131 textarea:-ms-input-placeholder{color:#000000;opacity:0.5;}</style> </div> <div class="t706__form-bottom-text t-text t-text_xs">Our manager will follow up with you shortly</div> </div> </div> <div class="t706__cartdata"> </div> </div> <style></style> <style>@media (hover:hover),(min-width:0\0) {#rec1136479131 .t-submit:hover {background-color:#6377d4 !important;}#rec1136479131 .t-submit:focus-visible {background-color:#6377d4 !important;}}</style> <style>.t-menuwidgeticons__cart .t-menuwidgeticons__icon-counter{background-color:#f5e39c;}</style> <style>.t-menuwidgeticons__cart .t-menuwidgeticons__icon-counter{color:#6377d4 !important;}</style> <style> #rec1136479131 .t706__cartwin-content{border-radius:30px;}</style> <!--/tcart--> </div> </header> <!--/header--> <div id="rec1083751191" class="r t-rec" style=" " data-animationappear="off" data-record-type="121"> </div> <div id="rec1117140011" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1117140011 .t396__artboard {height:799px;background-color:#f5f5f5;overflow:visible;}#rec1117140011 .t396__filter {height:799px;}#rec1117140011 .t396__carrier{height:799px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1117140011 .t396__artboard,#rec1117140011 .t396__filter,#rec1117140011 .t396__carrier {}#rec1117140011 .t396__filter {}#rec1117140011 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1117140011 .t396__artboard,#rec1117140011 .t396__filter,#rec1117140011 .t396__carrier {height:868px;}#rec1117140011 .t396__filter {}#rec1117140011 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1117140011 .t396__artboard,#rec1117140011 .t396__filter,#rec1117140011 .t396__carrier {height:1110px;}#rec1117140011 .t396__filter {}#rec1117140011 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1117140011 .t396__artboard,#rec1117140011 .t396__filter,#rec1117140011 .t396__carrier {height:1170px;}#rec1117140011 .t396__filter {}#rec1117140011 .t396__carrier {background-attachment:scroll;}}#rec1117140011 .tn-elem[data-elem-id="1750439321837"]{color:#1a1919;text-align:LEFT;z-index:3;top:695px;left:calc(50% - 600px + 569px);width:309px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321837"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321837"] {display:table;top:702px;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321837"] {display:table;top:735px;left:calc(50% - 320px + 247px);height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321837"] {display:table;top:1056px;left:calc(50% - 207px + 15px);width:386px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321837"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321837"] {display:table;top:1082px;left:calc(50% - 195px + 108px);width:237px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321832"]{color:#1a1919;text-align:LEFT;z-index:3;top:493px;left:calc(50% - 600px + 569px);width:133px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321832"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321832"] {display:table;top:500px;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321832"] {display:table;top:564px;left:calc(50% - 320px + 248px);height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321832"] {display:table;top:901px;left:calc(50% - 207px + 15px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321832"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321832"] {display:table;top:941px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321830"]{color:#1a1919;text-align:LEFT;z-index:3;top:493px;left:calc(50% - 600px + 802px);width:auto;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321830"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321830"] {display:table;top:500px;left:calc(50% - 480px + 670px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321830"] {display:table;top:564px;left:calc(50% - 320px + 446px);height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321830"] {display:table;top:901px;left:calc(50% - 207px + 213px);width:98px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321830"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321830"] {display:table;top:941px;left:calc(50% - 195px + 201px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321826"]{color:#1a1919;text-align:LEFT;z-index:3;top:448px;left:calc(50% - 600px + 569px);width:177px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321826"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321826"] {display:table;top:455px;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321826"] {display:table;top:524px;left:calc(50% - 320px + 248px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321826"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321826"] {display:table;top:868px;left:calc(50% - 207px + 15px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321826"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321826"] {display:table;top:908px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321824"]{color:#1a1919;text-align:LEFT;z-index:3;top:448px;left:calc(50% - 600px + 802px);width:204px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321824"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321824"] {display:table;top:455px;left:calc(50% - 480px + 670px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321824"] {display:table;top:524px;left:calc(50% - 320px + 446px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321824"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321824"] {display:table;top:868px;left:calc(50% - 207px + 213px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321824"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321824"] {display:table;top:908px;left:calc(50% - 195px + 201px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321822"]{color:#1a1919;text-align:LEFT;z-index:3;top:608px;left:calc(50% - 600px + 569px);width:auto;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321822"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321822"] {display:table;top:615px;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321822"] {display:table;top:669px;left:calc(50% - 320px + 248px);height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321822"] {display:table;top:986px;left:calc(50% - 207px + 15px);width:131px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321822"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321822"] {display:table;top:1026px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321820"]{color:#1a1919;text-align:LEFT;z-index:3;top:608px;left:calc(50% - 600px + 802px);width:auto;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321820"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321820"] {display:table;top:615px;left:calc(50% - 480px + 670px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321820"] {display:table;top:669px;left:calc(50% - 320px + 446px);height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321820"] {display:table;top:986px;left:calc(50% - 207px + 213px);width:115px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321820"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321820"] {display:table;top:1026px;left:calc(50% - 195px + 201px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321818"]{color:#1a1919;text-align:LEFT;z-index:3;top:563px;left:calc(50% - 600px + 569px);width:83px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321818"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321818"] {display:table;top:570px;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321818"] {display:table;top:629px;left:calc(50% - 320px + 248px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321818"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321818"] {display:table;top:953px;left:calc(50% - 207px + 15px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321818"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321818"] {display:table;top:993px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321815"]{color:#1a1919;text-align:LEFT;z-index:3;top:563px;left:calc(50% - 600px + 802px);width:116px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321815"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321815"] {display:table;top:570px;left:calc(50% - 480px + 670px);height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321815"] {display:table;top:629px;left:calc(50% - 320px + 446px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321815"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321815"] {display:table;top:953px;left:calc(50% - 207px + 213px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321815"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321815"] {display:table;top:993px;left:calc(50% - 195px + 201px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321808"]{color:#1a1919;text-align:LEFT;z-index:3;top:328px;left:calc(50% - 600px + 901px);width:250px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321808"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321808"] {display:table;top:335px;left:calc(50% - 480px + 670px);width:269px;height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321808"] {display:table;top:445px;left:calc(50% - 320px + 247px);width:358px;height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321808"] {display:table;top:771px;left:calc(50% - 207px + 136px);width:262px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321808"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321808"] {display:table;top:807px;left:calc(50% - 195px + 108px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750439321805"]{color:#1a1919;text-align:LEFT;z-index:3;top:328px;left:calc(50% - 600px + 569px);width:305px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321805"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750439321805"] {display:table;top:335px;left:calc(50% - 480px + 395px);width:247px;height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750439321805"] {display:table;top:375px;left:calc(50% - 320px + 247px);width:301px;height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750439321805"] {display:table;top:708px;left:calc(50% - 207px + 136px);width:267px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750439321805"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750439321805"] {display:table;top:744px;left:calc(50% - 195px + 108px);height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750502038890"]{color:#8091e2;z-index:3;top:87px;left:calc(50% - 600px + 567px);width:616px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502038890"] .tn-atom {vertical-align:middle;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.8;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750502038890"] {display:table;left:calc(50% - 480px + 395px);height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502038890"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750502038890"] {display:table;left:calc(50% - 320px + 248px);width:373px;height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750502038890"] {display:table;top:337px;left:calc(50% - 207px + 136px);width:263px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502038890"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750502038890"] {display:table;top:409px;left:calc(50% - 195px + 15px);width:291px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750502187292"]{z-index:3;top:487px;left:calc(50% - 600px + 1017px);width:295px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502187292"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117140011 .tn-elem[data-elem-id="1750502187292"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750502187292"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750502187292"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750502187292"] {display:table;height:auto;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750502187292"] {display:table;top:537px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750502522170"]{color:#8091e2;z-index:3;top:123px;left:calc(50% - 600px + 363px);width:828px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502522170"] .tn-atom {vertical-align:middle;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.8;font-weight:900;letter-spacing:-0.5px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750502522170"] {display:table;top:118px;left:calc(50% - 480px + 296px);width:649px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502522170"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750502522170"] {display:table;top:151px;left:calc(50% - 320px + 15px);width:605px;height:auto;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750502522170"] {display:table;top:421px;left:calc(50% - 207px + 15px);width:384px;height:auto;}#rec1117140011 .tn-elem[data-elem-id="1750502522170"] .tn-atom {vertical-align:middle;white-space:normal;font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750502522170"] {display:table;top:465px;width:367px;height:auto;}}#rec1117140011 .tn-elem[data-elem-id="1750510676151"]{z-index:3;top:698px;left:calc(50% - 600px + -27px);width:267px;height:267px;}#rec1117140011 .tn-elem[data-elem-id="1750510676151"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676151"] {display:table;top:643px;left:calc(50% - 480px + -90px);width:251px;height:251px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676151"] {display:table;top:-52px;left:calc(50% - 320px + -17px);width:203px;height:203px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676151"] {display:table;top:184px;left:calc(50% - 207px + 303px);width:158px;height:158px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676151"] {display:table;top:170px;left:calc(50% - 195px + 287px);width:152px;height:152px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676155"]{z-index:3;top:315px;left:calc(50% - 600px + 40px);width:207px;height:207px;}#rec1117140011 .tn-elem[data-elem-id="1750510676155"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676155"] {display:table;top:310px;left:calc(50% - 480px + 39px);width:194px;height:194px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676155"] {display:table;top:628px;left:calc(50% - 320px + 49px);width:161px;height:162px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676155"] {display:table;top:182px;left:calc(50% - 207px + 55px);width:127px;height:127px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676155"] {display:table;top:222px;left:calc(50% - 195px + 50px);width:122px;height:122px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676158"]{z-index:3;top:600px;left:calc(50% - 600px + 185px);width:185px;height:186px;}#rec1117140011 .tn-elem[data-elem-id="1750510676158"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676158"] {display:table;top:577px;left:calc(50% - 480px + 174px);width:174px;height:174px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676158"] {display:table;top:0px;left:calc(50% - 320px + -52px);width:151px;height:151px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676158"] {display:table;top:224px;left:calc(50% - 207px + 276px);width:117px;height:118px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676158"] {display:table;top:249px;left:calc(50% - 195px + 262px);width:112px;height:113px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676161"]{z-index:3;top:357px;left:calc(50% - 600px + 268px);width:196px;height:211px;}#rec1117140011 .tn-elem[data-elem-id="1750510676161"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676161"] {display:table;top:378px;left:calc(50% - 480px + 116px);width:184px;height:198px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676161"] {display:table;top:669px;left:calc(50% - 320px + -581px);width:153px;height:165px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676161"] {display:table;top:171px;left:calc(50% - 207px + -7px);width:120px;height:130px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676161"] {display:table;top:1052px;left:calc(50% - 195px + -52px);width:167px;height:182px;}#rec1117140011 .tn-elem[data-elem-id="1750510676161"] .tn-atom {background-size:cover;-webkit-transform:rotate(28deg);-moz-transform:rotate(28deg);transform:rotate(28deg);}}#rec1117140011 .tn-elem[data-elem-id="1750510676163"]{z-index:3;top:356px;left:calc(50% - 600px + -81px);width:314px;height:314px;}#rec1117140011 .tn-elem[data-elem-id="1750510676163"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676163"] {display:table;top:348px;left:calc(50% - 480px + -75px);width:294px;height:294px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676163"] {display:table;top:668px;left:calc(50% - 320px + -34px);width:244px;height:245px;}#rec1117140011 .tn-elem[data-elem-id="1750510676163"] .tn-atom {background-size:cover;-webkit-transform:rotate(290deg);-moz-transform:rotate(290deg);transform:rotate(290deg);}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676163"] {display:table;top:200px;left:calc(50% - 207px + -48px);width:192px;height:193px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676163"] {display:table;top:240px;left:calc(50% - 195px + -49px);width:185px;height:185px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676166"]{z-index:3;top:-93px;left:calc(50% - 600px + -179px);width:502px;height:555px;}#rec1117140011 .tn-elem[data-elem-id="1750510676166"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676166"] {display:table;top:-73px;left:calc(50% - 480px + -167px);width:471px;height:520px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676166"] {display:table;top:309px;left:calc(50% - 320px + -123px);width:392px;height:433px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676166"] {display:table;top:-69px;left:calc(50% - 207px + -80px);width:308px;height:341px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676166"] {display:table;top:-19px;width:296px;height:328px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676169"]{z-index:3;top:-23px;left:calc(50% - 600px + -192px);width:511px;height:622px;}#rec1117140011 .tn-elem[data-elem-id="1750510676169"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676169"] {display:table;top:-7px;left:calc(50% - 480px + -179px);width:479px;height:583px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676169"] {display:table;top:364px;left:calc(50% - 320px + -133px);width:398px;height:486px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676169"] {display:table;top:-40px;left:calc(50% - 207px + 23px);width:313px;height:382px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676169"] {display:table;top:16px;left:calc(50% - 195px + 0px);width:301px;height:367px;}}#rec1117140011 .tn-elem[data-elem-id="1750510676171"]{z-index:3;top:242px;left:calc(50% - 600px + 137px);width:227px;height:248px;}#rec1117140011 .tn-elem[data-elem-id="1750510676171"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117140011 .tn-elem[data-elem-id="1750510676171"] {display:table;top:241px;left:calc(50% - 480px + 130px);width:213px;height:233px;}}@media screen and (max-width:959px) {#rec1117140011 .tn-elem[data-elem-id="1750510676171"] {display:table;top:611px;left:calc(50% - 320px + 114px);width:177px;height:194px;}}@media screen and (max-width:639px) {#rec1117140011 .tn-elem[data-elem-id="1750510676171"] {display:table;top:156px;left:calc(50% - 207px + 144px);width:139px;height:153px;}}@media screen and (max-width:413px) {#rec1117140011 .tn-elem[data-elem-id="1750510676171"] {display:table;top:236px;left:calc(50% - 195px + 136px);width:134px;height:147px;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1117140011" data-artboard-screens="390,414,640,960,1200" data-artboard-height="799" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-ovrflw="visible" data-artboard-height-res-390="1170" data-artboard-height-res-414="1110" data-artboard-height-res-640="868"> <div class="t396__carrier" data-artboard-recid="1117140011"></div> <div class="t396__filter" data-artboard-recid="1117140011"></div> <div class='t396__elem tn-elem tn-elem__11171400111750439321837 t-animate' data-elem-id='1750439321837' data-elem-type='text' data-field-top-value="695" data-field-left-value="569" data-field-height-value="84" data-field-width-value="309" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1082" data-field-left-res-390-value="108" data-field-width-res-390-value="237" data-field-top-res-414-value="1056" data-field-left-res-414-value="15" data-field-width-res-414-value="386" data-field-top-res-640-value="735" data-field-left-res-640-value="247" data-field-top-res-960-value="702" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750439321837'>We’re not just selling shakes — we’re supporting healthy habits that stick</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321832 t-animate' data-elem-id='1750439321832' data-elem-type='text' data-field-top-value="493" data-field-left-value="569" data-field-height-value="34" data-field-width-value="133" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="941" data-field-top-res-414-value="901" data-field-left-res-414-value="15" data-field-top-res-640-value="564" data-field-left-res-640-value="248" data-field-top-res-960-value="500" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750439321832'>happy customers across the UAE</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321830 t-animate' data-elem-id='1750439321830' data-elem-type='text' data-field-top-value="493" data-field-left-value="802" data-field-height-value="17" data-field-width-value="106" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-top-res-390-value="941" data-field-left-res-390-value="201" data-field-top-res-414-value="901" data-field-left-res-414-value="213" data-field-width-res-414-value="98" data-field-top-res-640-value="564" data-field-left-res-640-value="446" data-field-top-res-960-value="500" data-field-left-res-960-value="670"> <div class='tn-atom'field='tn_text_1750439321830'>orders delivered</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321826 t-animate' data-elem-id='1750439321826' data-elem-type='text' data-field-top-value="448" data-field-left-value="569" data-field-height-value="38" data-field-width-value="177" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="908" data-field-top-res-414-value="868" data-field-left-res-414-value="15" data-field-top-res-640-value="524" data-field-left-res-640-value="248" data-field-top-res-960-value="455" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750439321826'>50,000+</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321824 t-animate' data-elem-id='1750439321824' data-elem-type='text' data-field-top-value="448" data-field-left-value="802" data-field-height-value="38" data-field-width-value="204" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="908" data-field-left-res-390-value="201" data-field-top-res-414-value="868" data-field-left-res-414-value="213" data-field-top-res-640-value="524" data-field-left-res-640-value="446" data-field-top-res-960-value="455" data-field-left-res-960-value="670"> <div class='tn-atom'field='tn_text_1750439321824'>200,000+</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321822 t-animate' data-elem-id='1750439321822' data-elem-type='text' data-field-top-value="608" data-field-left-value="569" data-field-height-value="17" data-field-width-value="142" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autowidth" data-field-top-res-390-value="1026" data-field-top-res-414-value="986" data-field-left-res-414-value="15" data-field-width-res-414-value="131" data-field-top-res-640-value="669" data-field-left-res-640-value="248" data-field-top-res-960-value="615" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750439321822'>flavors to choose from</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321820 t-animate' data-elem-id='1750439321820' data-elem-type='text' data-field-top-value="608" data-field-left-value="802" data-field-height-value="17" data-field-width-value="124" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-top-res-390-value="1026" data-field-left-res-390-value="201" data-field-top-res-414-value="986" data-field-left-res-414-value="213" data-field-width-res-414-value="115" data-field-top-res-640-value="669" data-field-left-res-640-value="446" data-field-top-res-960-value="615" data-field-left-res-960-value="670"> <div class='tn-atom'field='tn_text_1750439321820'>organic ingredients</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321818 t-animate' data-elem-id='1750439321818' data-elem-type='text' data-field-top-value="563" data-field-left-value="569" data-field-height-value="38" data-field-width-value="83" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="993" data-field-top-res-414-value="953" data-field-left-res-414-value="15" data-field-top-res-640-value="629" data-field-left-res-640-value="248" data-field-top-res-960-value="570" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750439321818'>10+</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321815 t-animate' data-elem-id='1750439321815' data-elem-type='text' data-field-top-value="563" data-field-left-value="802" data-field-height-value="38" data-field-width-value="116" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="993" data-field-left-res-390-value="201" data-field-top-res-414-value="953" data-field-left-res-414-value="213" data-field-top-res-640-value="629" data-field-left-res-640-value="446" data-field-top-res-960-value="570" data-field-left-res-960-value="670"> <div class='tn-atom'field='tn_text_1750439321815'>100%</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321808 t-animate' data-elem-id='1750439321808' data-elem-type='text' data-field-top-value="328" data-field-left-value="901" data-field-height-value="50" data-field-width-value="250" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="807" data-field-left-res-390-value="108" data-field-top-res-414-value="771" data-field-left-res-414-value="136" data-field-width-res-414-value="262" data-field-top-res-640-value="445" data-field-left-res-640-value="247" data-field-width-res-640-value="358" data-field-top-res-960-value="335" data-field-left-res-960-value="670" data-field-width-res-960-value="269"> <div class='tn-atom'field='tn_text_1750439321808'>Gluten free, soy free, dairy free, no added sugars, vegan friendly, no GMO’s, No Fillers or Binders</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750439321805 t-animate' data-elem-id='1750439321805' data-elem-type='text' data-field-top-value="328" data-field-left-value="569" data-field-height-value="50" data-field-width-value="305" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="744" data-field-left-res-390-value="108" data-field-top-res-414-value="708" data-field-left-res-414-value="136" data-field-width-res-414-value="267" data-field-top-res-640-value="375" data-field-left-res-640-value="247" data-field-width-res-640-value="301" data-field-top-res-960-value="335" data-field-left-res-960-value="395" data-field-width-res-960-value="247"> <div class='tn-atom'field='tn_text_1750439321805'>We believe that vitamins and minerals should be delivered to the body through real foods, not chemically synthesized pills.</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750502038890 t-animate' data-elem-id='1750502038890' data-elem-type='text' data-field-top-value="87" data-field-left-value="567" data-field-height-value="36" data-field-width-value="616" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinleft" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="409" data-field-left-res-390-value="15" data-field-width-res-390-value="291" data-field-top-res-414-value="337" data-field-left-res-414-value="136" data-field-width-res-414-value="263" data-field-left-res-640-value="248" data-field-width-res-640-value="373" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750502038890'>Multi-vit shakes offers you</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750502187292' data-elem-id='1750502187292' data-elem-type='image' data-field-top-value="487" data-field-left-value="1017" data-field-height-value="324" data-field-width-value="295" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="110" data-field-filewidth-value="305" data-field-fileheight-value="335" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="537" data-field-height-res-390-value="324" data-field-height-res-414-value="324" data-field-height-res-640-value="324" data-field-height-res-960-value="324"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3733-6333-4664-a666-653861323533/Clip_path_group-Phot.png'
src='../thb.tildacdn.one/tild3733-6333-4664-a666-653861323533/-/resize/20x/Clip_path_group-Phot.png'
alt='' imgfield='tn_img_1750502187292'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750502522170 t-animate' data-elem-id='1750502522170' data-elem-type='text' data-field-top-value="123" data-field-left-value="363" data-field-height-value="180" data-field-width-value="828" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinleft" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="465" data-field-width-res-390-value="367" data-field-top-res-414-value="421" data-field-left-res-414-value="15" data-field-height-res-414-value="168" data-field-width-res-414-value="384" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="151" data-field-left-res-640-value="15" data-field-width-res-640-value="605" data-field-top-res-960-value="118" data-field-left-res-960-value="296" data-field-width-res-960-value="649"> <div class='tn-atom'field='tn_text_1750502522170'>a simple, all natural solution to maintain your health – taking all the vitamins &amp; minerals your body needs, no artificial additives, nothing lab made, just 100% whole foods</div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676151' data-elem-id='1750510676151' data-elem-type='shape' data-field-top-value="698" data-field-left-value="-27" data-field-height-value="267" data-field-width-value="267" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-top-res-390-value="170" data-field-left-res-390-value="287" data-field-height-res-390-value="152" data-field-width-res-390-value="152" data-field-top-res-414-value="184" data-field-left-res-414-value="303" data-field-height-res-414-value="158" data-field-width-res-414-value="158" data-field-top-res-640-value="-52" data-field-left-res-640-value="-17" data-field-height-res-640-value="203" data-field-width-res-640-value="203" data-field-top-res-960-value="643" data-field-left-res-960-value="-90" data-field-height-res-960-value="251" data-field-width-res-960-value="251"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3265-3035-4134-b666-373839636434/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676155' data-elem-id='1750510676155' data-elem-type='shape' data-field-top-value="315" data-field-left-value="40" data-field-height-value="207" data-field-width-value="207" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="115" data-animate-mobile="y" data-field-top-res-390-value="222" data-field-left-res-390-value="50" data-field-height-res-390-value="122" data-field-width-res-390-value="122" data-field-top-res-414-value="182" data-field-left-res-414-value="55" data-field-height-res-414-value="127" data-field-width-res-414-value="127" data-field-top-res-640-value="628" data-field-left-res-640-value="49" data-field-height-res-640-value="162" data-field-width-res-640-value="161" data-field-top-res-960-value="310" data-field-left-res-960-value="39" data-field-height-res-960-value="194" data-field-width-res-960-value="194"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6130-6166-4263-b165-393333336337/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676158' data-elem-id='1750510676158' data-elem-type='shape' data-field-top-value="600" data-field-left-value="185" data-field-height-value="186" data-field-width-value="185" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="115" data-animate-mobile="y" data-field-top-res-390-value="249" data-field-left-res-390-value="262" data-field-height-res-390-value="113" data-field-width-res-390-value="112" data-field-top-res-414-value="224" data-field-left-res-414-value="276" data-field-height-res-414-value="118" data-field-width-res-414-value="117" data-field-top-res-640-value="0" data-field-left-res-640-value="-52" data-field-height-res-640-value="151" data-field-width-res-640-value="151" data-field-top-res-960-value="577" data-field-left-res-960-value="174" data-field-height-res-960-value="174" data-field-width-res-960-value="174"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3366-6164-4632-b261-653238653536/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676161' data-elem-id='1750510676161' data-elem-type='shape' data-field-top-value="357" data-field-left-value="268" data-field-height-value="211" data-field-width-value="196" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="85" data-animate-mobile="y" data-field-top-res-390-value="1052" data-field-left-res-390-value="-52" data-field-height-res-390-value="182" data-field-width-res-390-value="167" data-field-top-res-414-value="171" data-field-left-res-414-value="-7" data-field-height-res-414-value="130" data-field-width-res-414-value="120" data-field-top-res-640-value="669" data-field-left-res-640-value="-581" data-field-height-res-640-value="165" data-field-width-res-640-value="153" data-field-top-res-960-value="378" data-field-left-res-960-value="116" data-field-height-res-960-value="198" data-field-width-res-960-value="184"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6332-6434-4835-a130-653633376236/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676163' data-elem-id='1750510676163' data-elem-type='shape' data-field-top-value="356" data-field-left-value="-81" data-field-height-value="314" data-field-width-value="314" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="85" data-animate-mobile="y" data-field-top-res-390-value="240" data-field-left-res-390-value="-49" data-field-height-res-390-value="185" data-field-width-res-390-value="185" data-field-top-res-414-value="200" data-field-left-res-414-value="-48" data-field-height-res-414-value="193" data-field-width-res-414-value="192" data-field-top-res-640-value="668" data-field-left-res-640-value="-34" data-field-height-res-640-value="245" data-field-width-res-640-value="244" data-field-top-res-960-value="348" data-field-left-res-960-value="-75" data-field-height-res-960-value="294" data-field-width-res-960-value="294"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3432-3666-4565-b061-633233666530/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676166' data-elem-id='1750510676166' data-elem-type='shape' data-field-top-value="-93" data-field-left-value="-179" data-field-height-value="555" data-field-width-value="502" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-top-res-390-value="-19" data-field-height-res-390-value="328" data-field-width-res-390-value="296" data-field-top-res-414-value="-69" data-field-left-res-414-value="-80" data-field-height-res-414-value="341" data-field-width-res-414-value="308" data-field-top-res-640-value="309" data-field-left-res-640-value="-123" data-field-height-res-640-value="433" data-field-width-res-640-value="392" data-field-top-res-960-value="-73" data-field-left-res-960-value="-167" data-field-height-res-960-value="520" data-field-width-res-960-value="471"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6338-3535-4530-a337-663166363034/Rectangle_2.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676169' data-elem-id='1750510676169' data-elem-type='shape' data-field-top-value="-23" data-field-left-value="-192" data-field-height-value="622" data-field-width-value="511" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-top-res-390-value="16" data-field-left-res-390-value="0" data-field-height-res-390-value="367" data-field-width-res-390-value="301" data-field-top-res-414-value="-40" data-field-left-res-414-value="23" data-field-height-res-414-value="382" data-field-width-res-414-value="313" data-field-top-res-640-value="364" data-field-left-res-640-value="-133" data-field-height-res-640-value="486" data-field-width-res-640-value="398" data-field-top-res-960-value="-7" data-field-left-res-960-value="-179" data-field-height-res-960-value="583" data-field-width-res-960-value="479"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3232-3666-4562-b730-623238336462/Group-1_1.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11171400111750510676171' data-elem-id='1750510676171' data-elem-type='shape' data-field-top-value="242" data-field-left-value="137" data-field-height-value="248" data-field-width-value="227" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-top-res-390-value="236" data-field-left-res-390-value="136" data-field-height-res-390-value="147" data-field-width-res-390-value="134" data-field-top-res-414-value="156" data-field-left-res-414-value="144" data-field-height-res-414-value="153" data-field-width-res-414-value="139" data-field-top-res-640-value="611" data-field-left-res-640-value="114" data-field-height-res-640-value="194" data-field-width-res-640-value="177" data-field-top-res-960-value="241" data-field-left-res-960-value="130" data-field-height-res-960-value="233" data-field-width-res-960-value="213"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6135-6435-4234-a439-643730616364/Group_1.png"
aria-label='' role="img"> </div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1117140011');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1117140011');});});</script> <!-- /T396 --> </div> <div id="rec1117948046" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1117948046 .t396__artboard {height:930px;background-color:#f5f5f5;overflow:visible;}#rec1117948046 .t396__filter {height:930px;}#rec1117948046 .t396__carrier{height:930px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1117948046 .t396__artboard,#rec1117948046 .t396__filter,#rec1117948046 .t396__carrier {}#rec1117948046 .t396__filter {}#rec1117948046 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1117948046 .t396__artboard,#rec1117948046 .t396__filter,#rec1117948046 .t396__carrier {height:1238px;}#rec1117948046 .t396__filter {}#rec1117948046 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1117948046 .t396__artboard,#rec1117948046 .t396__filter,#rec1117948046 .t396__carrier {height:1226px;}#rec1117948046 .t396__filter {}#rec1117948046 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1117948046 .t396__artboard,#rec1117948046 .t396__filter,#rec1117948046 .t396__carrier {height:1276px;}#rec1117948046 .t396__filter {}#rec1117948046 .t396__carrier {background-attachment:scroll;}}#rec1117948046 .tn-elem[data-elem-id="1750502694237"]{z-index:3;top:501px;left:calc(50% - 600px + 1454px);width:654px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694237"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750502694237"] .tn-atom {-webkit-transform:rotate(270deg);-moz-transform:rotate(270deg);transform:rotate(270deg);}#rec1117948046 .tn-elem[data-elem-id="1750502694237"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694198"]{color:#8091e2;text-align:LEFT;z-index:3;top:138px;left:calc(50% - 600px + 362px);width:801px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694198"] .tn-atom {vertical-align:middle;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694198"] {display:table;top:134px;left:calc(50% - 480px + 296px);width:593px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694198"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694198"] {display:table;top:168px;left:calc(50% - 320px + 15px);width:578px;height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694198"] {display:table;left:calc(50% - 207px + 136px);width:271px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694198"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694198"] {display:table;left:calc(50% - 195px + 107px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694193"]{color:#8091e2;text-align:LEFT;z-index:3;top:100px;left:calc(50% - 600px + 561px);width:auto;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694193"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694193"] {display:table;left:calc(50% - 480px + 395px);width:535px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694193"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694193"] {display:table;left:calc(50% - 320px + 247px);width:361px;height:68px;}#rec1117948046 .tn-elem[data-elem-id="1750502694193"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694193"] {display:table;left:calc(50% - 207px + 15px);width:303px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694193"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694193"] {display:table;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694187"]{color:#1a1919;text-align:LEFT;z-index:3;top:793px;left:calc(50% - 600px + 1027px);width:437px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694187"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694187"] {display:table;left:calc(50% - 480px + 782px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694187"] {display:table;top:1104px;left:calc(50% - 320px + 247px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694187"] {display:table;top:1105px;left:calc(50% - 207px + 336px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694187"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694187"] {display:table;top:1155px;left:calc(50% - 195px + 316px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750516647021"]{color:#1a1919;text-align:LEFT;z-index:3;top:239px;left:calc(50% - 600px + 564px);width:437px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750516647021"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750516647021"] {display:table;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750516647021"] {display:table;top:273px;left:calc(50% - 320px + 15px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750516647021"] {display:table;top:278px;width:386px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750516647021"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750516647021"] {display:table;top:298px;width:354px;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694182"]{color:#1a1919;text-align:LEFT;z-index:3;top:482px;left:calc(50% - 600px + 564px);width:480px;height:101px;}#rec1117948046 .tn-elem[data-elem-id="1750502694182"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694182"] {display:table;left:calc(50% - 480px + 395px);width:px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694182"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694182"] {display:table;top:664px;left:calc(50% - 320px + 247px);width:373px;height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694182"] {display:table;top:622px;left:calc(50% - 207px + 136px);width:264px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694182"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694182"] {display:table;top:662px;left:calc(50% - 195px + 107px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694178"]{z-index:3;top:239px;left:calc(50% - 600px + 18px);width:521px;height:344px;}#rec1117948046 .tn-elem[data-elem-id="1750502694178"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694178"] {display:table;left:calc(50% - 480px + 20px);width:361px;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694178"] {display:table;top:373px;left:calc(50% - 320px + 15px);width:610px;height:253px;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694178"] {display:table;width:384px;height:218px;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694178"] {display:table;top:408px;width:361px;}}#rec1117948046 .tn-elem[data-elem-id="1750502694174"]{z-index:3;top:288px;left:calc(50% - 600px + 959px);width:221px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694174"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750502694174"] .tn-atom {-webkit-transform:rotate(84deg);-moz-transform:rotate(84deg);transform:rotate(84deg);}#rec1117948046 .tn-elem[data-elem-id="1750502694174"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694174"] {display:table;top:272px;left:calc(50% - 480px + 752px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694174"] .tn-atom {background-size:cover;-webkit-transform:rotate(84deg);-moz-transform:rotate(84deg);transform:rotate(84deg);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694174"] {display:table;top:168px;left:calc(50% - 320px + 468px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694174"] {display:table;top:770px;left:calc(50% - 207px + 0px);width:201px;height:auto;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694174"] {display:table;top:830px;left:calc(50% - 195px + 10px);width:191px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694174"] .tn-atom {background-size:cover;-webkit-transform:rotate(331deg);-moz-transform:rotate(331deg);transform:rotate(331deg);}}#rec1117948046 .tn-elem[data-elem-id="1750502694203"]{color:#1a1919;text-align:LEFT;z-index:3;top:675px;left:calc(50% - 600px + 829px);width:117px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694203"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694203"] {display:table;left:calc(50% - 480px + 621px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694203"] {display:table;top:949px;left:calc(50% - 320px + 249px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694203"] {display:table;top:995px;left:calc(50% - 207px + 30px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694203"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694203"] {display:table;top:1045px;left:calc(50% - 195px + 32px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694212"]{color:#1a1919;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 829px);width:97px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694212"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694212"] {display:table;left:calc(50% - 480px + 621px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694212"] {display:table;top:917px;left:calc(50% - 320px + 249px);width:97px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694212"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694212"] {display:table;top:971px;left:calc(50% - 207px + 30px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694212"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694212"] {display:table;top:1021px;left:calc(50% - 195px + 32px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694155"]{color:#1a1919;text-align:LEFT;z-index:3;top:675px;left:calc(50% - 600px + 32px);width:179px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694155"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694155"] {display:table;top:675px;left:calc(50% - 480px + 32px);width:127px;height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694155"] {display:table;top:839px;left:calc(50% - 320px + 32px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694155"] {display:table;top:761px;width:109px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694155"] .tn-atom{font-size:13px;background-size:cover;-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);transform:rotate(0deg);}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694155"] {display:table;top:811px;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694163"]{color:#1a1919;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 32px);width:129px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694163"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694163"] {display:table;top:643px;left:calc(50% - 480px + 32px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694163"] {display:table;top:807px;left:calc(50% - 320px + 32px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694163"] {display:table;top:737px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694163"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694163"] {display:table;top:787px;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694133"]{color:#1a1919;text-align:LEFT;z-index:3;top:675px;left:calc(50% - 600px + 318px);width:144px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694133"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694133"] {display:table;left:calc(50% - 480px + 212px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694133"] {display:table;top:839px;left:calc(50% - 320px + 249px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694133"] {display:table;top:761px;left:calc(50% - 207px + 223px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694133"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694133"] {display:table;top:811px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694142"]{color:#1a1919;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 318px);width:108px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694142"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694142"] {display:table;left:calc(50% - 480px + 212px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694142"] {display:table;top:807px;left:calc(50% - 320px + 249px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694142"] {display:table;top:737px;left:calc(50% - 207px + 223px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694142"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694142"] {display:table;top:787px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694117"]{color:#1a1919;text-align:LEFT;z-index:3;top:675px;left:calc(50% - 600px + 561px);width:174px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694117"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694117"] {display:table;left:calc(50% - 480px + 403px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694117"] {display:table;top:839px;left:calc(50% - 320px + 454px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694117"] {display:table;top:878px;left:calc(50% - 207px + 223px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694117"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694117"] {display:table;top:928px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694124"]{color:#1a1919;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 561px);width:132px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694124"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694124"] {display:table;left:calc(50% - 480px + 403px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694124"] {display:table;top:807px;left:calc(50% - 320px + 454px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694124"] {display:table;top:854px;left:calc(50% - 207px + 223px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694124"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694124"] {display:table;top:904px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694223"]{color:#1a1919;text-align:LEFT;z-index:3;top:675px;left:calc(50% - 600px + 1040px);width:138px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694223"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694223"] {display:table;left:calc(50% - 480px + 792px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694223"] {display:table;top:949px;left:calc(50% - 320px + 454px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694223"] {display:table;top:995px;left:calc(50% - 207px + 224px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694223"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694223"] {display:table;top:1049px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750502694228"]{color:#1a1919;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 1040px);width:118px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694228"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750502694228"] {display:table;left:calc(50% - 480px + 792px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750502694228"] {display:table;top:917px;left:calc(50% - 320px + 454px);width:118px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694228"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750502694228"] {display:table;top:971px;left:calc(50% - 207px + 224px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750502694228"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750502694228"] {display:table;top:1025px;left:calc(50% - 195px + 209px);height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750503642398"]{color:#000000;z-index:3;top:761px;left:calc(50% - 600px + 565px);width:309px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750503642398"] .tn-atom {vertical-align:middle;color:#000000;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:1;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750503642398"] {display:table;left:calc(50% - 480px + 395px);height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750503642398"] {display:table;top:1035px;left:calc(50% - 320px + 247px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750503642398"] {display:table;top:1081px;left:calc(50% - 207px + 15px);height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750503642398"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750503642398"] {display:table;top:1131px;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750503807837"]{color:#ffffff;text-align:center;z-index:3;top:838px;left:calc(50% - 600px + 552px);width:247px;height:45px;}#rec1117948046 .tn-elem[data-elem-id="1750503807837"] .tn-atom{color:#ffffff;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750503807837"] {display:table;left:calc(50% - 480px + 384px);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750503807837"] {display:table;top:1152px;left:calc(50% - 320px + 235px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750503807837"] {display:table;top:1161px;left:calc(50% - 207px + 15px);width:384px;}#rec1117948046 .tn-elem[data-elem-id="1750503807837"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750503807837"] {display:table;top:1211px;width:360px;}}#rec1117948046 .tn-elem[data-elem-id="1750503908212"]{z-index:3;top:726px;left:calc(50% - 600px + -141px);width:356px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750503908212"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750503908212"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750503908212"] {display:table;top:741px;left:calc(50% - 480px + -132px);width:330px;height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750503908212"] {display:table;top:861px;left:calc(50% - 320px + -107px);width:315px;height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750503908212"] {display:table;top:855px;left:calc(50% - 207px + -714px);height:auto;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750503908212"] {display:table;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750503910360"]{z-index:3;top:817px;left:calc(50% - 600px + 1015px);width:265px;height:auto;}#rec1117948046 .tn-elem[data-elem-id="1750503910360"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750503910360"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750503910360"] {display:table;top:811px;left:calc(50% - 480px + 805px);width:239px;height:auto;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750503910360"] {display:table;top:1066px;left:calc(50% - 320px + 509px);height:auto;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750503910360"] {display:table;top:610px;left:calc(50% - 207px + -461px);height:auto;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750503910360"] {display:table;height:auto;}}#rec1117948046 .tn-elem[data-elem-id="1750506160331"]{z-index:3;top:621px;left:calc(50% - 600px + 20px);width:1159px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506160331"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506160331"] {display:table;width:920px;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506160331"] {display:table;top:786px;left:calc(50% - 320px + 15px);width:610px;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506160331"] {display:table;top:716px;width:384px;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506160331"] {display:table;top:766px;width:360px;}}#rec1117948046 .tn-elem[data-elem-id="1750517432058"]{z-index:3;top:-7053px;left:calc(50% - 600px + -11957px);width:1159px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750517432058"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750517432058"] {display:table;width:920px;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750517432058"] {display:table;top:897px;left:calc(50% - 320px + 233px);width:392px;}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750517432058"] {display:table;top:1049px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750517432058"] {display:table;top:1100px;width:360px;}}#rec1117948046 .tn-elem[data-elem-id="1750506255197"]{z-index:3;top:672px;left:calc(50% - 600px + -30px);width:100px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506255197"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750506255197"] .tn-atom {-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506255197"] {display:table;}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506255197"] {display:table;top:836px;left:calc(50% - 320px + -35px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506255197"] {display:table;top:766px;}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506255197"] {display:table;top:816px;}}#rec1117948046 .tn-elem[data-elem-id="1750506285256"]{z-index:3;top:672px;left:calc(50% - 600px + 257px);width:100px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506285256"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750506285256"] .tn-atom {-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506285256"] {display:table;left:calc(50% - 480px + 151px);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506285256"] {display:table;top:836px;left:calc(50% - 320px + 183px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506285256"] {display:table;top:766px;left:calc(50% - 207px + 157px);}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506285256"] {display:table;top:816px;left:calc(50% - 195px + 145px);}}#rec1117948046 .tn-elem[data-elem-id="1750506297682"]{z-index:3;top:672px;left:calc(50% - 600px + 499px);width:100px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506297682"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750506297682"] .tn-atom {-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506297682"] {display:table;left:calc(50% - 480px + 341px);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506297682"] {display:table;top:836px;left:calc(50% - 320px + 387px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506297682"] {display:table;top:883px;left:calc(50% - 207px + 157px);}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506297682"] {display:table;top:933px;left:calc(50% - 195px + 145px);}}#rec1117948046 .tn-elem[data-elem-id="1750506426262"]{z-index:3;top:672px;left:calc(50% - 600px + 978px);width:100px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506426262"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750506426262"] .tn-atom {-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506426262"] {display:table;left:calc(50% - 480px + 730px);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506426262"] {display:table;top:946px;left:calc(50% - 320px + 387px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506426262"] {display:table;top:1000px;left:calc(50% - 207px + 157px);}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506426262"] {display:table;top:1050px;left:calc(50% - 195px + 145px);}}#rec1117948046 .tn-elem[data-elem-id="1750506359039"]{z-index:3;top:672px;left:calc(50% - 600px + 766px);width:100px;height:1px;}#rec1117948046 .tn-elem[data-elem-id="1750506359039"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#7f90e0;background-position:center center;border-color:transparent ;border-style:solid;}#rec1117948046 .tn-elem[data-elem-id="1750506359039"] .tn-atom {-webkit-transform:rotate(90deg);-moz-transform:rotate(90deg);transform:rotate(90deg);}@media screen and (max-width:1199px) {#rec1117948046 .tn-elem[data-elem-id="1750506359039"] {display:table;left:calc(50% - 480px + 558px);}}@media screen and (max-width:959px) {#rec1117948046 .tn-elem[data-elem-id="1750506359039"] {display:table;top:946px;left:calc(50% - 320px + 183px);}}@media screen and (max-width:639px) {#rec1117948046 .tn-elem[data-elem-id="1750506359039"] {display:table;top:1000px;left:calc(50% - 207px + -35px);}}@media screen and (max-width:413px) {#rec1117948046 .tn-elem[data-elem-id="1750506359039"] {display:table;top:1050px;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1117948046" data-artboard-screens="390,414,640,960,1200" data-artboard-height="930" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-ovrflw="visible" data-artboard-height-res-390="1276" data-artboard-height-res-414="1226" data-artboard-height-res-640="1238"> <div class="t396__carrier" data-artboard-recid="1117948046"></div> <div class="t396__filter" data-artboard-recid="1117948046"></div> <div class='t396__elem tn-elem tn-elem__11179480461750502694237' data-elem-id='1750502694237' data-elem-type='vector' data-field-top-value="501" data-field-left-value="1454" data-field-height-value="115" data-field-width-value="654" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-filewidth-value="654" data-field-fileheight-value="115" data-field-heightmode-value="hug"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 654.28 115.11" data-guides="{&quot;vertical&quot;:[],&quot;horizontal&quot;:[]}"> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg1348d0bf835" title="Line 43" x1="0.9999364870254794" y1="0.9998773810930288" x2="653.2825613350778" y2="0.9998773810930288"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg4395c90afd" title="Line 44" x1="653.2825613350778" y1="0.9998773810930288" x2="653.2825613350778" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvge68baf3a58" title="Line 45" x1="107.21328903698395" y1="0.9998773810930288" x2="107.21328903698395" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg6a9161475b" title="Line 46" x1="242.21328903698395" y1="0.9998773810930288" x2="242.21328903698395" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg13717f7fb96" title="Line 47" x1="387.2109375" y1="0.9998773810930288" x2="387.2109375" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvgde4373fff2" title="Line 48" x1="520.213289036984" y1="0.9998773810930288" x2="520.213289036984" y2="114.11044275851509"></line> <defs></defs> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694198 t-animate' data-elem-id='1750502694198' data-elem-type='text' data-field-top-value="138" data-field-left-value="362" data-field-height-value="76" data-field-width-value="801" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinleft" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-left-res-390-value="107" data-field-left-res-414-value="136" data-field-width-res-414-value="271" data-field-top-res-640-value="168" data-field-left-res-640-value="15" data-field-width-res-640-value="578" data-field-top-res-960-value="134" data-field-left-res-960-value="296" data-field-width-res-960-value="593"> <div class='tn-atom'field='tn_text_1750502694198'>out of a desire to simplify the path to health</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694193 t-animate' data-elem-id='1750502694193' data-elem-type='text' data-field-top-value="100" data-field-left-value="561" data-field-height-value="38" data-field-width-value="602" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinleft" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-left-res-414-value="15" data-field-width-res-414-value="303" data-field-left-res-640-value="247" data-field-height-res-640-value="68" data-field-width-res-640-value="361" data-field-textfit-res-640-value="fixedsize" data-field-left-res-960-value="395" data-field-width-res-960-value="535"> <div class='tn-atom'field='tn_text_1750502694193'>Multi-Vit Shakes was born</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694187' data-elem-id='1750502694187' data-elem-type='text' data-field-top-value="793" data-field-left-value="1027" data-field-height-value="18" data-field-width-value="437" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1155" data-field-left-res-390-value="316" data-field-top-res-414-value="1105" data-field-left-res-414-value="336" data-field-top-res-640-value="1104" data-field-left-res-640-value="247" data-field-left-res-960-value="782"> <div class='tn-atom'><a href="#superfoods"style="color: inherit"><u>View all →</u></a></div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750516647021' data-elem-id='1750516647021' data-elem-type='text' data-field-top-value="239" data-field-left-value="564" data-field-height-value="67" data-field-width-value="437" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="298" data-field-width-res-390-value="354" data-field-top-res-414-value="278" data-field-width-res-414-value="386" data-field-top-res-640-value="273" data-field-left-res-640-value="15" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750516647021'>We realized that people needed not only a simple but also a delicious solution for maintaining health. Since our creation, we've been offering natural shakes that replace synthetic vitamins and provide tasty and healthy solutions for everyday life.</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694182' data-elem-id='1750502694182' data-elem-type='text' data-field-top-value="482" data-field-left-value="564" data-field-height-value="101" data-field-width-value="480" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-textfit-value="fixedsize" data-field-top-res-390-value="662" data-field-left-res-390-value="107" data-field-top-res-414-value="622" data-field-left-res-414-value="136" data-field-width-res-414-value="264" data-field-top-res-640-value="664" data-field-left-res-640-value="247" data-field-width-res-640-value="373" data-field-left-res-960-value="395" data-field-heightunits-res-960-value="px" data-field-textfit-res-960-value="autoheight"> <div class='tn-atom'field='tn_text_1750502694182'>We don’t only use fruits and vegetables, we use carefully selected rare and rich superfoods like:</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694178' data-elem-id='1750502694178' data-elem-type='shape' data-field-top-value="239" data-field-left-value="18" data-field-height-value="344" data-field-width-value="521" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="408" data-field-width-res-390-value="361" data-field-height-res-414-value="218" data-field-width-res-414-value="384" data-field-top-res-640-value="373" data-field-left-res-640-value="15" data-field-height-res-640-value="253" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-left-res-960-value="20" data-field-width-res-960-value="361"> <div class='tn-atom t-bgimg' data-original="../static.tildacdn.one/tild3866-3434-4162-a439-393865633262/1fce4b48-33e2-4e5d-b.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694174' data-elem-id='1750502694174' data-elem-type='image' data-field-top-value="288" data-field-left-value="959" data-field-height-value="221" data-field-width-value="221" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-filewidth-value="1024" data-field-fileheight-value="1024" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="830" data-field-left-res-390-value="10" data-field-height-res-390-value="191" data-field-width-res-390-value="191" data-field-top-res-414-value="770" data-field-left-res-414-value="0" data-field-height-res-414-value="201" data-field-width-res-414-value="201" data-field-top-res-640-value="168" data-field-left-res-640-value="468" data-field-height-res-640-value="221" data-field-top-res-960-value="272" data-field-left-res-960-value="752" data-field-height-res-960-value="221"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild3065-3837-4565-a538-653931393763/8ca7b6a0-ba7b-46c8-9.png'
src='../thb.tildacdn.one/tild3065-3837-4565-a538-653931393763/-/resize/20x/8ca7b6a0-ba7b-46c8-9.png'
alt='' imgfield='tn_img_1750502694174'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694203 t-animate' data-elem-id='1750502694203' data-elem-type='text' data-field-top-value="675" data-field-left-value="829" data-field-height-value="34" data-field-width-value="117" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.4" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1045" data-field-left-res-390-value="32" data-field-top-res-414-value="995" data-field-left-res-414-value="30" data-animate-delay-res-414="0.2" data-field-top-res-640-value="949" data-field-left-res-640-value="249" data-field-left-res-960-value="621"> <div class='tn-atom'field='tn_text_1750502694203'>Clean energy and mental clarity</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694212 t-animate' data-elem-id='1750502694212' data-elem-type='text' data-field-top-value="643" data-field-left-value="829" data-field-height-value="21" data-field-width-value="97" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.4" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1021" data-field-left-res-390-value="32" data-field-top-res-414-value="971" data-field-left-res-414-value="30" data-animate-delay-res-414="0.2" data-field-top-res-640-value="917" data-field-left-res-640-value="249" data-field-height-res-640-value="21" data-field-width-res-640-value="97" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-left-res-960-value="621"> <div class='tn-atom'field='tn_text_1750502694212'>Matcha</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694155 t-animate' data-elem-id='1750502694155' data-elem-type='text' data-field-top-value="675" data-field-left-value="32" data-field-height-value="17" data-field-width-value="179" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="811" data-field-top-res-414-value="761" data-field-width-res-414-value="109" data-animate-delay-res-414="0.2" data-field-top-res-640-value="839" data-field-left-res-640-value="32" data-field-top-res-960-value="675" data-field-left-res-960-value="32" data-field-width-res-960-value="127"> <div class='tn-atom'field='tn_text_1750502694155'>Detox and immune support</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694163 t-animate' data-elem-id='1750502694163' data-elem-type='text' data-field-top-value="643" data-field-left-value="32" data-field-height-value="21" data-field-width-value="129" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="787" data-field-top-res-414-value="737" data-animate-delay-res-414="0.2" data-field-top-res-640-value="807" data-field-left-res-640-value="32" data-field-top-res-960-value="643" data-field-left-res-960-value="32"> <div class='tn-atom'field='tn_text_1750502694163'>Chlorella</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694133 t-animate' data-elem-id='1750502694133' data-elem-type='text' data-field-top-value="675" data-field-left-value="318" data-field-height-value="34" data-field-width-value="144" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="811" data-field-left-res-390-value="209" data-field-top-res-414-value="761" data-field-left-res-414-value="223" data-animate-delay-res-414="0.2" data-field-top-res-640-value="839" data-field-left-res-640-value="249" data-field-left-res-960-value="212"> <div class='tn-atom'field='tn_text_1750502694133'>Natural multivitamin with over 90 nutrients</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694142 t-animate' data-elem-id='1750502694142' data-elem-type='text' data-field-top-value="643" data-field-left-value="318" data-field-height-value="21" data-field-width-value="108" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="787" data-field-left-res-390-value="209" data-field-top-res-414-value="737" data-field-left-res-414-value="223" data-animate-delay-res-414="0.2" data-field-top-res-640-value="807" data-field-left-res-640-value="249" data-field-left-res-960-value="212"> <div class='tn-atom'field='tn_text_1750502694142'>Moringa</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694117 t-animate' data-elem-id='1750502694117' data-elem-type='text' data-field-top-value="675" data-field-left-value="561" data-field-height-value="34" data-field-width-value="174" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.3" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="928" data-field-left-res-390-value="209" data-field-top-res-414-value="878" data-field-left-res-414-value="223" data-animate-delay-res-414="0.2" data-field-top-res-640-value="839" data-field-left-res-640-value="454" data-field-left-res-960-value="403"> <div class='tn-atom'field='tn_text_1750502694117'>Contains 92 of the 102 minerals your body needs</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694124 t-animate' data-elem-id='1750502694124' data-elem-type='text' data-field-top-value="643" data-field-left-value="561" data-field-height-value="21" data-field-width-value="132" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.3" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="904" data-field-left-res-390-value="209" data-field-top-res-414-value="854" data-field-left-res-414-value="223" data-animate-delay-res-414="0.2" data-field-top-res-640-value="807" data-field-left-res-640-value="454" data-field-left-res-960-value="403"> <div class='tn-atom'field='tn_text_1750502694124'>Sea Moss</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694223 t-animate' data-elem-id='1750502694223' data-elem-type='text' data-field-top-value="675" data-field-left-value="1040" data-field-height-value="34" data-field-width-value="138" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.5" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1049" data-field-left-res-390-value="209" data-field-top-res-414-value="995" data-field-left-res-414-value="224" data-animate-delay-res-414="0.2" data-field-top-res-640-value="949" data-field-left-res-640-value="454" data-field-left-res-960-value="792"> <div class='tn-atom'field='tn_text_1750502694223'>Plant-based protein and iron</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750502694228 t-animate' data-elem-id='1750502694228' data-elem-type='text' data-field-top-value="643" data-field-left-value="1040" data-field-height-value="21" data-field-width-value="118" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.5" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1025" data-field-left-res-390-value="209" data-field-top-res-414-value="971" data-field-left-res-414-value="224" data-animate-delay-res-414="0.2" data-field-top-res-640-value="917" data-field-left-res-640-value="454" data-field-height-res-640-value="21" data-field-width-res-640-value="118" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-left-res-960-value="792"> <div class='tn-atom'field='tn_text_1750502694228'>Spirulina</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750503642398' data-elem-id='1750503642398' data-elem-type='text' data-field-top-value="761" data-field-left-value="565" data-field-height-value="50" data-field-width-value="309" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1131" data-field-top-res-414-value="1081" data-field-left-res-414-value="15" data-field-top-res-640-value="1035" data-field-left-res-640-value="247" data-field-left-res-960-value="395"> <div class='tn-atom'field='tn_text_1750503642398'>Acai berry, leafy greens, fruits, seeds &amp; more</div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750503807837' data-elem-id='1750503807837' data-elem-type='button' data-field-top-value="838" data-field-left-value="552" data-field-height-value="45" data-field-width-value="247" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-top-res-390-value="1211" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1161" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="1152" data-field-left-res-640-value="235" data-field-left-res-960-value="384"> <a class='tn-atom' href="catalog.html#ct">Choose Your Shake →</a> </div> <div class='t396__elem tn-elem tn-elem__11179480461750503908212' data-elem-id='1750503908212' data-elem-type='image' data-field-top-value="726" data-field-left-value="-141" data-field-height-value="359" data-field-width-value="356" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-filewidth-value="477" data-field-fileheight-value="694" data-field-heightmode-value="hug" data-field-height-res-390-value="317" data-field-top-res-414-value="855" data-field-left-res-414-value="-714" data-field-height-res-414-value="317" data-field-top-res-640-value="861" data-field-left-res-640-value="-107" data-field-height-res-640-value="317" data-field-width-res-640-value="315" data-field-top-res-960-value="741" data-field-left-res-960-value="-132" data-field-height-res-960-value="332" data-field-width-res-960-value="330"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild3732-3463-4738-b163-366536313265/image-from-rawpixel-.png'
src='../thb.tildacdn.one/tild3732-3463-4738-b163-366536313265/-/resize/20x/image-from-rawpixel-.png'
alt='' imgfield='tn_img_1750503908212'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750503910360' data-elem-id='1750503910360' data-elem-type='image' data-field-top-value="817" data-field-left-value="1015" data-field-height-value="244" data-field-width-value="265" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-filewidth-value="297" data-field-fileheight-value="273" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-height-res-390-value="220" data-field-top-res-414-value="610" data-field-left-res-414-value="-461" data-field-height-res-414-value="220" data-field-top-res-640-value="1066" data-field-left-res-640-value="509" data-field-height-res-640-value="220" data-field-top-res-960-value="811" data-field-left-res-960-value="805" data-field-height-res-960-value="220" data-field-width-res-960-value="239"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild6361-3735-4139-a434-316165643161/image-from-rawpixel-.png'
src='../thb.tildacdn.one/tild6361-3735-4139-a434-316165643161/-/resize/20x/image-from-rawpixel-.png'
alt='' imgfield='tn_img_1750503910360'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506160331' data-elem-id='1750506160331' data-elem-type='shape' data-field-top-value="621" data-field-left-value="20" data-field-height-value="1" data-field-width-value="1159" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-top-res-390-value="766" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="716" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="786" data-field-left-res-640-value="15" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-width-res-960-value="920" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750517432058' data-elem-id='1750517432058' data-elem-type='shape' data-field-top-value="-7053" data-field-left-value="-11957" data-field-height-value="1" data-field-width-value="1159" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-top-res-390-value="1100" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1049" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="897" data-field-left-res-640-value="233" data-field-width-res-640-value="392" data-field-widthmode-res-640-value="fixed" data-field-width-res-960-value="920" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506255197' data-elem-id='1750506255197' data-elem-type='shape' data-field-top-value="672" data-field-left-value="-30" data-field-height-value="1" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="816" data-field-top-res-414-value="766" data-field-top-res-640-value="836" data-field-left-res-640-value="-35"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506285256' data-elem-id='1750506285256' data-elem-type='shape' data-field-top-value="672" data-field-left-value="257" data-field-height-value="1" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="816" data-field-left-res-390-value="145" data-field-top-res-414-value="766" data-field-left-res-414-value="157" data-field-top-res-640-value="836" data-field-left-res-640-value="183" data-field-left-res-960-value="151"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506297682' data-elem-id='1750506297682' data-elem-type='shape' data-field-top-value="672" data-field-left-value="499" data-field-height-value="1" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="933" data-field-left-res-390-value="145" data-field-top-res-414-value="883" data-field-left-res-414-value="157" data-field-top-res-640-value="836" data-field-left-res-640-value="387" data-field-left-res-960-value="341"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506426262' data-elem-id='1750506426262' data-elem-type='shape' data-field-top-value="672" data-field-left-value="978" data-field-height-value="1" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1050" data-field-left-res-390-value="145" data-field-top-res-414-value="1000" data-field-left-res-414-value="157" data-field-top-res-640-value="946" data-field-left-res-640-value="387" data-field-left-res-960-value="730"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11179480461750506359039' data-elem-id='1750506359039' data-elem-type='shape' data-field-top-value="672" data-field-left-value="766" data-field-height-value="1" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1050" data-field-top-res-414-value="1000" data-field-left-res-414-value="-35" data-field-top-res-640-value="946" data-field-left-res-640-value="183" data-field-left-res-960-value="558"> <div class='tn-atom'> </div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1117948046');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1117948046');});});</script> <!-- /T396 --> </div> <div id="rec1118363251" class="r t-rec" style=" " data-animationappear="off" data-record-type="1093"> <!-- t1093 --> <div class="t1093"> <div class="t-popup " data-anim="fadein" data-anim-timeout="0.3" data-tooltip-hook="#superfoods" data-popup-rec-ids="1118329261"
role="dialog"
aria-modal="true"
tabindex="-1"> <div class="t-popup__container t-width t-valign_middle"> </div> </div> <div class="t-popup__bg"></div> </div> <style> #rec1118329261[data-record-type="396"]{display:none;}.t1093 .t-popup #rec1118329261[data-record-type="396"]{display:block;}</style> <style>#rec1118363251 .t1093 .t-popup__bg{-webkit-backdrop-filter:blur(3px);backdrop-filter:blur(3px);}</style> <style>#rec1118363251 .t1093 .t-popup.t-popup-anim-fadein .t-popup__container{transition-timing-function:ease-in-out;}</style> <script>t_onReady(function() {t_onFuncLoad('t1093__init',function() {t1093__init('1118363251');});t_onFuncLoad('t1093__initPopup',function() {t1093__initPopup('1118363251');});});</script> </div> <div id="rec1118329261" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1118329261 .t396__artboard {height:750px;}#rec1118329261 .t396__filter {height:750px;}#rec1118329261 .t396__carrier{height:750px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1118329261 .t396__artboard,#rec1118329261 .t396__filter,#rec1118329261 .t396__carrier {}#rec1118329261 .t396__filter {}#rec1118329261 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1118329261 .t396__artboard,#rec1118329261 .t396__filter,#rec1118329261 .t396__carrier {height:750px;}#rec1118329261 .t396__filter {}#rec1118329261 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1118329261 .t396__artboard,#rec1118329261 .t396__filter,#rec1118329261 .t396__carrier {height:909px;}#rec1118329261 .t396__filter {}#rec1118329261 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1118329261 .t396__artboard,#rec1118329261 .t396__filter,#rec1118329261 .t396__carrier {height:1131px;}#rec1118329261 .t396__filter {}#rec1118329261 .t396__carrier {background-attachment:scroll;}}#rec1118329261 .tn-elem[data-elem-id="1750518592648"]{z-index:3;top:59px;left:calc(50% - 600px + 205px);width:774px;height:630px;}#rec1118329261 .tn-elem[data-elem-id="1750518592648"] .tn-atom{border-width:1px;border-radius:30px 30px 30px 30px;background-color:#f5f0e7;background-position:center center;border-color:#dfd9cf ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518592648"] {display:table;left:calc(50% - 480px + 93px);}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518592648"] {display:table;left:calc(50% - 320px + 15px);width:610px;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518592648"] {display:table;left:calc(50% - 207px + 14px);width:384px;height:789px;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518592648"] {display:table;width:361px;height:1011px;}}#rec1118329261 .tn-elem[data-elem-id="1750519015004"]{z-index:3;top:119px;left:calc(50% - 600px + 757px);width:243px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750519015004"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750519015004"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750519015004"] {display:table;top:138px;left:calc(50% - 480px + 648px);height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750519015004"] {display:table;top:229px;left:calc(50% - 320px + 466px);width:221px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750519015004"] {display:table;top:119px;left:calc(50% - 207px + 212px);width:168px;height:auto;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750519015004"] {display:table;top:119px;left:calc(50% - 195px + 222px);height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750518904464"]{z-index:3;top:374px;left:calc(50% - 600px + 418px);width:227px;height:248px;}#rec1118329261 .tn-elem[data-elem-id="1750518904464"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518904464"] {display:table;top:381px;left:calc(50% - 480px + 315px);width:213px;height:233px;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518904464"] {display:table;top:424px;left:calc(50% - 320px + 205px);width:177px;height:194px;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518904464"] {display:table;top:668px;left:calc(50% - 207px + 136px);width:119px;height:131px;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518904464"] {display:table;top:632px;left:calc(50% - 195px + 230px);width:134px;height:147px;}#rec1118329261 .tn-elem[data-elem-id="1750518904464"] .tn-atom {background-size:cover;-webkit-transform:rotate(22deg);-moz-transform:rotate(22deg);transform:rotate(22deg);}}#rec1118329261 .tn-elem[data-elem-id="1750502694237"]{z-index:3;top:501px;left:calc(50% - 600px + 1454px);width:654px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750502694237"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750502694237"] .tn-atom {-webkit-transform:rotate(270deg);-moz-transform:rotate(270deg);transform:rotate(270deg);}#rec1118329261 .tn-elem[data-elem-id="1750502694237"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750502694237"] {display:table;height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750502694182"]{color:#1a1919;text-align:LEFT;z-index:3;top:109px;left:calc(50% - 600px + 254px);width:269px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750502694182"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750502694182"] {display:table;left:calc(50% - 480px + 143px);width:px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750502694182"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750502694182"] {display:table;top:126px;left:calc(50% - 320px + 45px);width:265px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750502694182"] {display:table;top:110px;left:calc(50% - 207px + 34px);width:264px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750502694182"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750502694182"] {display:table;top:119px;left:calc(50% - 195px + 43px);height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750518781615"]{color:#1a1919;text-align:LEFT;z-index:3;top:109px;left:calc(50% - 600px + 615px);width:257px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781615"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518781615"] {display:table;left:calc(50% - 480px + 505px);width:px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781615"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518781615"] {display:table;top:125px;left:calc(50% - 320px + 364px);width:222px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518781615"] {display:table;top:525px;left:calc(50% - 207px + 34px);width:195px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781615"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518781615"] {display:table;top:528px;left:calc(50% - 195px + 42px);height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750518722815"]{color:#1a1919;text-align:LEFT;z-index:3;top:170px;left:calc(50% - 600px + 254px);width:231px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518722815"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518722815"] {display:table;left:calc(50% - 480px + 143px);width:386px;height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518722815"] {display:table;top:187px;left:calc(50% - 320px + 45px);width:382px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518722815"] {display:table;top:160px;left:calc(50% - 207px + 34px);width:263px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518722815"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518722815"] {display:table;top:168px;left:calc(50% - 195px + 43px);width:271px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518722815"] .tn-atom{font-size:13px;background-size:cover;}}#rec1118329261 .tn-elem[data-elem-id="1750518920534"]{z-index:3;top:521px;left:calc(50% - 600px + 258px);width:180px;height:193px;}#rec1118329261 .tn-elem[data-elem-id="1750518920534"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750518920534"] .tn-atom {-webkit-transform:rotate(97deg);-moz-transform:rotate(97deg);transform:rotate(97deg);}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518920534"] {display:table;top:525px;left:calc(50% - 480px + 146px);width:166px;height:179px;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518920534"] {display:table;top:538px;left:calc(50% - 320px + 72px);width:153px;height:165px;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518920534"] {display:table;top:725px;left:calc(50% - 207px + 62px);width:120px;height:130px;}#rec1118329261 .tn-elem[data-elem-id="1750518920534"] .tn-atom {background-size:cover;-webkit-transform:rotate(27deg);-moz-transform:rotate(27deg);transform:rotate(27deg);}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518920534"] {display:table;top:848px;left:calc(50% - 195px + 283px);width:116px;height:160px;}}#rec1118329261 .tn-elem[data-elem-id="1750518880213"]{z-index:3;top:537px;left:calc(50% - 600px + 147px);width:213px;height:213px;}#rec1118329261 .tn-elem[data-elem-id="1750518880213"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750518880213"] .tn-atom {-webkit-transform:rotate(117deg);-moz-transform:rotate(117deg);transform:rotate(117deg);}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518880213"] {display:table;top:539px;left:calc(50% - 480px + 21px);width:229px;height:229px;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518880213"] {display:table;top:570px;left:calc(50% - 320px + -24px);width:204px;height:204px;}#rec1118329261 .tn-elem[data-elem-id="1750518880213"] .tn-atom {background-size:cover;-webkit-transform:rotate(89deg);-moz-transform:rotate(89deg);transform:rotate(89deg);}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518880213"] {display:table;top:734px;left:calc(50% - 207px + -37px);width:183px;height:183px;}#rec1118329261 .tn-elem[data-elem-id="1750518880213"] .tn-atom {background-size:cover;-webkit-transform:rotate(185deg);-moz-transform:rotate(185deg);transform:rotate(185deg);}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518880213"] {display:table;top:948px;left:calc(50% - 195px + 190px);width:209px;height:179px;}}#rec1118329261 .tn-elem[data-elem-id="1750518817313"]{color:#1a1919;text-align:LEFT;z-index:3;top:344px;left:calc(50% - 600px + 615px);width:174px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817313"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518817313"] {display:table;left:calc(50% - 480px + 505px);width:px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817313"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518817313"] {display:table;top:361px;left:calc(50% - 320px + 364px);width:165px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817313"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518817313"] {display:table;top:525px;left:calc(50% - 207px + 233px);width:134px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817313"] .tn-atom{font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518817313"] {display:table;top:746px;left:calc(50% - 195px + 42px);width:134px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817313"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}#rec1118329261 .tn-elem[data-elem-id="1750518781622"]{color:#1a1919;text-align:LEFT;z-index:3;top:170px;left:calc(50% - 600px + 615px);width:231px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781622"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518781622"] {display:table;top:170px;left:calc(50% - 480px + 505px);width:386px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781622"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518781622"] {display:table;top:187px;left:calc(50% - 320px + 364px);width:382px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518781622"] {display:table;top:575px;left:calc(50% - 207px + 33px);width:263px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781622"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518781622"] {display:table;top:577px;left:calc(50% - 195px + 42px);width:200px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518781622"] .tn-atom{font-size:13px;background-size:cover;}}#rec1118329261 .tn-elem[data-elem-id="1750518817319"]{color:#1a1919;text-align:LEFT;z-index:3;top:405px;left:calc(50% - 600px + 615px);width:231px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817319"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518817319"] {display:table;left:calc(50% - 480px + 505px);width:386px;height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518817319"] {display:table;top:424px;left:calc(50% - 320px + 364px);width:382px;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518817319"] {display:table;top:575px;left:calc(50% - 207px + 232px);width:263px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817319"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518817319"] {display:table;top:795px;left:calc(50% - 195px + 43px);width:271px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518817319"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}#rec1118329261 .tn-elem[data-elem-id="1750518960937"]{z-index:3;top:363px;left:calc(50% - 600px + 821px);width:169px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750518960937"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750518960937"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750518960937"] {display:table;top:386px;left:calc(50% - 480px + 711px);width:156px;height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750518960937"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750518960937"] {display:table;top:286px;left:calc(50% - 207px + 297px);width:105px;height:auto;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750518960937"] {display:table;top:343px;left:calc(50% - 195px + 259px);width:116px;height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750519089501"]{z-index:3;top:85px;left:calc(50% - 600px + 934px);width:20px;height:auto;}#rec1118329261 .tn-elem[data-elem-id="1750519089501"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1118329261 .tn-elem[data-elem-id="1750519089501"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750519089501"] {display:table;left:calc(50% - 480px + 821px);height:auto;}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750519089501"] {display:table;left:calc(50% - 320px + 580px);height:auto;}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750519089501"] {display:table;left:calc(50% - 207px + 353px);height:auto;}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750519089501"] {display:table;left:calc(50% - 195px + 330px);height:auto;}}#rec1118329261 .tn-elem[data-elem-id="1750519870311"]{z-index:3;top:60px;left:calc(50% - 600px + 916px);width:63px;height:63px;}#rec1118329261 .tn-elem[data-elem-id="1750519870311"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1118329261 .tn-elem[data-elem-id="1750519870311"] {display:table;left:calc(50% - 480px + 804px);}}@media screen and (max-width:959px) {#rec1118329261 .tn-elem[data-elem-id="1750519870311"] {display:table;left:calc(50% - 320px + 562px);}}@media screen and (max-width:639px) {#rec1118329261 .tn-elem[data-elem-id="1750519870311"] {display:table;left:calc(50% - 207px + 335px);}}@media screen and (max-width:413px) {#rec1118329261 .tn-elem[data-elem-id="1750519870311"] {display:table;left:calc(50% - 195px + 312px);}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1118329261" data-artboard-screens="390,414,640,960,1200" data-artboard-height="750" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-height-res-390="1131" data-artboard-height-res-414="909" data-artboard-height-res-640="750"> <div class="t396__carrier" data-artboard-recid="1118329261"></div> <div class="t396__filter" data-artboard-recid="1118329261"></div> <div class='t396__elem tn-elem tn-elem__11183292611750518592648' data-elem-id='1750518592648' data-elem-type='shape' data-field-top-value="59" data-field-left-value="205" data-field-height-value="630" data-field-width-value="774" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="fixed" data-field-height-res-390-value="1011" data-field-width-res-390-value="361" data-field-widthmode-res-390-value="fixed" data-field-left-res-414-value="14" data-field-height-res-414-value="789" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-left-res-640-value="15" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-left-res-960-value="93"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750519015004' data-elem-id='1750519015004' data-elem-type='image' data-field-top-value="119" data-field-left-value="757" data-field-height-value="267" data-field-width-value="243" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="110" data-field-filewidth-value="305" data-field-fileheight-value="335" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="119" data-field-left-res-390-value="222" data-field-height-res-390-value="185" data-field-top-res-414-value="119" data-field-left-res-414-value="212" data-field-height-res-414-value="185" data-field-width-res-414-value="168" data-field-top-res-640-value="229" data-field-left-res-640-value="466" data-field-height-res-640-value="243" data-field-width-res-640-value="221" data-field-top-res-960-value="138" data-field-left-res-960-value="648" data-field-height-res-960-value="267"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3733-6333-4664-a666-653861323533/Clip_path_group-Phot.png'
src='../thb.tildacdn.one/tild3733-6333-4664-a666-653861323533/-/resize/20x/Clip_path_group-Phot.png'
alt='' imgfield='tn_img_1750519015004'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518904464' data-elem-id='1750518904464' data-elem-type='shape' data-field-top-value="374" data-field-left-value="418" data-field-height-value="248" data-field-width-value="227" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="632" data-field-left-res-390-value="230" data-field-height-res-390-value="147" data-field-width-res-390-value="134" data-field-top-res-414-value="668" data-field-left-res-414-value="136" data-field-height-res-414-value="131" data-field-width-res-414-value="119" data-field-top-res-640-value="424" data-field-left-res-640-value="205" data-field-height-res-640-value="194" data-field-width-res-640-value="177" data-field-top-res-960-value="381" data-field-left-res-960-value="315" data-field-height-res-960-value="233" data-field-width-res-960-value="213"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6135-6435-4234-a439-643730616364/Group_1.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750502694237' data-elem-id='1750502694237' data-elem-type='vector' data-field-top-value="501" data-field-left-value="1454" data-field-height-value="115" data-field-width-value="654" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-filewidth-value="654" data-field-fileheight-value="115" data-field-heightmode-value="hug"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 654.28 115.11" data-guides="{&quot;vertical&quot;:[],&quot;horizontal&quot;:[]}"> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg1348d0bf835" title="Line 43" x1="0.9999364870254794" y1="0.9998773810930288" x2="653.2825613350778" y2="0.9998773810930288"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg4395c90afd" title="Line 44" x1="653.2825613350778" y1="0.9998773810930288" x2="653.2825613350778" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvge68baf3a58" title="Line 45" x1="107.21328903698395" y1="0.9998773810930288" x2="107.21328903698395" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg6a9161475b" title="Line 46" x1="242.21328903698395" y1="0.9998773810930288" x2="242.21328903698395" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvg13717f7fb96" title="Line 47" x1="387.2109375" y1="0.9998773810930288" x2="387.2109375" y2="114.11044275851509"></line> <line fill="transparent" fill-opacity="1" stroke="#8191e2" stroke-opacity="1" stroke-width="1" id="tSvgde4373fff2" title="Line 48" x1="520.213289036984" y1="0.9998773810930288" x2="520.213289036984" y2="114.11044275851509"></line> <defs></defs> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750502694182 t-animate' data-elem-id='1750502694182' data-elem-type='text' data-field-top-value="109" data-field-left-value="254" data-field-height-value="42" data-field-width-value="269" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="119" data-field-left-res-390-value="43" data-field-top-res-414-value="110" data-field-left-res-414-value="34" data-field-width-res-414-value="264" data-field-top-res-640-value="126" data-field-left-res-640-value="45" data-field-width-res-640-value="265" data-field-left-res-960-value="143" data-field-heightunits-res-960-value="px" data-field-textfit-res-960-value="autoheight"> <div class='tn-atom'field='tn_text_1750502694182'>Superfood Powders &amp; Nutrient Boosters</div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518781615 t-animate' data-elem-id='1750518781615' data-elem-type='text' data-field-top-value="109" data-field-left-value="615" data-field-height-value="42" data-field-width-value="257" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="528" data-field-left-res-390-value="42" data-field-top-res-414-value="525" data-field-left-res-414-value="34" data-field-width-res-414-value="195" data-field-top-res-640-value="125" data-field-left-res-640-value="364" data-field-width-res-640-value="222" data-field-left-res-960-value="505" data-field-heightunits-res-960-value="px" data-field-textfit-res-960-value="autoheight"> <div class='tn-atom'field='tn_text_1750518781615'><strong>Sweeteners &amp; Flavor Enhancers</strong></div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518722815 t-animate' data-elem-id='1750518722815' data-elem-type='text' data-field-top-value="170" data-field-left-value="254" data-field-height-value="336" data-field-width-value="231" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="168" data-field-left-res-390-value="43" data-field-width-res-390-value="271" data-field-top-res-414-value="160" data-field-left-res-414-value="34" data-field-width-res-414-value="263" data-field-top-res-640-value="187" data-field-left-res-640-value="45" data-field-width-res-640-value="382" data-field-left-res-960-value="143" data-field-width-res-960-value="386"> <div class='tn-atom'field='tn_text_1750518722815'><ul><li data-list="bullet">Spirulina Powder</li><li data-list="bullet">Chlorella Powder </li><li data-list="bullet">Maca Powder</li><li data-list="bullet">Cacao Powder</li><li data-list="bullet">Matcha Powder</li><li data-list="bullet">Camu Camu Powder </li><li data-list="bullet">Acai Powder</li><li data-list="bullet">Reishi Mushroom Powder</li><li data-list="bullet">Shiitake Mushroom Powder</li><li data-list="bullet">Maitake Mushroom Powder</li><li data-list="bullet">Turkey Tail Mushroom Powder </li><li data-list="bullet">Cordyceps Powder </li><li data-list="bullet">Ashwagandha Powder </li><li data-list="bullet">Beetroot Powder</li><li data-list="bullet">Turmeric Powder</li><li data-list="bullet">Ginger Powder</li><li data-list="bullet">Cinnamon</li><li data-list="bullet">Blue Spirulina</li><li data-list="bullet">Baobab Powder </li><li data-list="bullet">Lucuma Powder</li></ul></div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518920534' data-elem-id='1750518920534' data-elem-type='shape' data-field-top-value="521" data-field-left-value="258" data-field-height-value="193" data-field-width-value="180" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="85" data-field-top-res-390-value="848" data-field-left-res-390-value="283" data-field-height-res-390-value="160" data-field-width-res-390-value="116" data-field-top-res-414-value="725" data-field-left-res-414-value="62" data-field-height-res-414-value="130" data-field-width-res-414-value="120" data-field-top-res-640-value="538" data-field-left-res-640-value="72" data-field-height-res-640-value="165" data-field-width-res-640-value="153" data-field-top-res-960-value="525" data-field-left-res-960-value="146" data-field-height-res-960-value="179" data-field-width-res-960-value="166"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6332-6434-4835-a130-653633376236/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518880213' data-elem-id='1750518880213' data-elem-type='shape' data-field-top-value="537" data-field-left-value="147" data-field-height-value="213" data-field-width-value="213" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="90" data-animate-mobile="y" data-field-top-res-390-value="948" data-field-left-res-390-value="190" data-field-height-res-390-value="179" data-field-width-res-390-value="209" data-field-top-res-414-value="734" data-field-left-res-414-value="-37" data-field-height-res-414-value="183" data-field-width-res-414-value="183" data-field-top-res-640-value="570" data-field-left-res-640-value="-24" data-field-height-res-640-value="204" data-field-width-res-640-value="204" data-field-top-res-960-value="539" data-field-left-res-960-value="21" data-field-height-res-960-value="229" data-field-width-res-960-value="229"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3232-6265-4533-a363-313539353736/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518817313 t-animate' data-elem-id='1750518817313' data-elem-type='text' data-field-top-value="344" data-field-left-value="615" data-field-height-value="42" data-field-width-value="174" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="746" data-field-left-res-390-value="42" data-field-width-res-390-value="134" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="525" data-field-left-res-414-value="233" data-field-width-res-414-value="134" data-field-top-res-640-value="361" data-field-left-res-640-value="364" data-field-width-res-640-value="165" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-left-res-960-value="505" data-field-heightunits-res-960-value="px" data-field-textfit-res-960-value="autoheight"> <div class='tn-atom'field='tn_text_1750518817313'><strong>Nuts, Seeds &amp; Butters</strong></div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518781622 t-animate' data-elem-id='1750518781622' data-elem-type='text' data-field-top-value="170" data-field-left-value="615" data-field-height-value="134" data-field-width-value="231" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="577" data-field-left-res-390-value="42" data-field-width-res-390-value="200" data-field-top-res-414-value="575" data-field-left-res-414-value="33" data-field-height-res-414-value="128" data-field-width-res-414-value="263" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="187" data-field-left-res-640-value="364" data-field-width-res-640-value="382" data-field-top-res-960-value="170" data-field-left-res-960-value="505" data-field-height-res-960-value="134" data-field-width-res-960-value="386" data-field-container-res-960-value="grid" data-field-heightunits-res-960-value="px" data-field-textfit-res-960-value="autoheight"> <div class='tn-atom'field='tn_text_1750518781622'><ul><li data-list="bullet">Raw Honey</li><li data-list="bullet">Blackstrap Molasses</li><li data-list="bullet">Maple Syrup</li><li data-list="bullet">Coconut Sugar</li><li data-list="bullet">Medjool Dates</li><li data-list="bullet">Vanilla Extract</li><li data-list="bullet">Cacao Nibs</li><li data-list="bullet">Mint Leaves</li></ul></div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518817319 t-animate' data-elem-id='1750518817319' data-elem-type='text' data-field-top-value="405" data-field-left-value="615" data-field-height-value="235" data-field-width-value="231" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-field-textfit-value="autoheight" data-field-top-res-390-value="795" data-field-left-res-390-value="43" data-field-width-res-390-value="271" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="575" data-field-left-res-414-value="232" data-field-height-res-414-value="224" data-field-width-res-414-value="263" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="424" data-field-left-res-640-value="364" data-field-width-res-640-value="382" data-field-left-res-960-value="505" data-field-width-res-960-value="386"> <div class='tn-atom'field='tn_text_1750518817319'><ul><li data-list="bullet">Almonds</li><li data-list="bullet">Cashews</li><li data-list="bullet">Walnuts</li><li data-list="bullet">Brazil Nuts </li><li data-list="bullet">Hazelnuts</li><li data-list="bullet">Pumpkin Seeds </li><li data-list="bullet">Sunflower Seeds </li><li data-list="bullet">Chia Seeds</li><li data-list="bullet">Flaxseeds</li><li data-list="bullet">Hemp Seeds</li><li data-list="bullet">Sesame Seeds / Tahini</li><li data-list="bullet">Almond Butter</li><li data-list="bullet">Cashew Butter</li><li data-list="bullet">Peanut Butter</li></ul></div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750518960937' data-elem-id='1750518960937' data-elem-type='image' data-field-top-value="363" data-field-left-value="821" data-field-height-value="339" data-field-width-value="169" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="383" data-field-fileheight-value="768" data-field-heightmode-value="hug" data-field-top-res-390-value="343" data-field-left-res-390-value="259" data-field-height-res-390-value="233" data-field-width-res-390-value="116" data-field-top-res-414-value="286" data-field-left-res-414-value="297" data-field-height-res-414-value="211" data-field-width-res-414-value="105" data-field-height-res-640-value="313" data-field-top-res-960-value="386" data-field-left-res-960-value="711" data-field-height-res-960-value="313" data-field-width-res-960-value="156"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild6161-3534-4763-b264-356533366433/Rectangle.png'
src='../thb.tildacdn.one/tild6161-3534-4763-b264-356533366433/-/resize/20x/Rectangle.png'
alt='' imgfield='tn_img_1750518960937'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750519089501' data-elem-id='1750519089501' data-elem-type='image' data-field-top-value="85" data-field-left-value="934" data-field-height-value="20" data-field-width-value="20" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="27" data-field-fileheight-value="27" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-left-res-390-value="330" data-field-height-res-390-value="20" data-field-left-res-414-value="353" data-field-height-res-414-value="20" data-field-left-res-640-value="580" data-field-height-res-640-value="20" data-field-left-res-960-value="821" data-field-height-res-960-value="20"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3963-6435-4861-a665-626633366430/Group_188.svg'
src='https://static.tildacdn.one/tild3963-6435-4861-a665-626633366430/Group_188.svg'
alt='' imgfield='tn_img_1750519089501'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11183292611750519870311' data-elem-id='1750519870311' data-elem-type='shape' data-field-top-value="60" data-field-left-value="916" data-field-height-value="63" data-field-width-value="63" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="312" data-field-left-res-414-value="335" data-field-left-res-640-value="562" data-field-left-res-960-value="804"> <a class='tn-atom' href="#closepopup"> </a> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1118329261');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1118329261');});});</script> <!-- /T396 --> </div> <div id="rec1086154816" class="r t-rec" style=" " data-animationappear="off" data-record-type="121"> <!-- T396 --> <style>#rec1086154816 .t396__artboard {height:777px;background-color:#f5f5f5;overflow:visible;}#rec1086154816 .t396__filter {height:777px;}#rec1086154816 .t396__carrier{height:777px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1086154816 .t396__artboard,#rec1086154816 .t396__filter,#rec1086154816 .t396__carrier {}#rec1086154816 .t396__filter {}#rec1086154816 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1086154816 .t396__artboard,#rec1086154816 .t396__filter,#rec1086154816 .t396__carrier {height:977px;}#rec1086154816 .t396__filter {}#rec1086154816 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1086154816 .t396__artboard,#rec1086154816 .t396__filter,#rec1086154816 .t396__carrier {height:1102px;}#rec1086154816 .t396__filter {}#rec1086154816 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1086154816 .t396__artboard,#rec1086154816 .t396__filter,#rec1086154816 .t396__carrier {height:1061px;}#rec1086154816 .t396__filter {}#rec1086154816 .t396__carrier {background-attachment:scroll;}}#rec1086154816 .tn-elem[data-elem-id="1749214032154"]{z-index:3;top:144px;left:calc(50% - 600px + 949px);width:397px;height:445px;}#rec1086154816 .tn-elem[data-elem-id="1749214032154"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032154"] {display:table;top:211px;left:calc(50% - 480px + 732px);width:350px;height:393px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032154"] {display:table;top:600px;left:calc(50% - 320px + -143px);width:304px;height:342px;}#rec1086154816 .tn-elem[data-elem-id="1749214032154"] .tn-atom {background-size:cover;-webkit-transform:rotate(48deg);-moz-transform:rotate(48deg);transform:rotate(48deg);}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032154"] {display:table;top:443px;left:calc(50% - 207px + -61px);width:223px;height:251px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032154"] {display:table;top:437px;left:calc(50% - 195px + -88px);width:216px;height:243px;}#rec1086154816 .tn-elem[data-elem-id="1749214032154"] .tn-atom {background-size:cover;-webkit-transform:rotate(59deg);-moz-transform:rotate(59deg);transform:rotate(59deg);}}#rec1086154816 .tn-elem[data-elem-id="1749214032159"]{z-index:3;top:454px;left:calc(50% - 600px + 156px);width:218px;height:218px;}#rec1086154816 .tn-elem[data-elem-id="1749214032159"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032159"] {display:table;top:478px;left:calc(50% - 480px + 92px);width:199px;height:199px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032159"] {display:table;top:600px;left:calc(50% - 320px + 75px);}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032159"] {display:table;top:354px;left:calc(50% - 207px + 128px);width:120px;height:120px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032159"] {display:table;top:386px;left:calc(50% - 195px + 128px);}}#rec1086154816 .tn-elem[data-elem-id="1749214032162"]{color:#1a1919;text-align:CENTER;z-index:3;top:636px;left:calc(50% - 600px + 469px);width:260px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032162"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:25px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-0.5px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032162"] {display:table;left:calc(50% - 480px + 350px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032162"] {display:table;top:816px;left:calc(50% - 320px + 190px);height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032162"] {display:table;top:971px;left:calc(50% - 207px + 101px);width:212px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032162"] .tn-atom {vertical-align:middle;white-space:normal;font-size:20px;background-size:cover;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032162"] {display:table;top:940px;left:calc(50% - 195px + 89px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032169"]{color:#8091e2;text-align:CENTER;z-index:3;top:110px;left:calc(50% - 600px + 393px);width:412px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032169"] .tn-atom {vertical-align:middle;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-0.5px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032169"] {display:table;left:calc(50% - 480px + 274px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032169"] {display:table;left:calc(50% - 320px + 130px);width:381px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032169"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032169"] {display:table;left:calc(50% - 207px + 55px);width:305px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032169"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032169"] {display:table;left:calc(50% - 195px + 43px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032175"]{color:#f5f5f5;text-align:center;z-index:3;top:727px;left:calc(50% - 600px + 522px);width:156px;height:40px;}#rec1086154816 .tn-elem[data-elem-id="1749214032175"] .tn-atom{color:#f5f5f5;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1086154816 .tn-elem[data-elem-id="1749214032175"] .tn-atom:hover {background-color:#6377d4;background-image:none;}}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032175"] {display:table;left:calc(50% - 480px + 402px);}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032175"] {display:table;top:907px;left:calc(50% - 320px + 242px);}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032175"] {display:table;top:1052px;left:calc(50% - 207px + 129px);width:156px;height:40px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032175"] {display:table;top:1011px;left:calc(50% - 195px + 117px);}}#rec1086154816 .tn-elem[data-elem-id="1749214032191"]{z-index:3;top:305px;left:calc(50% - 600px + 20px);width:283px;height:123px;}#rec1086154816 .tn-elem[data-elem-id="1749214032191"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032191"] {display:table;width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032191"] {display:table;top:305px;left:calc(50% - 320px + 15px);width:197px;height:135px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032191"] {display:table;top:275px;width:187px;height:136px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032191"] {display:table;width:175px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032194"]{z-index:3;top:305px;left:calc(50% - 600px + 310px);width:283px;height:123px;}#rec1086154816 .tn-elem[data-elem-id="1749214032194"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032194"] {display:table;left:calc(50% - 480px + 252px);width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032194"] {display:table;left:calc(50% - 320px + 222px);width:197px;height:135px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032194"] {display:table;top:275px;left:calc(50% - 207px + 212px);width:187px;height:136px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032194"] {display:table;left:calc(50% - 195px + 200px);width:175px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032197"]{z-index:3;top:305px;left:calc(50% - 600px + 603px);width:283px;height:123px;}#rec1086154816 .tn-elem[data-elem-id="1749214032197"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032197"] {display:table;left:calc(50% - 480px + 485px);width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032197"] {display:table;top:305px;left:calc(50% - 320px + 429px);width:197px;height:135px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032197"] {display:table;top:421px;left:calc(50% - 207px + 212px);width:187px;height:140px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032197"] {display:table;left:calc(50% - 195px + 200px);width:175px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032200"]{z-index:3;top:439px;left:calc(50% - 600px + 310px);width:283px;height:140px;}#rec1086154816 .tn-elem[data-elem-id="1749214032200"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032200"] {display:table;left:calc(50% - 480px + 252px);width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032200"] {display:table;top:450px;left:calc(50% - 320px + 222px);width:197px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032200"] {display:table;top:571px;left:calc(50% - 207px + 212px);width:187px;height:155px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032200"] {display:table;top:650px;left:calc(50% - 195px + 15px);width:360px;height:70px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032203"]{z-index:3;top:439px;left:calc(50% - 600px + 603px);width:283px;height:140px;}#rec1086154816 .tn-elem[data-elem-id="1749214032203"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032203"] {display:table;left:calc(50% - 480px + 485px);width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032203"] {display:table;top:600px;left:calc(50% - 320px + 222px);width:197px;height:155px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032203"] {display:table;top:736px;left:calc(50% - 207px + 15px);width:187px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032203"] {display:table;top:730px;width:360px;height:70px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032206"]{z-index:3;top:439px;left:calc(50% - 600px + 20px);width:283px;height:140px;}#rec1086154816 .tn-elem[data-elem-id="1749214032206"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032206"] {display:table;width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032206"] {display:table;top:450px;left:calc(50% - 320px + 15px);width:197px;height:140px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032206"] {display:table;top:571px;left:calc(50% - 207px + 15px);width:187px;height:155px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032206"] {display:table;width:360px;height:69px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032210"]{z-index:3;top:439px;left:calc(50% - 600px + 896px);width:283px;height:140px;}#rec1086154816 .tn-elem[data-elem-id="1749214032210"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032210"] {display:table;left:calc(50% - 480px + 717px);width:223px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032210"] {display:table;top:600px;left:calc(50% - 320px + 429px);width:197px;height:155px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032210"] {display:table;top:736px;left:calc(50% - 207px + 212px);width:187px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032210"] {display:table;top:810px;left:calc(50% - 195px + 15px);width:360px;height:80px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032212"]{color:#1a1919;text-align:LEFT;z-index:3;top:396px;left:calc(50% - 600px + 40px);width:232px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032212"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032212"] {display:table;top:398px;left:calc(50% - 480px + 35px);width:200px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032212"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032212"] {display:table;top:395px;left:calc(50% - 320px + 30px);width:142px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032212"] {display:table;top:365px;height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032212"] {display:table;top:363px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032212"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032216"]{color:#1a1919;text-align:LEFT;z-index:3;top:396px;left:calc(50% - 600px + 330px);width:auto;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032216"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032216"] {display:table;top:398px;left:calc(50% - 480px + 267px);width:174px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032216"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032216"] {display:table;top:395px;left:calc(50% - 320px + 237px);width:109px;height:30px;}#rec1086154816 .tn-elem[data-elem-id="1749214032216"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032216"] {display:table;top:365px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032216"] {display:table;top:363px;left:calc(50% - 195px + 215px);height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032216"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032219"]{color:#1a1919;text-align:LEFT;z-index:3;top:396px;left:calc(50% - 600px + 623px);width:191px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032219"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032219"] {display:table;top:398px;left:calc(50% - 480px + 500px);height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032219"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032219"] {display:table;top:395px;left:calc(50% - 320px + 444px);width:131px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032219"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032219"] {display:table;top:516px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032219"] {display:table;top:514px;left:calc(50% - 195px + 215px);height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032219"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032221"]{color:#1a1919;text-align:LEFT;z-index:3;top:529px;left:calc(50% - 600px + 330px);width:216px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032221"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032221"] {display:table;top:519px;left:calc(50% - 480px + 267px);width:185px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032221"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032221"] {display:table;top:530px;left:calc(50% - 320px + 237px);height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032221"] {display:table;top:666px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032221"] {display:table;top:667px;left:calc(50% - 195px + 108px);width:205px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032221"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032224"]{color:#1a1919;text-align:LEFT;z-index:3;top:529px;left:calc(50% - 600px + 623px);width:257px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032224"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032224"] {display:table;top:519px;left:calc(50% - 480px + 500px);width:174px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032224"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032224"] {display:table;top:695px;left:calc(50% - 320px + 237px);height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032224"] {display:table;top:831px;left:calc(50% - 207px + 30px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032224"] {display:table;top:747px;left:calc(50% - 195px + 108px);width:245px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032224"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032227"]{color:#1a1919;text-align:LEFT;z-index:3;top:512px;left:calc(50% - 600px + 40px);width:198px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032227"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032227"] {display:table;top:519px;left:calc(50% - 480px + 35px);height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032227"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032227"] {display:table;top:530px;left:calc(50% - 320px + 30px);width:198px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032227"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032227"] {display:table;top:651px;left:calc(50% - 207px + 30px);width:152px;height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032227"] {display:table;top:588px;left:calc(50% - 195px + 108px);width:240px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032227"] .tn-atom{font-size:18px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032229"]{color:#1a1919;text-align:LEFT;z-index:3;top:512px;left:calc(50% - 600px + 916px);width:238px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032229"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:20px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:400;letter-spacing:-0.2px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032229"] {display:table;top:519px;left:calc(50% - 480px + 732px);width:199px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032229"] .tn-atom{font-size:18px;background-size:cover;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032229"] {display:table;top:680px;left:calc(50% - 320px + 444px);width:167px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032229"] {display:table;top:816px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032229"] {display:table;top:826px;left:calc(50% - 195px + 108px);width:200px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032229"] .tn-atom{font-size:17px;line-height:0.95;background-size:cover;}}#rec1086154816 .tn-elem[data-elem-id="1749214032233"]{z-index:3;top:322px;left:calc(50% - 600px + 1074px);width:191px;height:194px;}#rec1086154816 .tn-elem[data-elem-id="1749214032233"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032233"] {display:table;top:361px;left:calc(50% - 480px + 837px);width:155px;height:158px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032233"] {display:table;top:488px;left:calc(50% - 320px + 519px);}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032233"] {display:table;top:504px;left:calc(50% - 207px + 91px);width:111px;height:112px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032233"] {display:table;top:792px;left:calc(50% - 195px + 289px);width:139px;height:140px;}}#rec1086154816 .tn-elem[data-elem-id="1749214032235"]{z-index:3;top:236px;left:calc(50% - 600px + 837px);width:157px;height:157px;}#rec1086154816 .tn-elem[data-elem-id="1749214032235"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032235"] {display:table;top:225px;left:calc(50% - 480px + 653px);width:151px;height:151px;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032235"] {display:table;top:425px;left:calc(50% - 320px + 365px);width:146px;height:146px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032235"] {display:table;top:204px;left:calc(50% - 207px + 328px);width:127px;height:127px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032235"] {display:table;top:214px;left:calc(50% - 195px + 312px);}}#rec1086154816 .tn-elem[data-elem-id="1749214032238"]{z-index:3;top:459px;left:calc(50% - 600px + 916px);width:26px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032238"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032238"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032238"] {display:table;top:454px;left:calc(50% - 480px + 732px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032238"] {display:table;top:615px;left:calc(50% - 320px + 444px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032238"] {display:table;top:751px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032238"] {display:table;top:825px;left:calc(50% - 195px + 30px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032241"]{z-index:3;top:325px;left:calc(50% - 600px + 623px);width:24px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032241"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032241"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032241"] {display:table;top:320px;left:calc(50% - 480px + 500px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032241"] {display:table;top:320px;left:calc(50% - 320px + 444px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032241"] {display:table;top:436px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032241"] {display:table;left:calc(50% - 195px + 215px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032244"]{z-index:3;top:459px;left:calc(50% - 600px + 330px);width:24px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032244"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032244"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032244"] {display:table;top:454px;left:calc(50% - 480px + 267px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032244"] {display:table;top:465px;left:calc(50% - 320px + 237px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032244"] {display:table;top:586px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032244"] {display:table;top:665px;left:calc(50% - 195px + 30px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032245"]{z-index:3;top:325px;left:calc(50% - 600px + 330px);width:26px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032245"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032245"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032245"] {display:table;top:320px;left:calc(50% - 480px + 267px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032245"] {display:table;left:calc(50% - 320px + 237px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032245"] {display:table;top:290px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032245"] {display:table;left:calc(50% - 195px + 215px);height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032247"]{z-index:3;top:459px;left:calc(50% - 600px + 623px);width:26px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032247"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032247"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032247"] {display:table;top:454px;left:calc(50% - 480px + 500px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032247"] {display:table;top:615px;left:calc(50% - 320px + 237px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032247"] {display:table;top:751px;left:calc(50% - 207px + 30px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032247"] {display:table;top:745px;height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032248"]{z-index:3;top:325px;left:calc(50% - 600px + 40px);width:26px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032248"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032248"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032248"] {display:table;top:320px;left:calc(50% - 480px + 35px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032248"] {display:table;top:320px;left:calc(50% - 320px + 30px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032248"] {display:table;top:290px;height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032248"] {display:table;height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032249"]{z-index:3;top:459px;left:calc(50% - 600px + 40px);width:26px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032249"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032249"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032249"] {display:table;top:454px;left:calc(50% - 480px + 35px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032249"] {display:table;top:465px;left:calc(50% - 320px + 30px);width:24px;height:auto;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032249"] {display:table;top:586px;left:calc(50% - 207px + 30px);height:auto;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032249"] {display:table;height:auto;}}#rec1086154816 .tn-elem[data-elem-id="1749214032177"]{z-index:3;top:191px;left:calc(50% - 600px + 479px);width:238px;height:53px;}#rec1086154816 .tn-elem[data-elem-id="1749214032177"] .tn-atom {border-radius:14px 14px 14px 14px;background-color:#f47373;background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032177"] .tn-atom {-webkit-transform:rotate(358deg);-moz-transform:rotate(358deg);transform:rotate(358deg);}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032177"] {display:table;left:calc(50% - 480px + 361px);}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032177"] {display:table;left:calc(50% - 320px + 211px);width:218px;height:48px;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032177"] {display:table;top:181px;left:calc(50% - 207px + 110px);width:195px;height:43px;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032177"] {display:table;top:176px;left:calc(50% - 195px + 98px);}}#rec1086154816 .tn-elem[data-elem-id="1749214032182"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:198px;left:calc(50% - 600px + 490px);width:221px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032182"] .tn-atom {vertical-align:middle;color:#f5f5f5;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-0.5px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1086154816 .tn-elem[data-elem-id="1749214032182"] .tn-atom {-webkit-transform:rotate(358deg);-moz-transform:rotate(358deg);transform:rotate(358deg);}@media screen and (max-width:1199px) {#rec1086154816 .tn-elem[data-elem-id="1749214032182"] {display:table;left:calc(50% - 480px + 370px);height:auto;}}@media screen and (max-width:959px) {#rec1086154816 .tn-elem[data-elem-id="1749214032182"] {display:table;left:calc(50% - 320px + 222px);width:auto;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032182"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1086154816 .tn-elem[data-elem-id="1749214032182"] {display:table;top:188px;left:calc(50% - 207px + 122px);width:171px;height:auto;}#rec1086154816 .tn-elem[data-elem-id="1749214032182"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1086154816 .tn-elem[data-elem-id="1749214032182"] {display:table;left:calc(50% - 195px + 110px);height:auto;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1086154816" data-artboard-screens="390,414,640,960,1200" data-artboard-height="777" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-ovrflw="visible" data-artboard-height-res-390="1061" data-artboard-height-res-414="1102" data-artboard-height-res-640="977"> <div class="t396__carrier" data-artboard-recid="1086154816"></div> <div class="t396__filter" data-artboard-recid="1086154816"></div> <div class='t396__elem tn-elem tn-elem__10861548161749214032154' data-elem-id='1749214032154' data-elem-type='shape' data-field-top-value="144" data-field-left-value="949" data-field-height-value="445" data-field-width-value="397" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="80" data-field-top-res-390-value="437" data-field-left-res-390-value="-88" data-field-height-res-390-value="243" data-field-width-res-390-value="216" data-field-top-res-414-value="443" data-field-left-res-414-value="-61" data-field-height-res-414-value="251" data-field-width-res-414-value="223" data-field-top-res-640-value="600" data-field-left-res-640-value="-143" data-field-height-res-640-value="342" data-field-width-res-640-value="304" data-field-top-res-960-value="211" data-field-left-res-960-value="732" data-field-height-res-960-value="393" data-field-width-res-960-value="350"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3934-3863-4438-b830-633731353036/Rectangle.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032159' data-elem-id='1749214032159' data-elem-type='shape' data-field-top-value="454" data-field-left-value="156" data-field-height-value="218" data-field-width-value="218" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="80" data-animate-mobile="y" data-field-top-res-390-value="386" data-field-left-res-390-value="128" data-field-top-res-414-value="354" data-field-left-res-414-value="128" data-field-height-res-414-value="120" data-field-width-res-414-value="120" data-field-top-res-640-value="600" data-field-left-res-640-value="75" data-field-top-res-960-value="478" data-field-left-res-960-value="92" data-field-height-res-960-value="199" data-field-width-res-960-value="199"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6331-6634-4336-b836-316465353831/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032162' data-elem-id='1749214032162' data-elem-type='text' data-field-top-value="636" data-field-left-value="469" data-field-height-value="63" data-field-width-value="260" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-top-res-390-value="940" data-field-left-res-390-value="89" data-field-top-res-414-value="971" data-field-left-res-414-value="101" data-field-height-res-414-value="51" data-field-width-res-414-value="212" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="816" data-field-left-res-640-value="190" data-field-left-res-960-value="350"> <div class='tn-atom'field='tn_text_1749214032162'>Get 100% of all your daily required vitamins and more</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032169' data-elem-id='1749214032169' data-elem-type='text' data-field-top-value="110" data-field-left-value="393" data-field-height-value="76" data-field-width-value="412" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-left-res-390-value="43" data-field-left-res-414-value="55" data-field-width-res-414-value="305" data-field-left-res-640-value="130" data-field-width-res-640-value="381" data-field-left-res-960-value="274"> <div class='tn-atom'field='tn_text_1749214032169'>What Makes Multi-Vit Shakes </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032175' data-elem-id='1749214032175' data-elem-type='button' data-field-top-value="727" data-field-left-value="522" data-field-height-value="40" data-field-width-value="156" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="1011" data-field-left-res-390-value="117" data-field-top-res-414-value="1052" data-field-left-res-414-value="129" data-field-height-res-414-value="40" data-field-width-res-414-value="156" data-field-container-res-414-value="grid" data-field-top-res-640-value="907" data-field-left-res-640-value="242" data-field-left-res-960-value="402"> <a class='tn-atom' href="catalog.html#ct">Shop now →</a> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032191 t-animate' data-elem-id='1749214032191' data-elem-type='shape' data-field-top-value="305" data-field-left-value="20" data-field-height-value="123" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-width-res-390-value="175" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="275" data-field-height-res-414-value="136" data-field-width-res-414-value="187" data-field-top-res-640-value="305" data-field-left-res-640-value="15" data-field-height-res-640-value="135" data-field-width-res-640-value="197" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-width-res-960-value="223" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032194 t-animate' data-elem-id='1749214032194' data-elem-type='shape' data-field-top-value="305" data-field-left-value="310" data-field-height-value="123" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-left-res-390-value="200" data-field-width-res-390-value="175" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="275" data-field-left-res-414-value="212" data-field-height-res-414-value="136" data-field-width-res-414-value="187" data-field-left-res-640-value="222" data-field-height-res-640-value="135" data-field-width-res-640-value="197" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-left-res-960-value="252" data-field-width-res-960-value="223" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032197 t-animate' data-elem-id='1749214032197' data-elem-type='shape' data-field-top-value="305" data-field-left-value="603" data-field-height-value="123" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-left-res-390-value="200" data-field-width-res-390-value="175" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="421" data-field-left-res-414-value="212" data-field-height-res-414-value="140" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="305" data-field-left-res-640-value="429" data-field-height-res-640-value="135" data-field-width-res-640-value="197" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-left-res-960-value="485" data-field-width-res-960-value="223" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032200 t-animate' data-elem-id='1749214032200' data-elem-type='shape' data-field-top-value="439" data-field-left-value="310" data-field-height-value="140" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="650" data-field-left-res-390-value="15" data-field-height-res-390-value="70" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="571" data-field-left-res-414-value="212" data-field-height-res-414-value="155" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="450" data-field-left-res-640-value="222" data-field-width-res-640-value="197" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-left-res-960-value="252" data-field-width-res-960-value="223"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032203 t-animate' data-elem-id='1749214032203' data-elem-type='shape' data-field-top-value="439" data-field-left-value="603" data-field-height-value="140" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="730" data-field-height-res-390-value="70" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="736" data-field-left-res-414-value="15" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="600" data-field-left-res-640-value="222" data-field-height-res-640-value="155" data-field-width-res-640-value="197" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-left-res-960-value="485" data-field-width-res-960-value="223"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032206 t-animate' data-elem-id='1749214032206' data-elem-type='shape' data-field-top-value="439" data-field-left-value="20" data-field-height-value="140" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-height-res-390-value="69" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="571" data-field-left-res-414-value="15" data-field-height-res-414-value="155" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="450" data-field-left-res-640-value="15" data-field-height-res-640-value="140" data-field-width-res-640-value="197" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-width-res-960-value="223"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032210 t-animate' data-elem-id='1749214032210' data-elem-type='shape' data-field-top-value="439" data-field-left-value="896" data-field-height-value="140" data-field-width-value="283" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="810" data-field-left-res-390-value="15" data-field-height-res-390-value="80" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="736" data-field-left-res-414-value="212" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="600" data-field-left-res-640-value="429" data-field-height-res-640-value="155" data-field-width-res-640-value="197" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-left-res-960-value="717" data-field-width-res-960-value="223"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032212 t-animate' data-elem-id='1749214032212' data-elem-type='text' data-field-top-value="396" data-field-left-value="40" data-field-height-value="17" data-field-width-value="232" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="363" data-field-top-res-414-value="365" data-field-top-res-640-value="395" data-field-left-res-640-value="30" data-field-width-res-640-value="142" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="398" data-field-left-res-960-value="35" data-field-width-res-960-value="200" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'field='tn_text_1749214032212'>100% whole food-based</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032216 t-animate' data-elem-id='1749214032216' data-elem-type='text' data-field-top-value="396" data-field-left-value="330" data-field-height-value="17" data-field-width-value="194" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-top-res-390-value="363" data-field-left-res-390-value="215" data-field-height-res-390-value="34" data-field-top-res-414-value="365" data-field-left-res-414-value="227" data-field-top-res-640-value="395" data-field-left-res-640-value="237" data-field-height-res-640-value="30" data-field-width-res-640-value="109" data-field-textfit-res-640-value="fixedsize" data-animate-delay-res-640="0.1" data-field-top-res-960-value="398" data-field-left-res-960-value="267" data-field-width-res-960-value="174"> <div class='tn-atom'field='tn_text_1749214032216'>No synthetic nutrients</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032219 t-animate' data-elem-id='1749214032219' data-elem-type='text' data-field-top-value="396" data-field-left-value="623" data-field-height-value="17" data-field-width-value="191" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="514" data-field-left-res-390-value="215" data-field-top-res-414-value="516" data-field-left-res-414-value="227" data-field-top-res-640-value="395" data-field-left-res-640-value="444" data-field-width-res-640-value="131" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-animate-delay-res-640="0.1" data-field-top-res-960-value="398" data-field-left-res-960-value="500"> <div class='tn-atom'field='tn_text_1749214032219'>No lab-made isolates</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032221 t-animate' data-elem-id='1749214032221' data-elem-type='text' data-field-top-value="529" data-field-left-value="330" data-field-height-value="34" data-field-width-value="216" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="667" data-field-left-res-390-value="108" data-field-width-res-390-value="205" data-field-top-res-414-value="666" data-field-left-res-414-value="227" data-field-top-res-640-value="530" data-field-left-res-640-value="237" data-animate-delay-res-640="0.1" data-field-top-res-960-value="519" data-field-left-res-960-value="267" data-field-width-res-960-value="185"> <div class='tn-atom'field='tn_text_1749214032221'>No fillers, preservatives, or artificial anything
</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032224 t-animate' data-elem-id='1749214032224' data-elem-type='text' data-field-top-value="529" data-field-left-value="623" data-field-height-value="34" data-field-width-value="257" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="747" data-field-left-res-390-value="108" data-field-width-res-390-value="245" data-field-top-res-414-value="831" data-field-left-res-414-value="30" data-field-top-res-640-value="695" data-field-left-res-640-value="237" data-animate-delay-res-640="0.1" data-field-top-res-960-value="519" data-field-left-res-960-value="500" data-field-width-res-960-value="174"> <div class='tn-atom'field='tn_text_1749214032224'>Real results backed by real science and real ingredients</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032227 t-animate' data-elem-id='1749214032227' data-elem-type='text' data-field-top-value="512" data-field-left-value="40" data-field-height-value="51" data-field-width-value="198" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="588" data-field-left-res-390-value="108" data-field-width-res-390-value="240" data-field-top-res-414-value="651" data-field-left-res-414-value="30" data-field-width-res-414-value="152" data-field-top-res-640-value="530" data-field-left-res-640-value="30" data-field-width-res-640-value="198" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-animate-delay-res-640="0.1" data-field-top-res-960-value="519" data-field-left-res-960-value="35"> <div class='tn-atom'field='tn_text_1749214032227'>Delicious, functional, and easy to integrate into your life</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032229 t-animate' data-elem-id='1749214032229' data-elem-type='text' data-field-top-value="512" data-field-left-value="916" data-field-height-value="51" data-field-width-value="238" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="826" data-field-left-res-390-value="108" data-field-width-res-390-value="200" data-field-top-res-414-value="816" data-field-left-res-414-value="227" data-field-top-res-640-value="680" data-field-left-res-640-value="444" data-field-width-res-640-value="167" data-animate-delay-res-640="0.1" data-field-top-res-960-value="519" data-field-left-res-960-value="732" data-field-width-res-960-value="199"> <div class='tn-atom'field='tn_text_1749214032229'>Just real food—super—concentrated, delicious, and effective</div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032233' data-elem-id='1749214032233' data-elem-type='shape' data-field-top-value="322" data-field-left-value="1074" data-field-height-value="194" data-field-width-value="191" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="110" data-animate-mobile="y" data-field-top-res-390-value="792" data-field-left-res-390-value="289" data-field-height-res-390-value="140" data-field-width-res-390-value="139" data-field-top-res-414-value="504" data-field-left-res-414-value="91" data-field-height-res-414-value="112" data-field-width-res-414-value="111" data-field-top-res-640-value="488" data-field-left-res-640-value="519" data-field-top-res-960-value="361" data-field-left-res-960-value="837" data-field-height-res-960-value="158" data-field-width-res-960-value="155"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3662-3138-4466-a131-636630373833/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032235' data-elem-id='1749214032235' data-elem-type='shape' data-field-top-value="236" data-field-left-value="837" data-field-height-value="157" data-field-width-value="157" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-prx="scroll" data-animate-prx-s="95" data-animate-mobile="y" data-field-top-res-390-value="214" data-field-left-res-390-value="312" data-field-top-res-414-value="204" data-field-left-res-414-value="328" data-field-height-res-414-value="127" data-field-width-res-414-value="127" data-field-top-res-640-value="425" data-field-left-res-640-value="365" data-field-height-res-640-value="146" data-field-width-res-640-value="146" data-field-top-res-960-value="225" data-field-left-res-960-value="653" data-field-height-res-960-value="151" data-field-width-res-960-value="151"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild3636-3437-4739-b633-353538373364/image-from-rawpixel-.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032238 t-animate' data-elem-id='1749214032238' data-elem-type='vector' data-field-top-value="459" data-field-left-value="916" data-field-height-value="26" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-top-res-390-value="825" data-field-left-res-390-value="30" data-field-top-res-414-value="751" data-field-left-res-414-value="227" data-field-top-res-640-value="615" data-field-left-res-640-value="444" data-field-width-res-640-value="24" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="454" data-field-left-res-960-value="732"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"> <path d="M13 0C5.85 0 0 5.85 0 13C0 20.15 5.85 26 13 26C20.15 26 26 20.15 26 13C26 5.85 20.15 0 13 0ZM9.88 19.76C9.594 19.76 9.204 19.656 8.84 19.5L8.099 21.32L6.617 20.8L6.825 20.293C8.385 16.367 10.179 11.895 16.9 10.4C16.9 10.4 9.1 10.4 6.565 17.615C6.565 17.615 5.2 16.25 5.2 14.69C5.2 13.13 6.76 9.815 10.66 9.035C11.765 8.814 13 8.645 14.222 8.45C17.29 8.034 20.241 7.618 20.8 6.5C20.8 6.5 18.46 19.76 9.88 19.76Z" fill="#6A9050"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032241 t-animate' data-elem-id='1749214032241' data-elem-type='vector' data-field-top-value="325" data-field-left-value="623" data-field-height-value="24" data-field-width-value="24" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-left-res-390-value="215" data-field-top-res-414-value="436" data-field-left-res-414-value="227" data-field-top-res-640-value="320" data-field-left-res-640-value="444" data-field-width-res-640-value="24" data-field-container-res-640-value="grid" data-animate-delay-res-640="0.1" data-field-top-res-960-value="320" data-field-left-res-960-value="500"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25.901611328125 26.103271484375" fill="none"> <path d="M16.836545471191407 18.34098079223633L14.864386822509765 13.696622637939452C14.67069756164551 13.242074264526368 14.512224530029297 12.465548937988281 14.512224530029297 11.970728540039062C14.512224530029297 11.475933047485352 14.81333076171875 11.071095843505859 15.181357791137696 11.071095843505859C15.549359915161133 11.071095843505859 15.850466146850586 10.76687643737793 15.850466146850586 10.396383773803711C15.850466146850586 10.025866204833985 15.44899117126465 9.721646798706056 14.958305068969725 9.721646798706056H10.943555313110352C10.45286921081543 9.721646798706056 10.051394235229493 10.024695651245118 10.051394235229493 10.396383773803711C10.051394235229493 10.768071896362304 10.352500466918945 11.071095843505859 10.720502590942383 11.071095843505859C11.088529620361328 11.071095843505859 11.389635852050782 11.475933047485352 11.389635852050782 11.970728540039062C11.389635852050782 12.465548937988281 11.231162820434571 13.242074264526368 11.037473559570312 13.69722036743164L9.065314910888672 18.342176251220703C8.871600744628907 18.79672462463379 8.713127713012696 19.37201435546875 8.713127713012696 19.61882682495117C8.713127713012696 19.865639294433592 8.913865200805665 20.271647052001953 9.159208251953126 20.518459521484374C9.404576208496094 20.765271990966795 10.006788671875 20.968275869750975 10.497474774169921 20.968275869750975H15.404385607910156C15.894498886108398 20.968275869750975 16.496113619995118 20.767040274047854 16.742627224731446 20.518459521484374C16.98916573486328 20.2698787689209 17.18873266906738 19.86623702392578 17.18873266906738 19.61882682495117C17.18873266906738 19.37141662597656 17.030259637451174 18.7961268951416 16.836545471191407 18.342176251220703V18.34098079223633ZM13.586590600585938 8.38995530090332C13.586590600585938 8.559959530639649 13.653561209106446 8.72299024963379 13.772783337402345 8.84320859375C13.892005465698242 8.963402032470704 14.053666387939453 9.03094546508789 14.222275915527344 9.03094546508789C14.390860537719727 9.03094546508789 14.552546365356445 8.963402032470704 14.671743588256836 8.84320859375C14.790965716552734 8.72299024963379 14.857936325073242 8.559959530639649 14.857936325073242 8.38995530090332C14.85786160888672 8.219951071166992 14.790816284179687 8.05694525756836 14.671544345092775 7.936801629638672C14.552272406005859 7.816633096313477 14.390561672973634 7.74918928527832 14.22197705078125 7.7492640014648435C14.053392428588868 7.7493387176513675 13.891731506347655 7.816931961059571 13.772584094238281 7.937200115966797C13.6534117767334 8.057468270874024 13.586515884399414 8.219951071166992 13.586590600585938 8.38995530090332ZM11.875639739990234 8.488804815673829C11.953469100952148 8.488804815673829 12.028110571289062 8.457623260498048 12.083151495361328 8.402109133911132C12.138192419433594 8.346619912719726 12.169100015258788 8.271355807495118 12.169100015258788 8.192854000854492C12.169100015258788 8.114377099609376 12.138192419433594 8.039112994384764 12.083151495361328 7.983598867797852C12.028110571289062 7.928109646606445 11.953469100952148 7.896928091430664 11.875639739990234 7.896928091430664C11.79781037902832 7.896928091430664 11.723144003295898 7.928109646606445 11.668103079223632 7.983598867797852C11.613087060546874 8.039112994384764 11.582154559326172 8.114377099609376 11.582154559326172 8.192854000854492C11.582154559326172 8.271355807495118 11.613087060546874 8.346619912719726 11.668103079223632 8.402109133911132C11.723144003295898 8.457623260498048 11.79781037902832 8.488804815673829 11.875639739990234 8.488804815673829ZM13.000815698242187 7.009125457763672C13.126687567138672 7.015426522827148 13.25250962524414 6.995875787353516 13.370660821533203 6.9516188995361325C13.488812017822266 6.90736201171875 13.596776907348632 6.839370281982422 13.688055181884765 6.75172819519043C13.779333456420899 6.664086108398437 13.851982495117188 6.558661569213867 13.90159404296875 6.441855264282227C13.951205590820313 6.325024053955079 13.976758526611327 6.199251806640626 13.976658905029296 6.072184478759766C13.976584188842773 5.945092245483398 13.950881820678712 5.8193449035644536 13.901120840454102 5.702588409423828C13.851359860229493 5.585831915283203 13.778586294555664 5.4805069976806635 13.6872083984375 5.392989437866211C13.595830502319336 5.305471878051757 13.487765991210937 5.237604675292969 13.369564984130859 5.193497219848633C13.251363977050781 5.149414669799804 13.125517013549805 5.1300133666992185 12.999645144653321 5.136463864135742C12.763417468261718 5.151307479858398 12.541709637451172 5.256408248901367 12.379675134277344 5.430397341918945C12.217665536499023 5.604361529541016 12.127532910156251 5.834113803100586 12.127682342529297 6.072757302856445C12.127831774902344 6.311425708007812 12.218263265991212 6.541053454589844 12.380497012329101 6.714818399047852C12.542730758666993 6.888583343505859 12.764588021850585 6.9934101531982416 13.000815698242187 7.007954904174805V7.009125457763672Z" fill="#6A9050"></path> <path d="M12.950855474853515 25.190313232421875C19.59927608947754 25.190313232421875 24.98882858276367 19.755706878662107 24.98882858276367 13.051647610473632C24.98882858276367 6.347563436889648 19.59927608947754 0.9128425183105469 12.950855474853515 0.9128425183105469C6.302459765625 0.9128425183105469 0.9128425183105469 6.347563436889648 0.9128425183105469 13.051647610473632C0.9128425183105469 19.755706878662107 6.302459765625 25.190313232421875 12.950855474853515 25.190313232421875Z" stroke="#6A9050" stroke-width="1.82568" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M21.227092739868166 4.706048818969727L4.674792547607422 21.39689772644043" stroke="#6A9050" stroke-width="1.82568" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M21.29443692932129 6.117437582397461L6.141844869995117 21.396872821044923" stroke="#E2EFD9" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.500501290893553 4.706048818969727L4.347909231567383 19.985484057617185" stroke="#E2EFD9" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032244 t-animate' data-elem-id='1749214032244' data-elem-type='vector' data-field-top-value="459" data-field-left-value="330" data-field-height-value="24" data-field-width-value="24" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-top-res-390-value="665" data-field-left-res-390-value="30" data-field-top-res-414-value="586" data-field-left-res-414-value="227" data-field-top-res-640-value="465" data-field-left-res-640-value="237" data-field-width-res-640-value="24" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="454" data-field-left-res-960-value="267"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25.9013671875 26.103271484375" fill="none"> <path d="M13.578841558368389 13.051499684495193C13.925770447716346 13.051499684495193 14.20699952298678 12.767929524113582 14.20699952298678 12.41808673095703C14.20699952298678 12.068268842961238 13.925770447716346 11.78467377741887 13.578841558368389 11.78467377741887C13.231912669020433 11.78467377741887 12.95068359375 12.068268842961238 12.95068359375 12.41808673095703C12.95068359375 12.767929524113582 13.231912669020433 13.051499684495193 13.578841558368389 13.051499684495193Z" fill="#6A9050"></path> <path d="M12.63682875788762 10.517947490985577C12.8102932025616 10.517947490985577 12.950907740196815 10.376137505634015 12.950907740196815 10.201228561636118C12.950907740196815 10.026319617638222 12.8102932025616 9.88450963228666 12.63682875788762 9.88450963228666C12.463364313213642 9.88450963228666 12.322749775578426 10.026319617638222 12.322749775578426 10.201228561636118C12.322749775578426 10.376137505634015 12.463364313213642 10.517947490985577 12.63682875788762 10.517947490985577Z" fill="#6A9050"></path> <path d="M13.264762576059194 9.251096678748498C13.438227020733173 9.251096678748498 13.578841558368389 9.109311598557692 13.578841558368389 8.93437774939904C13.578841558368389 8.759468805401143 13.438227020733173 8.617683725210336 13.264762576059194 8.617683725210336C13.091298131385216 8.617683725210336 12.95068359375 8.759468805401143 12.95068359375 8.93437774939904C12.95068359375 9.109311598557692 13.091298131385216 9.251096678748498 13.264762576059194 9.251096678748498Z" fill="#6A9050"></path> <path d="M8.55370236722506 5.45044462139423V6.71729543363131H9.810043201622596V17.485465074744592C9.810043201622596 18.325416526442307 10.14093316744291 19.130998856295072 10.72996512451172 19.72493713003305C11.318972176419773 20.31890030893179 12.117855018028846 20.65257965275691 12.950857929875301 20.65257965275691C13.783860841721754 20.65257965275691 14.582743683330829 20.31890030893179 15.171750735238883 19.72493713003305C15.760757787146934 19.130998856295072 16.091672658128005 18.325416526442307 16.091672658128005 17.485465074744592V6.71729543363131H17.348013492525542V5.45044462139423H8.55370236722506ZM14.835356728891226 6.71729543363131V14.951763450270432H12.950857929875301C12.950857929875301 14.783778140963042 12.884684917743389 14.62266665602464 12.76688350736178 14.503869039212741C12.649057191819411 14.385071422400841 12.489290585561898 14.318350496732272 12.322699965256911 14.318350496732272C12.156084439791167 14.318350496732272 11.996317833533654 14.385071422400841 11.878516423152044 14.503869039212741C11.760715012770433 14.62266665602464 11.694517095477766 14.783778140963042 11.694517095477766 14.951763450270432H11.066359130859375V6.71729543363131H14.835356728891226Z" fill="#6A9050"></path> <path d="M12.950633783428486 25.19032484788161C19.59904154240535 25.19032484788161 24.988593045748196 19.755719908728967 24.988593045748196 13.051649115459735C24.988593045748196 6.347553417029747 19.59904154240535 0.9128413856858474 12.950633783428486 0.9128413856858474C6.302201119290865 0.9128413856858474 0.9125973151104266 6.347553417029747 0.9125973151104266 13.051649115459735C0.9125973151104266 19.755719908728967 6.302201119290865 25.19032484788161 12.950633783428486 25.19032484788161Z" stroke="#6A9050" stroke-width="1.82568" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M21.226593797889123 4.706054271521936L4.674325096717247 21.396895287146936" stroke="#6A9050" stroke-width="1.82568" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M21.294186404184195 6.117429731633113L6.141612642728366 21.396870381986176" stroke="#E2EFD9" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M19.500242769681492 4.706054271521936L4.347644103064904 19.985494921875" stroke="#E2EFD9" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032245 t-animate' data-elem-id='1749214032245' data-elem-type='vector' data-field-top-value="325" data-field-left-value="330" data-field-height-value="26" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-left-res-390-value="215" data-field-top-res-414-value="290" data-field-left-res-414-value="227" data-field-left-res-640-value="237" data-field-width-res-640-value="24" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="320" data-field-left-res-960-value="267"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"> <path d="M13 0C5.85 0 0 5.85 0 13C0 20.15 5.85 26 13 26C20.15 26 26 20.15 26 13C26 5.85 20.15 0 13 0ZM9.88 19.76C9.594 19.76 9.204 19.656 8.84 19.5L8.099 21.32L6.617 20.8L6.825 20.293C8.385 16.367 10.179 11.895 16.9 10.4C16.9 10.4 9.1 10.4 6.565 17.615C6.565 17.615 5.2 16.25 5.2 14.69C5.2 13.13 6.76 9.815 10.66 9.035C11.765 8.814 13 8.645 14.222 8.45C17.29 8.034 20.241 7.618 20.8 6.5C20.8 6.5 18.46 19.76 9.88 19.76Z" fill="#6A9050"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032247 t-animate' data-elem-id='1749214032247' data-elem-type='vector' data-field-top-value="459" data-field-left-value="623" data-field-height-value="26" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-top-res-390-value="745" data-field-top-res-414-value="751" data-field-left-res-414-value="30" data-field-top-res-640-value="615" data-field-left-res-640-value="237" data-field-width-res-640-value="24" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="454" data-field-left-res-960-value="500"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"> <path d="M13 0C5.85 0 0 5.85 0 13C0 20.15 5.85 26 13 26C20.15 26 26 20.15 26 13C26 5.85 20.15 0 13 0ZM9.88 19.76C9.594 19.76 9.204 19.656 8.84 19.5L8.099 21.32L6.617 20.8L6.825 20.293C8.385 16.367 10.179 11.895 16.9 10.4C16.9 10.4 9.1 10.4 6.565 17.615C6.565 17.615 5.2 16.25 5.2 14.69C5.2 13.13 6.76 9.815 10.66 9.035C11.765 8.814 13 8.645 14.222 8.45C17.29 8.034 20.241 7.618 20.8 6.5C20.8 6.5 18.46 19.76 9.88 19.76Z" fill="#6A9050"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032248 t-animate' data-elem-id='1749214032248' data-elem-type='vector' data-field-top-value="325" data-field-left-value="40" data-field-height-value="26" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-top-res-414-value="290" data-field-top-res-640-value="320" data-field-left-res-640-value="30" data-field-height-res-640-value="26" data-field-width-res-640-value="24" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="320" data-field-left-res-960-value="35"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"> <path d="M13 0C5.85 0 0 5.85 0 13C0 20.15 5.85 26 13 26C20.15 26 26 20.15 26 13C26 5.85 20.15 0 13 0ZM9.88 19.76C9.594 19.76 9.204 19.656 8.84 19.5L8.099 21.32L6.617 20.8L6.825 20.293C8.385 16.367 10.179 11.895 16.9 10.4C16.9 10.4 9.1 10.4 6.565 17.615C6.565 17.615 5.2 16.25 5.2 14.69C5.2 13.13 6.76 9.815 10.66 9.035C11.765 8.814 13 8.645 14.222 8.45C17.29 8.034 20.241 7.618 20.8 6.5C20.8 6.5 18.46 19.76 9.88 19.76Z" fill="#6A9050"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032249 t-animate' data-elem-id='1749214032249' data-elem-type='vector' data-field-top-value="459" data-field-left-value="40" data-field-height-value="26" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="hug" data-field-top-res-414-value="586" data-field-left-res-414-value="30" data-field-top-res-640-value="465" data-field-left-res-640-value="30" data-field-width-res-640-value="24" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-animate-delay-res-640="0.1" data-field-top-res-960-value="454" data-field-left-res-960-value="35"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 26 26" fill="none"> <path d="M13 0C5.85 0 0 5.85 0 13C0 20.15 5.85 26 13 26C20.15 26 26 20.15 26 13C26 5.85 20.15 0 13 0ZM9.88 19.76C9.594 19.76 9.204 19.656 8.84 19.5L8.099 21.32L6.617 20.8L6.825 20.293C8.385 16.367 10.179 11.895 16.9 10.4C16.9 10.4 9.1 10.4 6.565 17.615C6.565 17.615 5.2 16.25 5.2 14.69C5.2 13.13 6.76 9.815 10.66 9.035C11.765 8.814 13 8.645 14.222 8.45C17.29 8.034 20.241 7.618 20.8 6.5C20.8 6.5 18.46 19.76 9.88 19.76Z" fill="#6A9050"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032177' data-elem-id='1749214032177' data-elem-type='shape' data-field-top-value="191" data-field-left-value="479" data-field-height-value="53" data-field-width-value="238" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="176" data-field-left-res-390-value="98" data-field-top-res-414-value="181" data-field-left-res-414-value="110" data-field-height-res-414-value="43" data-field-width-res-414-value="195" data-field-heightmode-res-414-value="fixed" data-field-left-res-640-value="211" data-field-height-res-640-value="48" data-field-width-res-640-value="218" data-field-heightmode-res-640-value="fixed" data-field-left-res-960-value="361"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__10861548161749214032182' data-elem-id='1749214032182' data-elem-type='text' data-field-top-value="198" data-field-left-value="490" data-field-height-value="38" data-field-width-value="221" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-left-res-390-value="110" data-field-top-res-414-value="188" data-field-left-res-414-value="122" data-field-width-res-414-value="171" data-field-left-res-640-value="222" data-field-width-res-640-value="196" data-field-heightunits-res-640-value="px" data-field-widthunits-res-640-value="px" data-field-textfit-res-640-value="autowidth" data-field-left-res-960-value="370"> <div class='tn-atom'field='tn_text_1749214032182'>Different?</div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1086154816');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1086154816');});});</script> <!-- /T396 --> </div> <!--footer--> <footer id="t-footer" class="t-records" data-hook="blocks-collection-content-node" data-tilda-project-id="13569789" data-tilda-page-id="71000079" data-tilda-page-alias="footer" data-tilda-formskey="2a7fbf90afb3bba69f857fbe13569789" data-tilda-cookie="no" data-tilda-lazy="yes" data-tilda-root-zone="one"> <div id="rec1111375791" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1111375791 .t396__artboard {height:680px;background-color:#f5f5f5;overflow:visible;}#rec1111375791 .t396__filter {height:680px;}#rec1111375791 .t396__carrier{height:680px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1111375791 .t396__artboard,#rec1111375791 .t396__filter,#rec1111375791 .t396__carrier {height:639px;}#rec1111375791 .t396__filter {}#rec1111375791 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1111375791 .t396__artboard,#rec1111375791 .t396__filter,#rec1111375791 .t396__carrier {height:745px;}#rec1111375791 .t396__filter {}#rec1111375791 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1111375791 .t396__artboard,#rec1111375791 .t396__filter,#rec1111375791 .t396__carrier {height:1003px;}#rec1111375791 .t396__filter {}#rec1111375791 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1111375791 .t396__artboard,#rec1111375791 .t396__filter,#rec1111375791 .t396__carrier {height:963px;}#rec1111375791 .t396__filter {}#rec1111375791 .t396__carrier {background-attachment:scroll;}}#rec1111375791 .tn-elem[data-elem-id="1749555640389"]{z-index:3;top:109px;left:calc(50% - 600px + 20px);width:580px;height:560px;}#rec1111375791 .tn-elem[data-elem-id="1749555640389"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#cfe9bc;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640389"] {display:table;width:433px;height:520px;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640389"] {display:table;top:605px;left:calc(50% - 320px + 15px);width:610px;height:130px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640389"] {display:table;top:639px;left:calc(50% - 207px + 15px);width:384px;height:354px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640389"] {display:table;width:360px;height:314px;}}#rec1111375791 .tn-elem[data-elem-id="1749555640398"]{z-index:3;top:109px;left:calc(50% - 600px + 600px);width:580px;height:560px;}#rec1111375791 .tn-elem[data-elem-id="1749555640398"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#f5e39c;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640398"] {display:table;left:calc(50% - 480px + 453px);width:486px;height:520px;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640398"] {display:table;top:100px;left:calc(50% - 320px + 15px);width:610px;height:505px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640398"] {display:table;width:384px;height:539px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640398"] {display:table;width:360px;}}#rec1111375791 .tn-elem[data-elem-id="1749555640561"]{z-index:3;top:72px;left:calc(50% - 600px + -79px);width:756px;height:653px;}#rec1111375791 .tn-elem[data-elem-id="1749555640561"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640561"] {display:table;top:102px;left:calc(50% - 480px + -79px);width:620px;height:535px;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640561"] {display:table;top:327px;left:calc(50% - 320px + 265px);width:510px;height:440px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640561"] {display:table;top:583px;left:calc(50% - 207px + -58px);}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640561"] {display:table;top:584px;left:calc(50% - 195px + -40px);width:458px;height:395px;}}#rec1111375791 .tn-elem[data-elem-id="1749555640406"]{z-index:3;top:296px;left:calc(50% - 600px + 640px);width:498px;height:53px;}#rec1111375791 .tn-elem[data-elem-id="1749555640406"] .tn-atom {border-radius:14px 14px 14px 14px;background-color:#f47373;background-position:center center;border-color:transparent ;border-style:solid;}#rec1111375791 .tn-elem[data-elem-id="1749555640406"] .tn-atom {-webkit-transform:rotate(358deg);-moz-transform:rotate(358deg);transform:rotate(358deg);}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640406"] {display:table;top:275px;left:calc(50% - 480px + 471px);width:450px;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640406"] {display:table;top:227px;left:calc(50% - 320px + 95px);}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640406"] {display:table;top:251px;left:calc(50% - 207px + 100px);width:213px;height:77px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640406"] {display:table;top:244px;left:calc(50% - 195px + 88px);}}#rec1111375791 .tn-elem[data-elem-id="1749555640416"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:303px;left:calc(50% - 600px + 649px);width:480px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] .tn-atom {vertical-align:middle;color:#f5f5f5;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] .tn-atom {-webkit-transform:rotate(358deg);-moz-transform:rotate(358deg);transform:rotate(358deg);}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {display:table;top:283px;left:calc(50% - 480px + 485px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {text-align:center;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:40px;background-size:cover;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {display:table;top:235px;left:calc(50% - 320px + 109px);width:421px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {display:table;top:259px;left:calc(50% - 207px + 106px);width:201px;height:60px;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {text-align:center;}#rec1111375791 .tn-elem[data-elem-id="1749555640416"] .tn-atom {vertical-align:middle;white-space:normal;font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640416"] {display:table;left:calc(50% - 195px + 94px);height:auto;}}#rec1111375791 .tn-elem[data-elem-id="1749555640433"]{color:#8091e2;text-align:CENTER;z-index:3;top:168px;left:calc(50% - 600px + 697px);width:386px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640433"] .tn-atom {vertical-align:middle;color:#8091e2;font-size:45px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640433"] {display:table;top:148px;left:calc(50% - 480px + 503px);height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640433"] {display:table;top:149px;left:calc(50% - 320px + 64px);width:513px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640433"] .tn-atom{font-size:40px;background-size:cover;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640433"] {display:table;left:calc(50% - 207px + 54px);width:306px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640433"] {text-align:center;}#rec1111375791 .tn-elem[data-elem-id="1749555640433"] .tn-atom{font-size:35px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640433"] {display:table;left:calc(50% - 195px + 42px);height:auto;}}#rec1111375791 .tn-elem[data-elem-id="1749555640448"]{color:#1a1919;text-align:CENTER;z-index:3;top:382px;left:calc(50% - 600px + 748px);width:282px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640448"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640448"] {display:table;top:352px;left:calc(50% - 480px + 555px);height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640448"] {display:table;top:304px;left:calc(50% - 320px + 179px);height:auto;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640448"] {display:table;top:357px;left:calc(50% - 207px + 66px);width:282px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640448"] .tn-atom {vertical-align:middle;white-space:normal;font-size:12px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640448"] {display:table;left:calc(50% - 195px + 54px);height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640448"] .tn-atom{font-size:13px;background-size:cover;}}#rec1111375791 .tn-elem[data-elem-id="1749555640463"]{z-index:3;top:519px;left:calc(50% - 600px + 678px);width:423px;height:1px;}#rec1111375791 .tn-elem[data-elem-id="1749555640463"] .tn-atom{border-width:0px;border-radius:0px 0px 0px 0px;opacity:0.4;background-color:#8191e2;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640463"] {display:table;top:499px;left:calc(50% - 480px + 485px);}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640463"] {display:table;top:441px;left:calc(50% - 320px + 35px);width:284px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640463"] {display:table;top:477px;left:calc(50% - 207px + 65px);width:284px;height:1px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640463"] {display:table;left:calc(50% - 195px + 53px);}}#rec1111375791 .tn-elem[data-elem-id="1749555640473"]{z-index:3;top:551px;left:calc(50% - 600px + 678px);width:423px;height:1px;}#rec1111375791 .tn-elem[data-elem-id="1749555640473"] .tn-atom{border-width:0px;border-radius:0px 0px 0px 0px;opacity:0.4;background-color:#8191e2;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640473"] {display:table;top:531px;left:calc(50% - 480px + 485px);}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640473"] {display:table;top:473px;left:calc(50% - 320px + 35px);width:284px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640473"] {display:table;top:509px;left:calc(50% - 207px + 65px);width:284px;height:1px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640473"] {display:table;left:calc(50% - 195px + 53px);}}#rec1111375791 .tn-elem[data-elem-id="1749555640480"]{z-index:3;top:583px;left:calc(50% - 600px + 678px);width:423px;height:1px;}#rec1111375791 .tn-elem[data-elem-id="1749555640480"] .tn-atom{border-width:0px;border-radius:0px 0px 0px 0px;opacity:0.4;background-color:#8191e2;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640480"] {display:table;top:563px;left:calc(50% - 480px + 485px);}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640480"] {display:table;top:522px;left:calc(50% - 320px + 35px);width:284px;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640480"] {display:table;top:558px;left:calc(50% - 207px + 65px);width:284px;height:1px;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640480"] {display:table;left:calc(50% - 195px + 53px);}}#rec1111375791 .tn-elem[data-elem-id="1749555640492"]{color:#000000;text-align:LEFT;z-index:3;top:527px;left:calc(50% - 600px + 678px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640492"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640492"] {display:table;top:507px;left:calc(50% - 480px + 485px);width:277px;height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640492"] {display:table;top:449px;left:calc(50% - 320px + 35px);height:auto;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640492"] {display:table;top:485px;left:calc(50% - 207px + 65px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640492"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:12px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640492"] {display:table;left:calc(50% - 195px + 53px);width:256px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640492"] .tn-atom{font-size:13px;background-size:cover;}}#rec1111375791 .tn-elem[data-elem-id="1749555640508"]{color:#000000;text-align:LEFT;z-index:3;top:559px;left:calc(50% - 600px + 678px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640508"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640508"] {display:table;top:539px;left:calc(50% - 480px + 485px);width:417px;height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640508"] {display:table;top:481px;left:calc(50% - 320px + 35px);width:250px;}#rec1111375791 .tn-elem[data-elem-id="1749555640508"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640508"] {display:table;top:517px;left:calc(50% - 207px + 65px);width:278px;height:34px;}#rec1111375791 .tn-elem[data-elem-id="1749555640508"] .tn-atom {vertical-align:middle;white-space:normal;font-size:12px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640508"] {display:table;left:calc(50% - 195px + 53px);height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640508"] .tn-atom{font-size:13px;background-size:cover;}}#rec1111375791 .tn-elem[data-elem-id="1749555640524"]{color:#000000;text-align:LEFT;z-index:3;top:591px;left:calc(50% - 600px + 678px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640524"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640524"] {display:table;top:571px;left:calc(50% - 480px + 485px);width:374px;height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640524"] {display:table;top:530px;left:calc(50% - 320px + 35px);width:230px;}#rec1111375791 .tn-elem[data-elem-id="1749555640524"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640524"] {display:table;top:566px;left:calc(50% - 207px + 65px);width:230px;height:34px;}#rec1111375791 .tn-elem[data-elem-id="1749555640524"] .tn-atom {vertical-align:middle;white-space:normal;font-size:12px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640524"] {display:table;left:calc(50% - 195px + 53px);height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640524"] .tn-atom{font-size:13px;background-size:cover;}}#rec1111375791 .tn-elem[data-elem-id="1749555640543"]{color:#000000;text-align:LEFT;z-index:3;top:495px;left:calc(50% - 600px + 678px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640543"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1111375791 .tn-elem[data-elem-id="1749555640543"] {display:table;top:475px;left:calc(50% - 480px + 485px);width:145px;height:auto;}}@media screen and (max-width:959px) {#rec1111375791 .tn-elem[data-elem-id="1749555640543"] {display:table;top:417px;left:calc(50% - 320px + 35px);height:auto;}}@media screen and (max-width:639px) {#rec1111375791 .tn-elem[data-elem-id="1749555640543"] {display:table;top:453px;left:calc(50% - 207px + 65px);width:auto;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640543"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:12px;background-size:cover;}}@media screen and (max-width:413px) {#rec1111375791 .tn-elem[data-elem-id="1749555640543"] {display:table;left:calc(50% - 195px + 53px);width:134px;height:auto;}#rec1111375791 .tn-elem[data-elem-id="1749555640543"] .tn-atom{font-size:13px;background-size:cover;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1111375791" data-artboard-screens="390,414,640,960,1200" data-artboard-height="680" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-ovrflw="visible" data-artboard-height-res-390="963" data-artboard-height-res-414="1003" data-artboard-height-res-640="745" data-artboard-height-res-960="639"> <div class="t396__carrier" data-artboard-recid="1111375791"></div> <div class="t396__filter" data-artboard-recid="1111375791"></div> <div class='t396__elem tn-elem tn-elem__11113757911749555640389' data-elem-id='1749555640389' data-elem-type='shape' data-field-top-value="109" data-field-left-value="20" data-field-height-value="560" data-field-width-value="580" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-height-res-390-value="314" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="639" data-field-left-res-414-value="15" data-field-height-res-414-value="354" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="605" data-field-left-res-640-value="15" data-field-height-res-640-value="130" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-height-res-960-value="520" data-field-width-res-960-value="433" data-field-heightmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640398' data-elem-id='1749555640398' data-elem-type='shape' data-field-top-value="109" data-field-left-value="600" data-field-height-value="560" data-field-width-value="580" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-height-res-414-value="539" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="100" data-field-left-res-640-value="15" data-field-height-res-640-value="505" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-left-res-960-value="453" data-field-height-res-960-value="520" data-field-width-res-960-value="486" data-field-heightmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640561 t-animate' data-elem-id='1749555640561' data-elem-type='shape' data-field-top-value="72" data-field-left-value="-79" data-field-height-value="653" data-field-width-value="756" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="zoomin" data-animate-duration="2" data-animate-scale="0.9" data-animate-mobile="y" data-field-top-res-390-value="584" data-field-left-res-390-value="-40" data-field-height-res-390-value="395" data-field-width-res-390-value="458" data-animate-delay-res-390="0.2" data-animate-scale-res-390="0.85" data-field-top-res-414-value="583" data-field-left-res-414-value="-58" data-field-top-res-640-value="327" data-field-left-res-640-value="265" data-field-height-res-640-value="440" data-field-width-res-640-value="510" data-field-top-res-960-value="102" data-field-left-res-960-value="-79" data-field-height-res-960-value="535" data-field-width-res-960-value="620"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6363-6162-4063-b233-373436373133/_5_1.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640406 t-animate' data-elem-id='1749555640406' data-elem-type='shape' data-field-top-value="296" data-field-left-value="640" data-field-height-value="53" data-field-width-value="498" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="244" data-field-left-res-390-value="88" data-field-top-res-414-value="251" data-field-left-res-414-value="100" data-field-height-res-414-value="77" data-field-width-res-414-value="213" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="227" data-field-left-res-640-value="95" data-field-top-res-960-value="275" data-field-left-res-960-value="471" data-field-width-res-960-value="450" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640416 t-animate' data-elem-id='1749555640416' data-elem-type='text' data-field-top-value="303" data-field-left-value="649" data-field-height-value="38" data-field-width-value="480" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-left-res-390-value="94" data-field-top-res-414-value="259" data-field-left-res-414-value="106" data-field-height-res-414-value="60" data-field-width-res-414-value="201" data-field-textfit-res-414-value="fixedsize" data-field-top-res-640-value="235" data-field-left-res-640-value="109" data-field-width-res-640-value="421" data-field-top-res-960-value="283" data-field-left-res-960-value="485" data-field-width-res-960-value="421" data-field-heightunits-res-960-value="px" data-field-widthunits-res-960-value="px" data-field-textfit-res-960-value="autowidth"> <div class='tn-atom'field='tn_text_1749555640416'>in Dubai &amp; Abu Dhabi</div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640433 t-animate' data-elem-id='1749555640433' data-elem-type='text' data-field-top-value="168" data-field-left-value="697" data-field-height-value="114" data-field-width-value="386" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-left-res-390-value="42" data-field-left-res-414-value="54" data-field-width-res-414-value="306" data-field-top-res-640-value="149" data-field-left-res-640-value="64" data-field-width-res-640-value="513" data-field-top-res-960-value="148" data-field-left-res-960-value="503"> <div class='tn-atom'field='tn_text_1749555640433'>Fast, Fresh,<br>and Hassle-Free Delivery </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640448 t-animate' data-elem-id='1749555640448' data-elem-type='text' data-field-top-value="382" data-field-left-value="748" data-field-height-value="50" data-field-width-value="282" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-left-res-390-value="54" data-field-top-res-414-value="357" data-field-left-res-414-value="66" data-field-height-res-414-value="46" data-field-width-res-414-value="282" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="304" data-field-left-res-640-value="179" data-field-top-res-960-value="352" data-field-left-res-960-value="555"> <div class='tn-atom'field='tn_text_1749555640448'>We deliver daily across Dubai &amp; Abu Dhabi — keeping your shakes fresh and your routine smooth. You can pay securely online</div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640463 t-animate' data-elem-id='1749555640463' data-elem-type='shape' data-field-top-value="519" data-field-left-value="678" data-field-height-value="1" data-field-width-value="423" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-left-res-390-value="53" data-field-top-res-414-value="477" data-field-left-res-414-value="65" data-field-height-res-414-value="1" data-field-width-res-414-value="284" data-field-container-res-414-value="grid" data-field-top-res-640-value="441" data-field-left-res-640-value="35" data-field-width-res-640-value="284" data-field-top-res-960-value="499" data-field-left-res-960-value="485"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640473 t-animate' data-elem-id='1749555640473' data-elem-type='shape' data-field-top-value="551" data-field-left-value="678" data-field-height-value="1" data-field-width-value="423" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-left-res-390-value="53" data-field-top-res-414-value="509" data-field-left-res-414-value="65" data-field-height-res-414-value="1" data-field-width-res-414-value="284" data-field-container-res-414-value="grid" data-field-top-res-640-value="473" data-field-left-res-640-value="35" data-field-width-res-640-value="284" data-field-top-res-960-value="531" data-field-left-res-960-value="485"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640480 t-animate' data-elem-id='1749555640480' data-elem-type='shape' data-field-top-value="583" data-field-left-value="678" data-field-height-value="1" data-field-width-value="423" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-heightmode-value="fixed" data-field-left-res-390-value="53" data-field-top-res-414-value="558" data-field-left-res-414-value="65" data-field-height-res-414-value="1" data-field-width-res-414-value="284" data-field-container-res-414-value="grid" data-field-top-res-640-value="522" data-field-left-res-640-value="35" data-field-width-res-640-value="284" data-field-top-res-960-value="563" data-field-left-res-960-value="485"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640492 t-animate' data-elem-id='1749555640492' data-elem-type='text' data-field-top-value="527" data-field-left-value="678" data-field-height-value="17" data-field-width-value="277" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-left-res-390-value="53" data-field-width-res-390-value="256" data-field-top-res-414-value="485" data-field-left-res-414-value="65" data-field-height-res-414-value="15" data-field-width-res-414-value="236" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-widthunits-res-414-value="px" data-field-textfit-res-414-value="autowidth" data-field-top-res-640-value="449" data-field-left-res-640-value="35" data-field-top-res-960-value="507" data-field-left-res-960-value="485" data-field-width-res-960-value="277"> <div class='tn-atom'field='tn_text_1749555640492'>Free delivery for subscriptions across the UAE</div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640508 t-animate' data-elem-id='1749555640508' data-elem-type='text' data-field-top-value="559" data-field-left-value="678" data-field-height-value="17" data-field-width-value="417" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-left-res-390-value="53" data-field-top-res-414-value="517" data-field-left-res-414-value="65" data-field-height-res-414-value="34" data-field-width-res-414-value="278" data-field-container-res-414-value="grid" data-field-textfit-res-414-value="fixedsize" data-field-top-res-640-value="481" data-field-left-res-640-value="35" data-field-width-res-640-value="250" data-field-textfit-res-640-value="fixedsize" data-field-top-res-960-value="539" data-field-left-res-960-value="485" data-field-width-res-960-value="417"> <div class='tn-atom'field='tn_text_1749555640508'>Also available on Talabat, Deliveroo, Careem &amp; Noon for daily shakes</div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640524 t-animate' data-elem-id='1749555640524' data-elem-type='text' data-field-top-value="591" data-field-left-value="678" data-field-height-value="17" data-field-width-value="374" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-left-res-390-value="53" data-field-top-res-414-value="566" data-field-left-res-414-value="65" data-field-height-res-414-value="34" data-field-width-res-414-value="230" data-field-container-res-414-value="grid" data-field-textfit-res-414-value="fixedsize" data-field-top-res-640-value="530" data-field-left-res-640-value="35" data-field-width-res-640-value="230" data-field-textfit-res-640-value="fixedsize" data-field-top-res-960-value="571" data-field-left-res-960-value="485" data-field-width-res-960-value="374"> <div class='tn-atom'field='tn_text_1749555640524'>Payment methods: Credit cards, PayPal, Apple Pay, and others</div> </div> <div class='t396__elem tn-elem tn-elem__11113757911749555640543 t-animate' data-elem-id='1749555640543' data-elem-type='text' data-field-top-value="495" data-field-left-value="678" data-field-height-value="17" data-field-width-value="145" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autowidth" data-field-left-res-390-value="53" data-field-width-res-390-value="134" data-field-top-res-414-value="453" data-field-left-res-414-value="65" data-field-height-res-414-value="15" data-field-width-res-414-value="123" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-widthunits-res-414-value="px" data-field-textfit-res-414-value="autowidth" data-field-top-res-640-value="417" data-field-left-res-640-value="35" data-field-top-res-960-value="475" data-field-left-res-960-value="485" data-field-width-res-960-value="145"> <div class='tn-atom'field='tn_text_1749555640543'>Delivery across the UAE</div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1111375791');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1111375791');});});</script> <!-- /T396 --> </div> <div id="rec1109931071" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1109931071 .t396__artboard {height:540px;background-color:#f5f5f5;overflow:visible;}#rec1109931071 .t396__filter {height:540px;}#rec1109931071 .t396__carrier{height:540px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1109931071 .t396__artboard,#rec1109931071 .t396__filter,#rec1109931071 .t396__carrier {height:540px;}#rec1109931071 .t396__filter {}#rec1109931071 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1109931071 .t396__artboard,#rec1109931071 .t396__filter,#rec1109931071 .t396__carrier {height:540px;}#rec1109931071 .t396__filter {}#rec1109931071 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1109931071 .t396__artboard,#rec1109931071 .t396__filter,#rec1109931071 .t396__carrier {height:770px;}#rec1109931071 .t396__filter {}#rec1109931071 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1109931071 .t396__artboard,#rec1109931071 .t396__filter,#rec1109931071 .t396__carrier {height:770px;}#rec1109931071 .t396__filter {}#rec1109931071 .t396__carrier {background-attachment:scroll;}}#rec1109931071 .tn-elem[data-elem-id="1749556756491"]{z-index:3;top:20px;left:calc(50% - 600px + 20px);width:1160px;height:500px;}#rec1109931071 .tn-elem[data-elem-id="1749556756491"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756491"] {display:table;width:920px;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756491"] {display:table;left:calc(50% - 320px + 15px);width:610px;height:500px;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756491"] {display:table;width:384px;height:730px;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756491"] {display:table;width:360px;}}#rec1109931071 .tn-elem[data-elem-id="1749556756496"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:474px;left:calc(50% - 600px + 50px);width:360px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756496"] .tn-atom {vertical-align:middle;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756496"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756496"] {display:table;top:432px;left:calc(50% - 320px + 45px);width:146px;height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756496"] {display:table;top:352px;left:calc(50% - 207px + 35px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756496"] .tn-atom {vertical-align:middle;white-space:nowrap;background-size:cover;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756496"] {display:table;top:390px;height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556766708"]{z-index:3;top:50px;left:calc(50% - 600px + 50px);width:415px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556766708"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556766708"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556766708"] {display:table;width:334px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556766708"] {display:table;left:calc(50% - 320px + 45px);width:270px;height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556766708"] {display:table;top:40px;left:calc(50% - 207px + 35px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556766708"] {display:table;height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756510"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:253px;left:calc(50% - 600px + 81px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756510"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756510"] {display:table;top:217px;width:134px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756510"] {display:table;top:203px;left:calc(50% - 320px + 76px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756510"] {display:table;top:193px;left:calc(50% - 207px + 66px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756510"] {display:table;left:calc(50% - 195px + 64px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756515"]{z-index:3;top:250px;left:calc(50% - 600px + 52px);width:20px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756515"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556756515"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756515"] {display:table;top:214px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756515"] {display:table;top:200px;left:calc(50% - 320px + 47px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756515"] {display:table;top:190px;left:calc(50% - 207px + 37px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756515"] {display:table;left:calc(50% - 195px + 35px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1751359105147"]{z-index:3;top:277px;left:calc(50% - 600px + 55px);width:14px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1751359105147"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1751359105147"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1751359105147"] {display:table;top:242px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1751359105147"] {display:table;top:225px;left:calc(50% - 320px + 47px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1751359105147"] {display:table;top:214px;left:calc(50% - 207px + 40px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1751359105147"] {display:table;top:217px;left:calc(50% - 195px + 38px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1751359062257"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:280px;left:calc(50% - 600px + 81px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1751359062257"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1751359062257"] {display:table;top:245px;width:121px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1751359062257"] {display:table;top:228px;left:calc(50% - 320px + 76px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1751359062257"] {display:table;top:217px;left:calc(50% - 207px + 66px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1751359062257"] {display:table;top:220px;left:calc(50% - 195px + 64px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756518"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:226px;left:calc(50% - 600px + 81px);width:134px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756518"] .tn-atom {vertical-align:middle;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756518"] {display:table;top:190px;width:123px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756518"] {display:table;top:176px;left:calc(50% - 320px + 76px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756518"] {display:table;top:166px;left:calc(50% - 207px + 66px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756518"] {display:table;left:calc(50% - 195px + 64px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756523"]{z-index:3;top:223px;left:calc(50% - 600px + 52px);width:20px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756523"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556756523"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756523"] {display:table;top:187px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756523"] {display:table;top:173px;left:calc(50% - 320px + 47px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756523"] {display:table;top:163px;left:calc(50% - 207px + 37px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756523"] {display:table;left:calc(50% - 195px + 35px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756526"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:476px;left:calc(50% - 600px + 465px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756526"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756526"] {display:table;top:359px;left:calc(50% - 480px + 52px);width:68px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756526"] {display:table;top:278px;left:calc(50% - 320px + 47px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756526"] {display:table;top:268px;left:calc(50% - 207px + 37px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756526"] {display:table;top:366px;height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756530"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:476px;left:calc(50% - 600px + 567px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756530"] .tn-atom {vertical-align:middle;white-space:nowrap;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;letter-spacing:-0.3px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756530"] {display:table;top:359px;left:calc(50% - 480px + 154px);width:67px;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756530"] {display:table;top:302px;left:calc(50% - 320px + 47px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756530"] {display:table;top:268px;left:calc(50% - 207px + 127px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756530"] {display:table;top:366px;height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756535"]{color:#1a1919;text-align:center;z-index:3;top:412px;left:calc(50% - 600px + 50px);width:253px;height:38px;}#rec1109931071 .tn-elem[data-elem-id="1749556756535"] .tn-atom{color:#1a1919;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#f5e39c;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109931071 .tn-elem[data-elem-id="1749556756535"] .tn-atom:hover {background-color:#f5dc78;background-image:none;}}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756535"] {display:table;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756535"] {display:table;top:376px;left:calc(50% - 320px + 47px);width:204px;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756535"] {display:table;top:296px;left:calc(50% - 207px + 37px);}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756535"] {display:table;top:283px;left:calc(50% - 195px + 203px);width:156px;}}#rec1109931071 .tn-elem[data-elem-id="1749556756540"]{color:#f5f5f5;text-align:LEFT;z-index:3;top:397px;left:calc(50% - 600px + 465px);width:70px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756540"] .tn-atom {vertical-align:middle;color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756540"] {display:table;top:280px;left:calc(50% - 480px + 52px);height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756540"] {display:table;top:50px;left:calc(50% - 320px + 440px);width:auto;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756540"] .tn-atom {vertical-align:middle;white-space:nowrap;background-size:cover;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756540"] {display:table;top:159px;left:calc(50% - 207px + 247px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756540"] {display:table;top:271px;left:calc(50% - 195px + 35px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756547"]{z-index:3;top:418px;left:calc(50% - 600px + 506px);width:29px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756547"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556756547"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756547"] {display:table;top:301px;left:calc(50% - 480px + 93px);height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756547"] {display:table;top:71px;left:calc(50% - 320px + 481px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756547"] {display:table;top:180px;left:calc(50% - 207px + 281px);width:24px;height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756547"] {display:table;top:292px;left:calc(50% - 195px + 69px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756549"]{z-index:3;top:416px;left:calc(50% - 600px + 545px);width:36px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756549"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556756549"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756549"] {display:table;top:299px;left:calc(50% - 480px + 132px);height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756549"] {display:table;top:69px;left:calc(50% - 320px + 520px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756549"] {display:table;top:178px;left:calc(50% - 207px + 312px);width:30px;height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756549"] {display:table;top:290px;left:calc(50% - 195px + 100px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556756554"]{z-index:3;top:20px;left:calc(50% - 600px + 634px);width:545px;height:500px;}#rec1109931071 .tn-elem[data-elem-id="1749556756554"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756554"] {display:table;top:53px;left:calc(50% - 480px + 431px);width:509px;height:467px;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756554"] {display:table;top:162px;left:calc(50% - 320px + 235px);width:390px;height:358px;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756554"] {display:table;top:399px;left:calc(50% - 207px + 15px);width:384px;height:351px;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756554"] {display:table;top:421px;left:calc(50% - 195px + 15px);width:360px;height:329px;}}#rec1109931071 .tn-elem[data-elem-id="1749556756557"]{z-index:3;top:138px;left:calc(50% - 600px + 661px);width:13px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556756557"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556756557"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556756557"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556756557"] {display:table;top:149px;left:calc(50% - 320px + 488px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556756557"] {display:table;top:353px;left:calc(50% - 207px + 343px);height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556756557"] {display:table;top:398px;left:calc(50% - 195px + 315px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1749556863325"]{z-index:3;top:419px;left:calc(50% - 600px + 465px);width:32px;height:auto;}#rec1109931071 .tn-elem[data-elem-id="1749556863325"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109931071 .tn-elem[data-elem-id="1749556863325"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1749556863325"] {display:table;top:302px;left:calc(50% - 480px + 52px);height:auto;}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1749556863325"] {display:table;top:72px;left:calc(50% - 320px + 440px);height:auto;}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1749556863325"] {display:table;top:181px;left:calc(50% - 207px + 247px);width:26px;height:auto;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1749556863325"] {display:table;top:293px;left:calc(50% - 195px + 35px);height:auto;}}#rec1109931071 .tn-elem[data-elem-id="1750851330770"]{z-index:3;top:416px;left:calc(50% - 600px + 593px);width:37px;height:37px;}#rec1109931071 .tn-elem[data-elem-id="1750851330770"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1109931071 .tn-elem[data-elem-id="1750851330770"] {display:table;top:299px;left:calc(50% - 480px + 180px);}}@media screen and (max-width:959px) {#rec1109931071 .tn-elem[data-elem-id="1750851330770"] {display:table;top:69px;left:calc(50% - 320px + 568px);}}@media screen and (max-width:639px) {#rec1109931071 .tn-elem[data-elem-id="1750851330770"] {display:table;top:178px;left:calc(50% - 207px + 351px);width:31px;height:31px;}}@media screen and (max-width:413px) {#rec1109931071 .tn-elem[data-elem-id="1750851330770"] {display:table;top:290px;left:calc(50% - 195px + 139px);}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1109931071" data-artboard-screens="390,414,640,960,1200" data-artboard-height="540" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-ovrflw="visible" data-artboard-height-res-390="770" data-artboard-height-res-414="770" data-artboard-height-res-640="540" data-artboard-height-res-960="540"> <div class="t396__carrier" data-artboard-recid="1109931071"></div> <div class="t396__filter" data-artboard-recid="1109931071"></div> <div class='t396__elem tn-elem tn-elem__11099310711749556756491' data-elem-id='1749556756491' data-elem-type='shape' data-field-top-value="20" data-field-left-value="20" data-field-height-value="500" data-field-width-value="1160" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-height-res-414-value="730" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-left-res-640-value="15" data-field-height-res-640-value="500" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-field-width-res-960-value="920" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756496' data-elem-id='1749556756496' data-elem-type='text' data-field-top-value="474" data-field-left-value="50" data-field-height-value="15" data-field-width-value="360" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-top-res-390-value="390" data-field-top-res-414-value="352" data-field-left-res-414-value="35" data-field-width-res-414-value="235" data-field-heightunits-res-414-value="px" data-field-widthunits-res-414-value="px" data-field-textfit-res-414-value="autowidth" data-field-top-res-640-value="432" data-field-left-res-640-value="45" data-field-width-res-640-value="146" data-field-widthmode-res-640-value="fixed"> <div class='tn-atom'field='tn_text_1749556756496'>© 2025 Multi-Vit Shakes. All rights reserved</div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556766708' data-elem-id='1749556766708' data-elem-type='image' data-field-top-value="50" data-field-left-value="50" data-field-height-value="143" data-field-width-value="415" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="415" data-field-fileheight-value="143" data-field-heightmode-value="hug" data-field-height-res-390-value="93" data-field-top-res-414-value="40" data-field-left-res-414-value="35" data-field-height-res-414-value="93" data-field-left-res-640-value="45" data-field-height-res-640-value="93" data-field-width-res-640-value="270" data-field-height-res-960-value="115" data-field-width-res-960-value="334"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3037-6430-4238-b561-653262363537/svg_1749556766692.svg'
src='https://static.tildacdn.one/tild3037-6430-4238-b561-653262363537/svg_1749556766692.svg'
alt='' imgfield='tn_img_1749556766708'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756510' data-elem-id='1749556756510' data-elem-type='text' data-field-top-value="253" data-field-left-value="81" data-field-height-value="15" data-field-width-value="134" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autowidth" data-field-left-res-390-value="64" data-field-top-res-414-value="193" data-field-left-res-414-value="66" data-field-top-res-640-value="203" data-field-left-res-640-value="76" data-field-top-res-960-value="217" data-field-width-res-960-value="134"> <div class='tn-atom'><a href="mailto:<EMAIL>"style="color: inherit"><EMAIL></a></div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756515' data-elem-id='1749556756515' data-elem-type='vector' data-field-top-value="250" data-field-left-value="52" data-field-height-value="20" data-field-width-value="20" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-left-res-390-value="35" data-field-top-res-414-value="190" data-field-left-res-414-value="37" data-field-top-res-640-value="200" data-field-left-res-640-value="47" data-field-top-res-960-value="214"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none"> <path d="M16.6665 3.3335H3.333175C2.416505 3.3335 1.6748375 4.0835 1.6748375 5.000175L1.666505 15.00015C1.666505 15.916825 2.416505 16.666825 3.333175 16.666825H16.6665C17.583175 16.666825 18.333175 15.916825 18.333175 15.00015V5.000175C18.333175 4.0835 17.583175 3.3335 16.6665 3.3335ZM16.333175 6.875175L10.4415 10.5585C10.174825 10.725175 9.82485 10.725175 9.558175 10.5585L3.6665 6.875175C3.58295 6.82825 3.509775 6.764875 3.4514 6.688875C3.39305 6.612875 3.350725 6.5258 3.326975 6.432975C3.30325 6.340125 3.2986 6.24345 3.3133 6.14875C3.328 6.05405 3.361775 5.96335 3.412575 5.882075C3.46335 5.800825 3.5301 5.7307 3.608775 5.676C3.68745 5.621275 3.7764 5.5831 3.87025 5.56375C3.9641 5.5444 4.0609 5.5443 4.1548 5.56345C4.2487 5.5826 4.337725 5.6206 4.4165 5.675175L9.999825 9.166825L15.583175 5.675175C15.66195 5.6206 15.750975 5.5826 15.844875 5.56345C15.938775 5.5443 16.035575 5.5444 16.129425 5.56375C16.223275 5.5831 16.312225 5.621275 16.3909 5.676C16.469575 5.7307 16.536325 5.800825 16.5871 5.882075C16.6379 5.96335 16.67165 6.05405 16.686375 6.14875C16.701075 6.24345 16.696425 6.340125 16.6727 6.432975C16.64895 6.5258 16.606625 6.612875 16.548275 6.688875C16.4899 6.764875 16.416725 6.82825 16.333175 6.875175Z" fill="#F6F6F6"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711751359105147' data-elem-id='1751359105147' data-elem-type='image' data-field-top-value="277" data-field-left-value="55" data-field-height-value="20" data-field-width-value="14" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="14" data-field-fileheight-value="20" data-field-heightmode-value="hug" data-field-top-res-390-value="217" data-field-left-res-390-value="38" data-field-height-res-390-value="20" data-field-top-res-414-value="214" data-field-left-res-414-value="40" data-field-height-res-414-value="20" data-field-top-res-640-value="225" data-field-left-res-640-value="47" data-field-height-res-640-value="20" data-field-top-res-960-value="242" data-field-height-res-960-value="20"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild6539-6136-4430-b563-623339643838/Vector.svg'
src='https://static.tildacdn.one/tild6539-6136-4430-b563-623339643838/Vector.svg'
alt='' imgfield='tn_img_1751359105147'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711751359062257' data-elem-id='1751359062257' data-elem-type='text' data-field-top-value="280" data-field-left-value="81" data-field-height-value="15" data-field-width-value="121" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autowidth" data-field-top-res-390-value="220" data-field-left-res-390-value="64" data-field-top-res-414-value="217" data-field-left-res-414-value="66" data-field-top-res-640-value="228" data-field-left-res-640-value="76" data-field-top-res-960-value="245" data-field-width-res-960-value="121"> <div class='tn-atom'><a href="https://maps.app.goo.gl/wAnbXU8jSMpayd2A9"style="color: inherit">View on Google Maps</a></div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756518' data-elem-id='1749556756518' data-elem-type='text' data-field-top-value="226" data-field-left-value="81" data-field-height-value="15" data-field-width-value="134" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-left-res-390-value="64" data-field-top-res-414-value="166" data-field-left-res-414-value="66" data-field-top-res-640-value="176" data-field-left-res-640-value="76" data-field-top-res-960-value="190" data-field-width-res-960-value="123"> <div class='tn-atom'><a href="tel:+9710503209877"style="color: inherit">+ 971 0503209877</a></div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756523' data-elem-id='1749556756523' data-elem-type='vector' data-field-top-value="223" data-field-left-value="52" data-field-height-value="20" data-field-width-value="20" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-left-res-390-value="35" data-field-top-res-414-value="163" data-field-left-res-414-value="37" data-field-top-res-640-value="173" data-field-left-res-640-value="47" data-field-top-res-960-value="187"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none"> <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7935 18.444225C12.5935 18.40005 9.192675 17.93005 5.631 14.369225C2.0701625 10.80755 1.600995 7.40755 1.555995 6.206725C1.48933 4.376725 2.891 2.599225 4.510175 1.9050575C4.70515 1.8208625 4.918675 1.7888075 5.129775 1.8120375C5.340875 1.8352675 5.542325 1.9129825 5.714325 2.0375575C7.047675 3.009225 7.96765 4.479225 8.75765 5.63505C8.931475 5.889 9.0058 6.198025 8.96645 6.503225C8.927125 6.808425 8.776875 7.0885 8.544325 7.29005L6.9185 8.49755C6.83995 8.554275 6.78465 8.637575 6.7629 8.732C6.741125 8.8264 6.754375 8.9255 6.80015 9.0109C7.1685 9.68005 7.8235 10.676725 8.5735 11.426725C9.3235 12.176725 10.36765 12.87505 11.0835 13.28505C11.17325 13.33545 11.2789 13.349525 11.3787 13.324425C11.478525 13.2993 11.564925 13.2369 11.620175 13.15005L12.6785 11.539225C12.873075 11.280775 13.1601 11.107625 13.479475 11.05605C13.79885 11.004475 14.1258 11.078475 14.391825 11.26255C15.564325 12.074225 16.93265 12.9784 17.934325 14.2609C18.069025 14.43415 18.1547 14.6404 18.1824 14.8581C18.2101 15.075775 18.178825 15.296925 18.091825 15.4984C17.394325 17.1259 15.629325 18.511725 13.7935 18.444225Z" fill="#F6F6F6"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756526' data-elem-id='1749556756526' data-elem-type='text' data-field-top-value="476" data-field-left-value="465" data-field-height-value="15" data-field-width-value="68" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autowidth" data-field-top-res-390-value="366" data-field-top-res-414-value="268" data-field-left-res-414-value="37" data-field-top-res-640-value="278" data-field-left-res-640-value="47" data-field-top-res-960-value="359" data-field-left-res-960-value="52" data-field-width-res-960-value="68"> <div class='tn-atom'field='tn_text_1749556756526'>Privacy Policy</div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756530' data-elem-id='1749556756530' data-elem-type='text' data-field-top-value="476" data-field-left-value="567" data-field-height-value="15" data-field-width-value="67" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autowidth" data-field-top-res-390-value="366" data-field-top-res-414-value="268" data-field-left-res-414-value="127" data-field-top-res-640-value="302" data-field-left-res-640-value="47" data-field-top-res-960-value="359" data-field-left-res-960-value="154" data-field-width-res-960-value="67"> <div class='tn-atom'field='tn_text_1749556756530'>Terms of Use</div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756535' data-elem-id='1749556756535' data-elem-type='button' data-field-top-value="412" data-field-left-value="50" data-field-height-value="38" data-field-width-value="253" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="283" data-field-left-res-390-value="203" data-field-width-res-390-value="156" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="296" data-field-left-res-414-value="37" data-field-top-res-640-value="376" data-field-left-res-640-value="47" data-field-width-res-640-value="204"> <a class='tn-atom' href="contact.html">Contact us →</a> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756540' data-elem-id='1749556756540' data-elem-type='text' data-field-top-value="397" data-field-left-value="465" data-field-height-value="15" data-field-width-value="70" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-textfit-value="autoheight" data-field-top-res-390-value="271" data-field-left-res-390-value="35" data-field-top-res-414-value="159" data-field-left-res-414-value="247" data-field-top-res-640-value="50" data-field-left-res-640-value="440" data-field-width-res-640-value="51" data-field-heightunits-res-640-value="px" data-field-widthunits-res-640-value="px" data-field-textfit-res-640-value="autowidth" data-field-top-res-960-value="280" data-field-left-res-960-value="52"> <div class='tn-atom'field='tn_text_1749556756540'>Follow us</div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756547' data-elem-id='1749556756547' data-elem-type='vector' data-field-top-value="418" data-field-left-value="506" data-field-height-value="33" data-field-width-value="29" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="292" data-field-left-res-390-value="69" data-field-top-res-414-value="180" data-field-left-res-414-value="281" data-field-width-res-414-value="24" data-field-top-res-640-value="71" data-field-left-res-640-value="481" data-field-top-res-960-value="301" data-field-left-res-960-value="93"> <a class='tn-atom tn-atom__vector' href="https://www.tiktok.com/@multivitshakes"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.131103515625 33" fill="none"> <g clip-path="url(#clip0_773_1045)"> <path d="M21.501244232438566 11.83427426862146C23.61802972610343 13.346676508663862 26.211270601712737 14.23654457673945 29.01208926365518 14.23654457673945V8.849954552909654C28.482002773187098 8.85015373994224 27.95316120167268 8.7948544400207 27.434776949368988 8.684952994791667V12.924972662301348C24.634157474459133 12.924972662301348 22.041265176156852 12.035229086121127 19.924006613289595 10.522926439595018V21.515535902026574C19.924006613289595 27.01474129440438 15.463785681256676 31.47229809987647 9.962264739625066 31.47229809987647C7.909492978557358 31.47229809987647 6.00148039342114 30.85183049337273 4.416549175138555 29.78817173936632C6.225516008321647 31.63687638555021 8.748269572774772 32.783695725661055 11.539153781467013 32.783695725661055C17.04112289392194 32.783695725661055 21.501468317850225 28.326138920188967 21.501468317850225 22.826784137536723V11.83427426862146H21.501244232438566ZM23.447127252145098 6.399854458592081C22.36526778303619 5.218600660223023 21.65491702807826 3.692056140867054 21.501244232438566 2.0043319645808295V1.311542036383213H20.006519841538125C20.38278414609208 3.4565423732138085 21.666245790556555 5.28911286975828 23.447127252145098 6.399854458592081ZM7.896247040890424 25.568394454043137C7.291789092130743 24.77637701572516 6.965047663553352 23.807332102196845 6.9665913630558896 22.8110234635834C6.9665913630558896 20.29598839664296 9.006664950796273 18.256736455411993 11.523617192925348 18.256736455411993C11.992603061147838 18.256462573242185 12.458825209293202 18.32844378714276 12.905900503931289 18.470140462448253V12.96306718228332C12.383457815838675 12.891534139206064 11.856234638964008 12.861058523220487 11.329260445880076 12.872287692182493V17.15866814152644C10.881961065830328 17.01694656784188 10.41548993389423 16.945015150699454 9.946279980260082 16.94538862638555C7.429427331647302 16.94538862638555 5.389478235802284 18.98441648220486 5.389478235802284 21.499800126452325C5.389478235802284 23.278415835545207 6.4091166556073045 24.81823119094718 7.896247040890424 25.568394454043137Z" fill="#FF004F"></path> <path d="M19.923682934361644 10.522826846078726C22.041041090745193 12.03510459422576 24.633709303635815 12.924873068785056 27.434278981787525 12.924873068785056V8.684828502896302C25.871158743573048 8.35198697144598 24.48718234279013 7.535544223257212 23.44667908132178 6.399854458592081C21.665698026216944 5.288988377862914 20.38246046716413 3.456442779697516 20.006196162610177 1.311542036383213H16.080095258455195V22.826560052125068C16.071131841988848 25.33484765833667 14.034568925697783 27.36555945554554 11.523069428585737 27.36555945554554C10.043184471612914 27.36555945554554 8.728325971137153 26.660686343983706 7.895699276550815 25.568145470252404C6.408792976679353 24.81823119094718 5.389054963358039 23.27829134364984 5.389054963358039 21.499899719968617C5.389054963358039 18.984765059511883 7.4290040592030575 16.945488219901844 9.945831809436767 16.945488219901844C10.428063615326188 16.945488219901844 10.89284165748531 17.020507036049345 11.328812275056757 17.158767735042733V12.872387285698784C5.923847247470953 12.984031617462941 1.5770583508196447 17.398016259556957 1.5770583508196447 22.82665964564136C1.5770583508196447 25.53652452882946 2.659470563943977 27.993247591980502 4.416325089726897 29.78842072315705C6.0012563080094825 30.85183049337273 7.909169299629407 31.472547083667198 9.962040654213409 31.472547083667198C15.463686087740385 31.472547083667198 19.92380742625701 27.014492310613647 19.92380742625701 21.515535902026574L19.923682934361644 10.522826846078726Z" fill="black"></path> <path d="M27.43452796557826 8.684529722347422V7.538283044955262C26.024781742454593 7.540349610418336 24.642921703892895 7.1457600988665195 23.44685336997529 6.399655271559495C24.50558224492521 7.558151951455662 25.899791879507212 8.357041342397835 27.43452796557826 8.684753807759082M20.006245959368325 1.3112332964827056C19.970392293502936 1.1062847789797008 19.942879584627068 0.8999668506276709 19.92373273111979 0.6927899281976162V0H14.502683350652712V21.515237121477696C14.494068511493388 24.0232508455195 12.457530493581396 26.05416182976095 9.945881606194913 26.05416182976095C9.233862659839074 26.055157764923877 8.531578979700853 25.88883659271501 7.895749073308961 25.568394454043137C8.7283757678953 26.660437360192976 10.04323426837106 27.365310471754807 11.523119225343883 27.365310471754807C14.034519128939635 27.365310471754807 16.07128123226329 25.334598674545937 16.08012015683427 22.82648535698785V1.3113478290264422L20.006245959368325 1.3112332964827056ZM11.32921064912193 12.872088505149907V11.651619759740585C10.876209540264423 11.589772186122795 10.419523471304085 11.558798602555756 9.962314536383213 11.55892309445112C4.460121338516626 11.55892309445112 0 16.016679086955794 0 21.515237121477696C0 24.962616891192575 1.7528832343207463 28.000717105702456 4.4165989718967005 29.78792275557559C2.659744446113782 27.99299860818977 1.577334722827357 25.536275545038727 1.577334722827357 22.826360865092482C1.577334722827357 17.397817072524372 5.9240215361244655 12.98370793853499 11.32921064912193 12.872088505149907Z" fill="#00F2EA"></path> </g> <defs> <clipPath id="clip0_773_1045"> <rect width="7.253146807809161" height="8.21646509415064" fill="white" transform="scale(4)"></rect> </clipPath> </defs> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756549' data-elem-id='1749556756549' data-elem-type='vector' data-field-top-value="416" data-field-left-value="545" data-field-height-value="36" data-field-width-value="36" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="290" data-field-left-res-390-value="100" data-field-top-res-414-value="178" data-field-left-res-414-value="312" data-field-width-res-414-value="30" data-field-top-res-640-value="69" data-field-left-res-640-value="520" data-field-top-res-960-value="299" data-field-left-res-960-value="132"> <a class='tn-atom tn-atom__vector' href="https://www.facebook.com/multivitshakes"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36.167598724365234 36.167598724365234" fill="none"> <path d="M36.167598724365234 18.083849248525688C36.167598724365234 8.096478650436401 28.071294676129572 0 18.083849248525688 0C8.096478650436401 0 0 8.096478650436401 0 18.083849248525688C0 27.109984845207478 6.613033409792144 34.591439715128935 15.258261833977535 35.94809881486564V23.311214650087027H10.666647988475932V18.083849248525688H15.258261833977535V14.099751288907282C15.258261833977535 9.567502191656704 17.958110720822564 7.064005951127677 22.08887452906115 7.064005951127677C24.0673668951416 7.064005951127677 26.13695172366438 7.417201260049754 26.13695172366438 7.417201260049754V11.867536981982527H23.856572152507386C21.610065408293625 11.867536981982527 20.90946160624537 13.261536009505699 20.90946160624537 14.69172757892477V18.083849248525688H25.924934765625L25.123261232520793 23.311214650087027H20.90946160624537V35.94809881486564C29.554665087259227 34.591439715128935 36.167598724365234 27.109984845207478 36.167598724365234 18.083849248525688Z" fill="#1877F2"></path> <path d="M25.123510664236132 23.311214650087027L25.92518419734034 18.083849248525688H20.909835753818378V14.691702635753236C20.909835753818378 13.261386350476496 21.610439555866638 11.867512038810993 23.8569463000804 11.867512038810993H26.137201155379724V7.41717631687822C26.137201155379724 7.41717631687822 24.067741042714612 7.0639810079561425 22.08909901760496 7.0639810079561425C17.958484868395576 7.0639810079561425 15.258635981550546 9.56747724848517 15.258635981550546 14.099751288907282V18.083849248525688H10.667022136048942V23.311214650087027H15.258635981550546V35.94809881486564C16.19338133478888 36.09451523177048 17.138103956640837 36.167848156080574 18.084223396098697 36.167598724365234C19.03036777872809 36.167848156080574 19.975090400580047 36.09451523177048 20.909835753818378 35.94809881486564V23.311214650087027H25.123510664236132Z" fill="white"></path> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756554 t-animate' data-elem-id='1749556756554' data-elem-type='shape' data-field-top-value="20" data-field-left-value="634" data-field-height-value="500" data-field-width-value="545" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="zoomin" data-animate-duration="2" data-animate-scale="0.9" data-animate-mobile="y" data-field-top-res-390-value="421" data-field-left-res-390-value="15" data-field-height-res-390-value="329" data-field-width-res-390-value="360" data-animate-delay-res-390="0.2" data-field-top-res-414-value="399" data-field-left-res-414-value="15" data-field-height-res-414-value="351" data-field-width-res-414-value="384" data-field-top-res-640-value="162" data-field-left-res-640-value="235" data-field-height-res-640-value="358" data-field-width-res-640-value="390" data-field-top-res-960-value="53" data-field-left-res-960-value="431" data-field-height-res-960-value="467" data-field-width-res-960-value="509"> <div class='tn-atom t-bgimg' data-original="../static.tildacdn.one/tild3035-6464-4134-b334-336439393063/Group_185.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556756557' data-elem-id='1749556756557' data-elem-type='vector' data-field-top-value="138" data-field-left-value="661" data-field-height-value="13" data-field-width-value="13" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="398" data-field-left-res-390-value="315" data-field-top-res-414-value="353" data-field-left-res-414-value="343" data-field-top-res-640-value="149" data-field-left-res-640-value="488"> <div class='tn-atom tn-atom__vector'> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13.82373046875 13.82373046875" fill="none"> <path d="M8.958616641671318 13.511931290980748C12.603638143484934 12.381520417131698 14.64216936819894 8.510283440290179 13.511758494349888 4.8652619384765625C12.381347620500838 1.2202330310930525 8.510085958426341 -0.8183031306675502 4.8650397713797435 0.31210527465820315C1.2200207380894252 1.4425112114606584 -0.8184783958217076 5.31379262172154 0.31192754098074776 8.958838808768135C1.442335946306501 12.603860310581753 5.313595139857702 14.6423421648298 8.958616641671318 13.511931290980748Z" fill="#EDE0AE"></path> </svg> </div> </div> <div class='t396__elem tn-elem tn-elem__11099310711749556863325' data-elem-id='1749556863325' data-elem-type='image' data-field-top-value="419" data-field-left-value="465" data-field-height-value="32" data-field-width-value="32" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="97" data-field-fileheight-value="96" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="293" data-field-left-res-390-value="35" data-field-height-res-390-value="26" data-field-top-res-414-value="181" data-field-left-res-414-value="247" data-field-height-res-414-value="26" data-field-width-res-414-value="26" data-field-top-res-640-value="72" data-field-left-res-640-value="440" data-field-height-res-640-value="32" data-field-top-res-960-value="302" data-field-left-res-960-value="52" data-field-height-res-960-value="32"> <a class='tn-atom' href="https://www.instagram.com/multivitshakes"> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild3535-3365-4635-a339-643534376531/skill-icons_instagra.png'
src='../thb.tildacdn.one/tild3535-3365-4635-a339-643534376531/-/resize/20x/skill-icons_instagra.png'
alt='' imgfield='tn_img_1749556863325'
/> </a> </div> <div class='t396__elem tn-elem tn-elem__11099310711750851330770' data-elem-id='1750851330770' data-elem-type='shape' data-field-top-value="416" data-field-left-value="593" data-field-height-value="37" data-field-width-value="37" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="290" data-field-left-res-390-value="139" data-field-top-res-414-value="178" data-field-left-res-414-value="351" data-field-height-res-414-value="31" data-field-width-res-414-value="31" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="69" data-field-left-res-640-value="568" data-field-top-res-960-value="299" data-field-left-res-960-value="180"> <a class='tn-atom t-bgimg' href="https://www.linkedin.com/company/mulit-vit-shakes" data-original="https://static.tildacdn.one/tild6337-3534-4563-b837-386261326333/skill-icons_linkedin.svg"
aria-label='' role="img"> </a> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1109931071');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1109931071');});});</script> <!-- /T396 --> </div> </footer> <!--/footer--> </div> <!--/allrecords--> <!-- Stat --> <script type="text/javascript">if(!window.mainTracker) {window.mainTracker='tilda';}
window.tildastatcookie='no';setTimeout(function(){(function(d,w,k,o,g) {var n=d.getElementsByTagName(o)[0],s=d.createElement(o),f=function(){n.parentNode.insertBefore(s,n);};s.type="text/javascript";s.async=true;s.key=k;s.id="tildastatscript";s.src=g;if(w.opera=="[object Opera]") {d.addEventListener("DOMContentLoaded",f,false);} else {f();}})(document,window,'dc45e60d2b39e40a7f6983dc50529e6e','script','../static.tildacdn.one/js/tilda-stat-1.0.min.js');},2000);</script> </body> 
<!-- Mirrored from multivitshakes.com/aboutus by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 14:08:53 GMT -->
</html>