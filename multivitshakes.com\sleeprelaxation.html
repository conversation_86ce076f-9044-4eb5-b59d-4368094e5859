<!DOCTYPE html> <html> 
<!-- Mirrored from multivitshakes.com/sleeprelaxation by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 14:10:11 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head> <meta charset="utf-8" /> <meta http-equiv="Content-Type" content="text/html; charset=utf-8" /> <meta name="viewport" content="width=device-width, initial-scale=1.0" /> <!--metatextblock--> <title>Sleep & Relaxation</title> <meta property="og:url" content="sleeprelaxation.html" /> <meta property="og:title" content="Sleep & Relaxation" /> <meta property="og:description" content="" /> <meta property="og:type" content="website" /> <link rel="canonical" href="sleeprelaxation.html"> <!--/metatextblock--> <meta name="format-detection" content="telephone=no" /> <meta http-equiv="x-dns-prefetch-control" content="on"> <link rel="dns-prefetch" href="https://ws.tildacdn.com/"> <link rel="dns-prefetch" href="https://static.tildacdn.one/"> <link rel="icon" type="image/png" sizes="32x32" href="../static.tildacdn.one/tild6264-6461-4239-b432-623166343462/Frame_32.png" media="(prefers-color-scheme: light)"/> <link rel="icon" type="image/png" sizes="32x32" href="../static.tildacdn.one/tild3536-3436-4163-b762-613933623963/Frame_32.png" media="(prefers-color-scheme: dark)"/> <link rel="icon" type="image/svg+xml" sizes="any" href="https://static.tildacdn.one/tild3232-6434-4466-b763-356263396233/Group_64.svg"> <link rel="apple-touch-icon" type="image/png" href="../static.tildacdn.one/tild3030-3563-4935-a432-323630656431/Frame_32.png"> <link rel="icon" type="image/png" sizes="192x192" href="../static.tildacdn.one/tild3030-3563-4935-a432-323630656431/Frame_32.png"> <!-- Assets --> <script src="../neo.tildacdn.com/js/tilda-fallback-1.0.min.js" async charset="utf-8"></script> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-grid-3.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';"/> <link rel="stylesheet" href="../static.tildacdn.one/ws/project13569789/tilda-blocks-page71425561.minee26.css?t=1752237714" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-animation-2.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-quiz-form-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-forms-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-popup-1.1.min.css" type="text/css" media="print" onload="this.media='all';" onerror="this.loaderr='y';" /> <noscript><link rel="stylesheet" href="../static.tildacdn.one/css/tilda-popup-1.1.min.css" type="text/css" media="all" /></noscript> <link rel="stylesheet" href="../static.tildacdn.one/css/tilda-cart-1.0.min.css" type="text/css" media="all" onerror="this.loaderr='y';" /> <script nomodule src="../static.tildacdn.one/js/tilda-polyfill-1.0.min.js" charset="utf-8"></script> <script type="text/javascript">function t_onReady(func) {if(document.readyState!='loading') {func();} else {document.addEventListener('DOMContentLoaded',func);}}
function t_onFuncLoad(funcName,okFunc,time) {if(typeof window[funcName]==='function') {okFunc();} else {setTimeout(function() {t_onFuncLoad(funcName,okFunc,time);},(time||100));}}function t_throttle(fn,threshhold,scope) {return function() {fn.apply(scope||this,arguments);};}function t396_initialScale(t){t=document.getElementById("rec"+t);if(t){t=t.querySelector(".t396__artboard");if(t){var e,r=document.documentElement.clientWidth,a=[];if(i=t.getAttribute("data-artboard-screens"))for(var i=i.split(","),l=0;l<i.length;l++)a[l]=parseInt(i[l],10);else a=[320,480,640,960,1200];for(l=0;l<a.length;l++){var n=a[l];n<=r&&(e=n)}var o="edit"===window.allrecords.getAttribute("data-tilda-mode"),d="center"===t396_getFieldValue(t,"valign",e,a),c="grid"===t396_getFieldValue(t,"upscale",e,a),s=t396_getFieldValue(t,"height_vh",e,a),u=t396_getFieldValue(t,"height",e,a),g=!!window.opr&&!!window.opr.addons||!!window.opera||-1!==navigator.userAgent.indexOf("OPR/index.html");if(!o&&d&&!c&&!s&&u&&!g){for(var _=parseFloat((r/e).toFixed(3)),f=[t,t.querySelector(".t396__carrier"),t.querySelector(".t396__filter")],l=0;l<f.length;l++)f[l].style.height=Math.floor(parseInt(u,10)*_)+"px";t396_scaleInitial__getElementsToScale(t).forEach(function(t){t.style.zoom=_})}}}}function t396_scaleInitial__getElementsToScale(t){return t?Array.prototype.slice.call(t.children).filter(function(t){return t&&(t.classList.contains("t396__elem")||t.classList.contains("t396__group"))}):[]}function t396_getFieldValue(t,e,r,a){var i=a[a.length-1],l=r===i?t.getAttribute("data-artboard-"+e):t.getAttribute("data-artboard-"+e+"-res-"+r);if(!l)for(var n=0;n<a.length;n++){var o=a[n];if(!(o<=r)&&(l=o===i?t.getAttribute("data-artboard-"+e):t.getAttribute("data-artboard-"+e+"-res-"+o)))break}return l}</script> <script src="../static.tildacdn.one/js/jquery-1.10.2.min.js" charset="utf-8" onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-scripts-3.0.min.js" charset="utf-8" defer onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/ws/project13569789/tilda-blocks-page71425561.minee26.js?t=1752237714" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-lazyload-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-animation-2.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-zero-1.1.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-step-manager-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-quiz-form-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-forms-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-popup-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-cart-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-widget-positions-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-animation-sbs-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-zero-scale-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script src="../static.tildacdn.one/js/tilda-events-1.0.min.js" charset="utf-8" async onerror="this.loaderr='y';"></script> <script type="text/javascript">window.dataLayer=window.dataLayer||[];</script> <script type="text/javascript">(function() {if((/bot|google|yandex|baidu|bing|msn|duckduckbot|teoma|slurp|crawler|spider|robot|crawling|facebook/i.test(navigator.userAgent))===false&&typeof(sessionStorage)!='undefined'&&sessionStorage.getItem('visited')!=='y'&&document.visibilityState){var style=document.createElement('style');style.type='text/css';style.innerHTML='@media screen and (min-width: 980px) {.t-records {opacity: 0;}.t-records_animated {-webkit-transition: opacity ease-in-out .2s;-moz-transition: opacity ease-in-out .2s;-o-transition: opacity ease-in-out .2s;transition: opacity ease-in-out .2s;}.t-records.t-records_visible {opacity: 1;}}';document.getElementsByTagName('head')[0].appendChild(style);function t_setvisRecs(){var alr=document.querySelectorAll('.t-records');Array.prototype.forEach.call(alr,function(el) {el.classList.add("t-records_animated");});setTimeout(function() {Array.prototype.forEach.call(alr,function(el) {el.classList.add("t-records_visible");});sessionStorage.setItem("visited","y");},400);}
document.addEventListener('DOMContentLoaded',t_setvisRecs);}})();</script></head> <body class="t-body" style="margin:0;"> <!--allrecords--> <div id="allrecords" class="t-records" data-hook="blocks-collection-content-node" data-tilda-project-id="13569789" data-tilda-page-id="71425561" data-tilda-page-alias="sleeprelaxation" data-tilda-formskey="2a7fbf90afb3bba69f857fbe13569789" data-tilda-cookie="no" data-tilda-lazy="yes" data-tilda-root-zone="one"> <!--header--> <header id="t-header" class="t-records" data-hook="blocks-collection-content-node" data-tilda-project-id="13569789" data-tilda-page-id="71000039" data-tilda-page-alias="header" data-tilda-formskey="2a7fbf90afb3bba69f857fbe13569789" data-tilda-cookie="no" data-tilda-lazy="yes" data-tilda-root-zone="one"> <div id="rec1109929906" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1109929906 .t396__artboard {height:60px;background-color:#f5f5f5;}#rec1109929906 .t396__filter {height:60px;}#rec1109929906 .t396__carrier{height:60px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1109929906 .t396__artboard,#rec1109929906 .t396__filter,#rec1109929906 .t396__carrier {}#rec1109929906 .t396__filter {}#rec1109929906 .t396__carrier {background-attachment:scroll;}}#rec1109929906 .tn-elem[data-elem-id="1749132440124"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 1053px);width:127px;height:40px;}#rec1109929906 .tn-elem[data-elem-id="1749132440124"] .tn-atom{color:#f5f5f5;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] .tn-atom:hover {background-color:#6377d4;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;left:calc(50% - 480px + 813px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:12px;left:calc(50% - 320px + 493px);width:127px;height:40px;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:12px;left:calc(50% - 207px + 227px);}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440124"] {display:table;top:-63px;left:calc(50% - 195px + 500px);}}#rec1109929906 .tn-elem[data-elem-id="1749132440127"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 456px);width:76px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440127"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;top:10px;left:calc(50% - 480px + 336px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;top:141px;left:calc(50% - 320px + 731px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440127"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132440129"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 543px);width:97px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440129"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;top:10px;left:calc(50% - 480px + 423px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;top:141px;left:calc(50% - 320px + 818px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440129"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132440133"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 651px);width:92px;height:36px;}#rec1109929906 .tn-elem[data-elem-id="1749132440133"] .tn-atom{color:#f5f5f5;font-size:12px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:56px 56px 56px 56px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] .tn-atom:hover {background-color:#f25555;background-image:none;}}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;top:10px;left:calc(50% - 480px + 531px);}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;top:141px;left:calc(50% - 320px + 926px);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132440133"] {display:table;}}#rec1109929906 .tn-elem[data-elem-id="1749132446265"]{z-index:3;top:11px;left:calc(50% - 600px + 20px);width:106px;height:auto;}#rec1109929906 .tn-elem[data-elem-id="1749132446265"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109929906 .tn-elem[data-elem-id="1749132446265"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132446265"] {display:table;height:auto;}}#rec1109929906 .tn-elem[data-elem-id="1749132721781"]{z-index:3;top:-70px;left:calc(50% - 600px + -261px);width:100px;height:100px;}#rec1109929906 .tn-elem[data-elem-id="1749132721781"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#f47575;background-position:center center;border-color:transparent ;border-style:solid;box-shadow:0px 3px 0px 0px rgba(128,145,226,1);}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;top:12px;left:calc(50% - 320px + 443px);width:40px;height:40px;border-radius:10px 10px 10px 10px;}#rec1109929906 .tn-elem[data-elem-id="1749132721781"] .tn-atom {background-size:cover;border-radius:10px 10px 10px 10px;box-shadow:0px 2px 0px 0px rgba(128,145,226,1);}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;top:12px;left:calc(50% - 207px + 359px);width:40px;height:40px;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132721781"] {display:table;left:calc(50% - 195px + 335px);}}#rec1109929906 .tn-elem[data-elem-id="1749132882190"]{z-index:3;top:-34px;left:calc(50% - 600px + -243px);width:26px;height:auto;}#rec1109929906 .tn-elem[data-elem-id="1749132882190"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1109929906 .tn-elem[data-elem-id="1749132882190"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;top:24px;left:calc(50% - 320px + 450px);height:auto;}}@media screen and (max-width:639px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;top:24px;left:calc(50% - 207px + 366px);width:26px;height:auto;}}@media screen and (max-width:413px) {#rec1109929906 .tn-elem[data-elem-id="1749132882190"] {display:table;left:calc(50% - 195px + 342px);height:auto;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1109929906" data-artboard-screens="390,414,640,960,1200" data-artboard-height="60" data-artboard-valign="center" data-artboard-upscale="window"> <div class="t396__carrier" data-artboard-recid="1109929906"></div> <div class="t396__filter" data-artboard-recid="1109929906"></div> <div class='t396__elem tn-elem tn-elem__11099299061749132440124' data-elem-id='1749132440124' data-elem-type='button' data-field-top-value="10" data-field-left-value="1053" data-field-height-value="40" data-field-width-value="127" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="-63" data-field-left-res-390-value="500" data-field-top-res-414-value="12" data-field-left-res-414-value="227" data-field-top-res-640-value="12" data-field-left-res-640-value="493" data-field-height-res-640-value="40" data-field-width-res-640-value="127" data-field-container-res-640-value="grid" data-field-left-res-960-value="813"> <a class='tn-atom' href="#popup:quiz">Quiz →</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440127' data-elem-id='1749132440127' data-elem-type='button' data-field-top-value="10" data-field-left-value="456" data-field-height-value="36" data-field-width-value="76" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="731" data-field-top-res-960-value="10" data-field-left-res-960-value="336"> <a class='tn-atom' href="catalog.html">Shop</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440129' data-elem-id='1749132440129' data-elem-type='button' data-field-top-value="10" data-field-left-value="543" data-field-height-value="36" data-field-width-value="97" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="818" data-field-top-res-960-value="10" data-field-left-res-960-value="423"> <a class='tn-atom' href="aboutus.html">About Us</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132440133' data-elem-id='1749132440133' data-elem-type='button' data-field-top-value="10" data-field-left-value="651" data-field-height-value="36" data-field-width-value="92" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-640-value="141" data-field-left-res-640-value="926" data-field-top-res-960-value="10" data-field-left-res-960-value="531"> <a class='tn-atom' href="contact.html">Contact</a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132446265' data-elem-id='1749132446265' data-elem-type='image' data-field-top-value="11" data-field-left-value="20" data-field-height-value="37" data-field-width-value="106" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="107" data-field-fileheight-value="37" data-field-heightmode-value="hug" data-field-height-res-390-value="35" data-field-left-res-414-value="15" data-field-height-res-414-value="35" data-field-height-res-640-value="35" data-field-height-res-960-value="35"> <a class='tn-atom' href="index.html"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3664-3030-4365-b563-663263663965/photo.svg'
src='https://static.tildacdn.one/tild3664-3030-4365-b563-663263663965/photo.svg'
alt='' imgfield='tn_img_1749132446265'
/> </a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132721781' data-elem-id='1749132721781' data-elem-type='shape' data-field-top-value="-70" data-field-left-value="-261" data-field-height-value="100" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="335" data-field-top-res-414-value="12" data-field-left-res-414-value="359" data-field-height-res-414-value="40" data-field-width-res-414-value="40" data-field-container-res-414-value="grid" data-field-top-res-640-value="12" data-field-left-res-640-value="443" data-field-height-res-640-value="40" data-field-width-res-640-value="40" data-field-container-res-640-value="grid" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed"> <a class='tn-atom' href="#menu"> </a> </div> <div class='t396__elem tn-elem tn-elem__11099299061749132882190' data-elem-id='1749132882190' data-elem-type='image' data-field-top-value="-34" data-field-left-value="-243" data-field-height-value="15" data-field-width-value="26" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="26" data-field-fileheight-value="15" data-field-heightmode-value="hug" data-field-left-res-390-value="342" data-field-height-res-390-value="15" data-field-top-res-414-value="24" data-field-left-res-414-value="366" data-field-height-res-414-value="15" data-field-width-res-414-value="26" data-field-container-res-414-value="grid" data-field-top-res-640-value="24" data-field-left-res-640-value="450" data-field-height-res-640-value="15" data-field-height-res-960-value="15"> <a class='tn-atom' href="#menu"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3064-3832-4733-b035-663732393333/Group_184.svg'
src='https://static.tildacdn.one/tild3064-3832-4733-b035-663732393333/Group_184.svg'
alt='' imgfield='tn_img_1749132882190'
/> </a> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1109929906');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1109929906');});});</script> <!-- /T396 --> </div> <div id="rec1126836916" class="r t-rec t-screenmax-640px" style=" " data-animationappear="off" data-record-type="396" data-screen-max="640px"> <!-- T396 --> <style>#rec1126836916 .t396__artboard {height:648px;background-color:#f5f5f5;}#rec1126836916 .t396__filter {height:648px;}#rec1126836916 .t396__carrier{height:648px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:700px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:600px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1126836916 .t396__artboard,#rec1126836916 .t396__filter,#rec1126836916 .t396__carrier {height:600px;}#rec1126836916 .t396__filter {}#rec1126836916 .t396__carrier {background-attachment:scroll;}}#rec1126836916 .tn-elem[data-elem-id="1749134179388"]{z-index:3;top:5px;left:calc(50% - 600px + 20px);width:1159px;height:546px;}#rec1126836916 .tn-elem[data-elem-id="1749134179388"] .tn-atom{border-width:1px;border-radius:38px 38px 38px 38px;background-color:#f8f4ed;background-position:center center;border-color:#eae6df ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;top:5px;left:calc(50% - 480px + 20px);width:920px;height:546px;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;top:10px;left:calc(50% - 320px + 15px);width:610px;height:640px;}#rec1126836916 .tn-elem[data-elem-id="1749134179388"] .tn-atom{background-color:#e2efd9;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;width:384px;height:575px;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1749134179388"] {display:table;left:calc(50% - 195px + 10px);width:370px;}}#rec1126836916 .tn-elem[data-elem-id="1750859045018"]{z-index:3;top:11px;left:calc(50% - 600px + 20px);width:106px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859045018"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859045018"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;top:41px;left:calc(50% - 320px + 48px);width:121px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;left:calc(50% - 207px + 34px);width:158px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859045018"] {display:table;top:34px;width:134px;height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1749134179407"]{color:#1a1919;text-align:LEFT;z-index:3;top:304px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:319px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:445px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1749134179407"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1749134179407"] {display:table;top:455px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859017698"]{color:#1a1919;text-align:LEFT;z-index:3;top:213px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:278px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:404px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1750859017698"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859017698"] {display:table;top:414px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859016682"]{color:#1a1919;text-align:LEFT;z-index:3;top:93px;left:calc(50% - 600px + 50px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom {vertical-align:middle;color:#1a1919;font-size:50px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;letter-spacing:-1px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:103px;left:calc(50% - 480px + 41px);height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom{font-size:45px;background-size:cover;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:237px;left:calc(50% - 320px + 83px);width:474px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"]{color:#000000;text-align:center;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom {vertical-align:middle;color:#000000;white-space:normal;font-size:30px;background-size:cover;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:363px;left:calc(50% - 207px + 35px);width:283px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {text-align:left;}#rec1126836916 .tn-elem[data-elem-id="1750859016682"] .tn-atom{font-size:30px;background-size:cover;opacity:1;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859016682"] {display:table;top:373px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750858994538"]{color:#f5f5f5;text-align:center;z-index:3;top:10px;left:calc(50% - 600px + 1053px);width:127px;height:40px;}#rec1126836916 .tn-elem[data-elem-id="1750858994538"] .tn-atom{color:#f5f5f5;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;border-width:1px;border-radius:50px 50px 50px 50px;background-color:#8091e2;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media (hover),(min-width:0\0) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] .tn-atom:hover {background-color:#6377d4;background-image:none;}}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;left:calc(50% - 480px + 813px);}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:387px;left:calc(50% - 320px + 257px);width:127px;height:40px;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:117px;left:calc(50% - 207px + 34px);}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750858994538"] {display:table;top:-63px;left:calc(50% - 195px + 500px);}}#rec1126836916 .tn-elem[data-elem-id="1750859089207"]{z-index:3;top:419px;left:calc(50% - 600px + 465px);width:32px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089207"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089207"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:302px;left:calc(50% - 480px + 52px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:579px;left:calc(50% - 320px + 223px);width:38px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:524px;left:calc(50% - 207px + 40px);width:37px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089207"] {display:table;top:526px;left:calc(50% - 195px + 34px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089203"]{z-index:3;top:416px;left:calc(50% - 600px + 545px);width:36px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089203"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089203"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:299px;left:calc(50% - 480px + 132px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:576px;left:calc(50% - 320px + 318px);width:42px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:523px;left:calc(50% - 207px + 127px);width:37px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089203"] {display:table;top:523px;left:calc(50% - 195px + 122px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089195"]{z-index:3;top:418px;left:calc(50% - 600px + 506px);width:29px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859089195"] .tn-atom{background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859089195"] .tn-atom__vector svg {display:block;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:301px;left:calc(50% - 480px + 93px);height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:578px;left:calc(50% - 320px + 271px);width:34px;height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:525px;left:calc(50% - 207px + 87px);width:30px;height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089195"] {display:table;top:525px;left:calc(50% - 195px + 82px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859089214"]{z-index:3;top:416px;left:calc(50% - 600px + 593px);width:37px;height:37px;}#rec1126836916 .tn-elem[data-elem-id="1750859089214"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:299px;left:calc(50% - 480px + 180px);}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:576px;left:calc(50% - 320px + 374px);width:43px;height:44px;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:523px;left:calc(50% - 207px + 174px);width:38px;height:40px;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859089214"] {display:table;top:523px;left:calc(50% - 195px + 169px);}}#rec1126836916 .tn-elem[data-elem-id="1750859182344"]{z-index:3;top:0px;left:calc(50% - 600px + 0px);width:23px;height:auto;}#rec1126836916 .tn-elem[data-elem-id="1750859182344"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126836916 .tn-elem[data-elem-id="1750859182344"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;height:auto;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;top:40px;left:calc(50% - 320px + 572px);height:auto;}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;left:calc(50% - 207px + 346px);height:auto;}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859182344"] {display:table;left:calc(50% - 195px + 327px);height:auto;}}#rec1126836916 .tn-elem[data-elem-id="1750859349346"]{z-index:3;top:-7px;left:calc(50% - 600px + 0px);width:100px;height:100px;}#rec1126836916 .tn-elem[data-elem-id="1750859349346"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;}}@media screen and (max-width:959px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;top:10px;left:calc(50% - 320px + 525px);}}@media screen and (max-width:639px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;left:calc(50% - 207px + 299px);}}@media screen and (max-width:413px) {#rec1126836916 .tn-elem[data-elem-id="1750859349346"] {display:table;left:calc(50% - 195px + 280px);}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1126836916" data-artboard-screens="390,414,640,960,1200" data-artboard-height="648" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-height-res-390="600" data-artboard-height-res-414="600" data-artboard-height-res-640="700"> <div class="t396__carrier" data-artboard-recid="1126836916"></div> <div class="t396__filter" data-artboard-recid="1126836916"></div> <div class='t396__elem tn-elem tn-elem__11268369161749134179388' data-elem-id='1749134179388' data-elem-type='shape' data-field-top-value="5" data-field-left-value="20" data-field-height-value="546" data-field-width-value="1159" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="10" data-field-width-res-390-value="370" data-field-widthmode-res-390-value="fixed" data-field-height-res-414-value="575" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="10" data-field-left-res-640-value="15" data-field-height-res-640-value="640" data-field-width-res-640-value="610" data-field-widthmode-res-640-value="fixed" data-field-heightmode-res-640-value="fixed" data-field-top-res-960-value="5" data-field-left-res-960-value="20" data-field-height-res-960-value="546" data-field-width-res-960-value="920" data-field-container-res-960-value="grid"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859045018' data-elem-id='1750859045018' data-elem-type='image' data-field-top-value="11" data-field-left-value="20" data-field-height-value="37" data-field-width-value="106" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="107" data-field-fileheight-value="37" data-field-heightmode-value="hug" data-field-top-res-390-value="34" data-field-height-res-390-value="44" data-field-width-res-390-value="134" data-field-left-res-414-value="34" data-field-height-res-414-value="52" data-field-width-res-414-value="158" data-field-top-res-640-value="41" data-field-left-res-640-value="48" data-field-height-res-640-value="42" data-field-width-res-640-value="121" data-field-height-res-960-value="35"> <a class='tn-atom' href="index.html"> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3838-6263-4763-a636-643763323538/photo.svg'
src='https://static.tildacdn.one/tild3838-6263-4763-a636-643763323538/photo.svg'
alt='' imgfield='tn_img_1750859045018'
/> </a> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161749134179407 t-animate' data-elem-id='1749134179407' data-elem-type='text' data-field-top-value="304" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="455" data-field-left-res-390-value="34" data-field-top-res-414-value="445" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="319" data-field-left-res-640-value="83" data-field-height-res-640-value="76" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="contact.html"style="color: inherit">Contact</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859017698 t-animate' data-elem-id='1750859017698' data-elem-type='text' data-field-top-value="213" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="414" data-field-left-res-390-value="34" data-field-top-res-414-value="404" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="278" data-field-left-res-640-value="83" data-field-height-res-640-value="38" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="aboutus.html"style="color: inherit">About Us</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859016682 t-animate' data-elem-id='1750859016682' data-elem-type='text' data-field-top-value="93" data-field-left-value="50" data-field-height-value="43" data-field-width-value="474" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="373" data-field-left-res-390-value="34" data-field-top-res-414-value="363" data-field-left-res-414-value="35" data-field-width-res-414-value="283" data-field-top-res-640-value="237" data-field-left-res-640-value="83" data-field-height-res-640-value="38" data-field-width-res-640-value="474" data-field-container-res-640-value="grid" data-field-heightunits-res-640-value="px" data-field-textfit-res-640-value="autoheight" data-field-top-res-960-value="103" data-field-left-res-960-value="41"> <div class='tn-atom'><a href="catalog.html"style="color: inherit">Shop</a></div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750858994538' data-elem-id='1750858994538' data-elem-type='button' data-field-top-value="10" data-field-left-value="1053" data-field-height-value="40" data-field-width-value="127" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-top-res-390-value="-63" data-field-left-res-390-value="500" data-field-top-res-414-value="117" data-field-left-res-414-value="34" data-field-top-res-640-value="387" data-field-left-res-640-value="257" data-field-height-res-640-value="40" data-field-width-res-640-value="127" data-field-container-res-640-value="grid" data-field-left-res-960-value="813"> <a class='tn-atom' href="#popup:quiz">Quiz →</a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089207' data-elem-id='1750859089207' data-elem-type='image' data-field-top-value="419" data-field-left-value="465" data-field-height-value="32" data-field-width-value="32" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="97" data-field-fileheight-value="96" data-field-widthmode-value="fixed" data-field-heightmode-value="hug" data-field-top-res-390-value="526" data-field-left-res-390-value="34" data-field-height-res-390-value="37" data-field-top-res-414-value="524" data-field-left-res-414-value="40" data-field-height-res-414-value="37" data-field-width-res-414-value="37" data-field-top-res-640-value="579" data-field-left-res-640-value="223" data-field-height-res-640-value="38" data-field-width-res-640-value="38" data-field-top-res-960-value="302" data-field-left-res-960-value="52" data-field-height-res-960-value="32"> <a class='tn-atom' href="https://www.instagram.com/multivitshakes"> <img class='tn-atom__img t-img' data-original='../static.tildacdn.one/tild3535-3365-4635-a339-643534376531/skill-icons_instagra.png'
src='../thb.tildacdn.one/tild3535-3365-4635-a339-643534376531/-/resize/20x/skill-icons_instagra.png'
alt='' imgfield='tn_img_1750859089207'
/> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089203' data-elem-id='1750859089203' data-elem-type='vector' data-field-top-value="416" data-field-left-value="545" data-field-height-value="36" data-field-width-value="36" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="523" data-field-left-res-390-value="122" data-field-top-res-414-value="523" data-field-left-res-414-value="127" data-field-width-res-414-value="37" data-field-top-res-640-value="576" data-field-left-res-640-value="318" data-field-height-res-640-value="36" data-field-width-res-640-value="42" data-field-top-res-960-value="299" data-field-left-res-960-value="132"> <a class='tn-atom tn-atom__vector' href="https://www.facebook.com/people/Mulit-Vit-Shakes/pfbid034fSusqv8gRRzPwEAcadzPPAKZEPgYwfcz6JzaSi83xF56ytLjfNYUNKCss4nkzqxl/"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36.167598724365234 36.167598724365234" fill="none"> <path d="M36.167598724365234 18.083849248525688C36.167598724365234 8.096478650436401 28.071294676129572 0 18.083849248525688 0C8.096478650436401 0 0 8.096478650436401 0 18.083849248525688C0 27.109984845207478 6.613033409792144 34.591439715128935 15.258261833977535 35.94809881486564V23.311214650087027H10.666647988475932V18.083849248525688H15.258261833977535V14.099751288907282C15.258261833977535 9.567502191656704 17.958110720822564 7.064005951127677 22.08887452906115 7.064005951127677C24.0673668951416 7.064005951127677 26.13695172366438 7.417201260049754 26.13695172366438 7.417201260049754V11.867536981982527H23.856572152507386C21.610065408293625 11.867536981982527 20.90946160624537 13.261536009505699 20.90946160624537 14.69172757892477V18.083849248525688H25.924934765625L25.123261232520793 23.311214650087027H20.90946160624537V35.94809881486564C29.554665087259227 34.591439715128935 36.167598724365234 27.109984845207478 36.167598724365234 18.083849248525688Z" fill="#1877F2"></path> <path d="M25.123510664236132 23.311214650087027L25.92518419734034 18.083849248525688H20.909835753818378V14.691702635753236C20.909835753818378 13.261386350476496 21.610439555866638 11.867512038810993 23.8569463000804 11.867512038810993H26.137201155379724V7.41717631687822C26.137201155379724 7.41717631687822 24.067741042714612 7.0639810079561425 22.08909901760496 7.0639810079561425C17.958484868395576 7.0639810079561425 15.258635981550546 9.56747724848517 15.258635981550546 14.099751288907282V18.083849248525688H10.667022136048942V23.311214650087027H15.258635981550546V35.94809881486564C16.19338133478888 36.09451523177048 17.138103956640837 36.167848156080574 18.084223396098697 36.167598724365234C19.03036777872809 36.167848156080574 19.975090400580047 36.09451523177048 20.909835753818378 35.94809881486564V23.311214650087027H25.123510664236132Z" fill="white"></path> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089195' data-elem-id='1750859089195' data-elem-type='vector' data-field-top-value="418" data-field-left-value="506" data-field-height-value="33" data-field-width-value="29" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-heightmode-value="hug" data-field-top-res-390-value="525" data-field-left-res-390-value="82" data-field-top-res-414-value="525" data-field-left-res-414-value="87" data-field-width-res-414-value="30" data-field-top-res-640-value="578" data-field-left-res-640-value="271" data-field-height-res-640-value="33" data-field-width-res-640-value="34" data-field-top-res-960-value="301" data-field-left-res-960-value="93"> <a class='tn-atom tn-atom__vector' href="https://www.tiktok.com/@multivitshakes"> <?xml version="1.0" encoding="UTF-8"?> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <!--?xml version="1.0" encoding="UTF-8"?--> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.131103515625 33" fill="none"> <g clip-path="url(#clip0_773_1045)"> <path d="M21.501244232438566 11.83427426862146C23.61802972610343 13.346676508663862 26.211270601712737 14.23654457673945 29.01208926365518 14.23654457673945V8.849954552909654C28.482002773187098 8.85015373994224 27.95316120167268 8.7948544400207 27.434776949368988 8.684952994791667V12.924972662301348C24.634157474459133 12.924972662301348 22.041265176156852 12.035229086121127 19.924006613289595 10.522926439595018V21.515535902026574C19.924006613289595 27.01474129440438 15.463785681256676 31.47229809987647 9.962264739625066 31.47229809987647C7.909492978557358 31.47229809987647 6.00148039342114 30.85183049337273 4.416549175138555 29.78817173936632C6.225516008321647 31.63687638555021 8.748269572774772 32.783695725661055 11.539153781467013 32.783695725661055C17.04112289392194 32.783695725661055 21.501468317850225 28.326138920188967 21.501468317850225 22.826784137536723V11.83427426862146H21.501244232438566ZM23.447127252145098 6.399854458592081C22.36526778303619 5.218600660223023 21.65491702807826 3.692056140867054 21.501244232438566 2.0043319645808295V1.311542036383213H20.006519841538125C20.38278414609208 3.4565423732138085 21.666245790556555 5.28911286975828 23.447127252145098 6.399854458592081ZM7.896247040890424 25.568394454043137C7.291789092130743 24.77637701572516 6.965047663553352 23.807332102196845 6.9665913630558896 22.8110234635834C6.9665913630558896 20.29598839664296 9.006664950796273 18.256736455411993 11.523617192925348 18.256736455411993C11.992603061147838 18.256462573242185 12.458825209293202 18.32844378714276 12.905900503931289 18.470140462448253V12.96306718228332C12.383457815838675 12.891534139206064 11.856234638964008 12.861058523220487 11.329260445880076 12.872287692182493V17.15866814152644C10.881961065830328 17.01694656784188 10.41548993389423 16.945015150699454 9.946279980260082 16.94538862638555C7.429427331647302 16.94538862638555 5.389478235802284 18.98441648220486 5.389478235802284 21.499800126452325C5.389478235802284 23.278415835545207 6.4091166556073045 24.81823119094718 7.896247040890424 25.568394454043137Z" fill="#FF004F"></path> <path d="M19.923682934361644 10.522826846078726C22.041041090745193 12.03510459422576 24.633709303635815 12.924873068785056 27.434278981787525 12.924873068785056V8.684828502896302C25.871158743573048 8.35198697144598 24.48718234279013 7.535544223257212 23.44667908132178 6.399854458592081C21.665698026216944 5.288988377862914 20.38246046716413 3.456442779697516 20.006196162610177 1.311542036383213H16.080095258455195V22.826560052125068C16.071131841988848 25.33484765833667 14.034568925697783 27.36555945554554 11.523069428585737 27.36555945554554C10.043184471612914 27.36555945554554 8.728325971137153 26.660686343983706 7.895699276550815 25.568145470252404C6.408792976679353 24.81823119094718 5.389054963358039 23.27829134364984 5.389054963358039 21.499899719968617C5.389054963358039 18.984765059511883 7.4290040592030575 16.945488219901844 9.945831809436767 16.945488219901844C10.428063615326188 16.945488219901844 10.89284165748531 17.020507036049345 11.328812275056757 17.158767735042733V12.872387285698784C5.923847247470953 12.984031617462941 1.5770583508196447 17.398016259556957 1.5770583508196447 22.82665964564136C1.5770583508196447 25.53652452882946 2.659470563943977 27.993247591980502 4.416325089726897 29.78842072315705C6.0012563080094825 30.85183049337273 7.909169299629407 31.472547083667198 9.962040654213409 31.472547083667198C15.463686087740385 31.472547083667198 19.92380742625701 27.014492310613647 19.92380742625701 21.515535902026574L19.923682934361644 10.522826846078726Z" fill="black"></path> <path d="M27.43452796557826 8.684529722347422V7.538283044955262C26.024781742454593 7.540349610418336 24.642921703892895 7.1457600988665195 23.44685336997529 6.399655271559495C24.50558224492521 7.558151951455662 25.899791879507212 8.357041342397835 27.43452796557826 8.684753807759082M20.006245959368325 1.3112332964827056C19.970392293502936 1.1062847789797008 19.942879584627068 0.8999668506276709 19.92373273111979 0.6927899281976162V0H14.502683350652712V21.515237121477696C14.494068511493388 24.0232508455195 12.457530493581396 26.05416182976095 9.945881606194913 26.05416182976095C9.233862659839074 26.055157764923877 8.531578979700853 25.88883659271501 7.895749073308961 25.568394454043137C8.7283757678953 26.660437360192976 10.04323426837106 27.365310471754807 11.523119225343883 27.365310471754807C14.034519128939635 27.365310471754807 16.07128123226329 25.334598674545937 16.08012015683427 22.82648535698785V1.3113478290264422L20.006245959368325 1.3112332964827056ZM11.32921064912193 12.872088505149907V11.651619759740585C10.876209540264423 11.589772186122795 10.419523471304085 11.558798602555756 9.962314536383213 11.55892309445112C4.460121338516626 11.55892309445112 0 16.016679086955794 0 21.515237121477696C0 24.962616891192575 1.7528832343207463 28.000717105702456 4.4165989718967005 29.78792275557559C2.659744446113782 27.99299860818977 1.577334722827357 25.536275545038727 1.577334722827357 22.826360865092482C1.577334722827357 17.397817072524372 5.9240215361244655 12.98370793853499 11.32921064912193 12.872088505149907Z" fill="#00F2EA"></path> </g> <defs> <clipPath id="clip0_773_1045"> <rect width="7.253146807809161" height="8.21646509415064" fill="white" transform="scale(4)"></rect> </clipPath> </defs> </svg> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859089214' data-elem-id='1750859089214' data-elem-type='shape' data-field-top-value="416" data-field-left-value="593" data-field-height-value="37" data-field-width-value="37" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="523" data-field-left-res-390-value="169" data-field-top-res-414-value="523" data-field-left-res-414-value="174" data-field-height-res-414-value="40" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="576" data-field-left-res-640-value="374" data-field-height-res-640-value="44" data-field-width-res-640-value="43" data-field-top-res-960-value="299" data-field-left-res-960-value="180"> <a class='tn-atom t-bgimg' href="https://www.linkedin.com/company/mulit-vit-shakes" data-original="https://static.tildacdn.one/tild6337-3534-4563-b837-386261326333/skill-icons_linkedin.svg"
aria-label='' role="img"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268369161750859182344' data-elem-id='1750859182344' data-elem-type='image' data-field-top-value="0" data-field-left-value="0" data-field-height-value="23" data-field-width-value="23" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-filewidth-value="23" data-field-fileheight-value="23" data-field-heightmode-value="hug" data-field-left-res-390-value="327" data-field-height-res-390-value="23" data-field-left-res-414-value="346" data-field-height-res-414-value="23" data-field-top-res-640-value="40" data-field-left-res-640-value="572" data-field-height-res-640-value="23" data-field-height-res-960-value="23"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3739-3162-4831-b965-303463626164/Group_182.svg'
src='https://static.tildacdn.one/tild3739-3162-4831-b965-303463626164/Group_182.svg'
alt='' imgfield='tn_img_1750859182344'
/> </div> </div> <div class='t396__elem tn-elem nolimClose082 tn-elem__11268369161750859349346' data-elem-id='1750859349346' data-elem-type='shape' data-field-top-value="-7" data-field-left-value="0" data-field-height-value="100" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-left-res-390-value="280" data-field-left-res-414-value="299" data-field-top-res-640-value="10" data-field-left-res-640-value="525"> <div class='tn-atom'> </div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1126836916');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1126836916');});});</script> <!-- /T396 --> </div> <div id="rec**********" class="r t-rec" style=" " data-record-type="1040"> <!-- t1040 --> <div class="t1040 t-quiz t-quiz-test popup fixed-height without-panel "> <div
class="t-popup" data-tooltip-hook="#popup:quiz"
role="dialog"
aria-modal="true"
tabindex="-1"> <div class="t-popup__close t-popup__block-close t-popup__btn-close-popup"> <button
type="button"
class="t-popup__close-wrapper t-popup__block-close-button"
aria-label="Закрыть диалоговое окно"> <svg role="presentation" class="t-popup__close-icon" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#a)"> <path fill-rule="evenodd" clip-rule="evenodd" d="m12.4 0 1.378 1.392-5.442 5.496L14 12.608 12.622 14 6.958 8.28l-5.58 5.636L0 12.524l5.58-5.636L.222 1.476 1.6.084l5.358 5.412L12.4 0Z" fill="#fff"/> </g> <defs> <clipPath id="a"> <path fill="#fff" d="M0 0h14v14H0z"/> </clipPath> </defs> </svg> </button> </div> <div class="t-popup__container t-width t-width_10"> <div
class="t-quiz__quiz t-animate" data-animate-style="fadeinright" data-animate-group="yes"> <form
id="form**********" name='form**********' role="form" action='#' method='POST' data-formactiontype="2" data-inputbox=".t-input-group" 
class="t-form js-form-proccess t-form_inputs-total_20 " data-success-callback="t_quiz__onSuccess"> <input type="hidden" name="formservices[]" value="02f4ebbdbe15d673818532cbd05f0ede" class="js-formaction-services"> <input type="hidden" name="formservices[]" value="42beafd0361231338f85499263a54b7a" class="js-formaction-services"> <div
class="t-quiz__quiz-wrapper
t-quiz__quiz-wrapper_fixed-height t-quiz__quiz-published without-panel t-quiz__inputs-in-cols " data-quiz-height="500px" data-auto-step-change="true"> <div class="t-quiz__content-padding-container"> <div class="t-quiz__content-wrapper"> <div class="t-quiz__progress-bar-container"> <div class="t-quiz__progressbar" style="background-color:rgba(106,144,80,0.20)"> <div class="t-quiz__progress" style="background-color:#6a9050"></div> </div> </div> <div class="t-quiz__main"> <div class="t-form__inputsbox t-quiz__quiz-form-wrapper t-quiz__quiz-form-wrapper_withcheckbox t-quiz__quiz-form-wrapper_newcapturecondition"> <div
class="t-quiz__cover t-quiz__cover-test t-quiz__screen-wrapper t-step-form__step" data-step-index="0"> <div class="t-quiz__cover__side-container leftside"> <div class="t-quiz__cover__content-container justify-center align-start"> <div class="t-quiz__cover__text-wrapper align-start"> <div
class="t-quiz__cover__title t-title t-title_xxs"
field="li_cover_title__4211261180710">
If you don’t know what vitamins &amp; minerals you need, just take our quick quiz and we will guide you
</div> </div> <div class="t-quiz__cover__btns-container"> <button
type="button"
class="t-btn t-quiz__btn_start t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;color: #f6f6f6;background: #8191e2;border-radius: 30;"> <span>Start</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> </div> </div> <div class="t-quiz__cover__side-cover"> <div class="t-quiz__cover__container" style="height: 500px;"> <div class="t-quiz__cover__img t-bgimg" data-original="../static.tildacdn.one/tild3937-3533-4236-b834-613062383739/Group_188.png"
bgimgfield="li_cover_img__4211261180710"
style="
background-image:url('../thb.tildacdn.one/tild3937-3533-4236-b834-613062383739/-/resizeb/20x/Group_188.png');
background-size: cover;
background-repeat: no-repeat;
background-position: left center;
"
itemscope itemtype="http://schema.org/ImageObject"> <meta itemprop="image" content="../static.tildacdn.one/tild3937-3533-4236-b834-613062383739/Group_188.png"> </div> </div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="1"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180711"
role="radiogroup" aria-labelledby="field-title_4211261180711" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="What is your gender?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180711" data-redactor-toolbar="no"
field="li_title__4211261180711"
style="">What is your gender?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Male"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Male</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Female"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Female</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your gender?"
value="Prefer not to mention"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Prefer not to mention</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180711');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180711"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="2"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180712"
role="radiogroup" aria-labelledby="field-title_4211261180712" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="What is your age group?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180712" data-redactor-toolbar="no"
field="li_title__4211261180712"
style="">What is your age group?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="Under 20"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Under 20</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="20-29"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>20-29</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="30-39"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>30-39</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="40-49"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>40-49</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="50-59"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>50-59</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="What is your age group?"
value="60+"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>60+</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180712');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180712"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="3"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180713"
role="radiogroup" aria-labelledby="field-title_4211261180713" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you eating as healthy as you want? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180713" data-redactor-toolbar="no"
field="li_title__4211261180713"
style="">Are you eating as healthy as you want? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you eating as healthy as you want? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you eating as healthy as you want? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180713');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180713"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="4"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180714"
role="radiogroup" aria-labelledby="field-title_4211261180714" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180714" data-redactor-toolbar="no"
field="li_title__4211261180714"
style="">Are you too busy to buy fruits &amp; veggies regularly to get enough natural whole food nutrients that your body needs? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you too busy to buy fruits & veggies regularly to get enough natural whole food nutrients that your body needs? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180714');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180714"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="5"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180715"
role="radiogroup" aria-labelledby="field-title_4211261180715" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180715" data-redactor-toolbar="no"
field="li_title__4211261180715"
style="">Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you currently need to take multi-vitamins, but your worried about taking chemically generated pills, and you want to and you want to switch to a natural organic way?"
value="Answer"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Answer</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180715');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180715"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="6"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180716"
role="radiogroup" aria-labelledby="field-title_4211261180716" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180716" data-redactor-toolbar="no"
field="li_title__4211261180716"
style="">It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="1 month (start to see improvement)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>1 month (start to see improvement)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="3 months (good improvement)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>3 months (good improvement)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="6 months (see major improvements)"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>6 months (see major improvements)</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="It is suggested to take vitamins at least 1 to 3 months, to see real improvements. How many months do u wish to start off with?"
value="12 months (see long lasting improvements) "
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>12 months (see long lasting improvements) </span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180716');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180716"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="7"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180717"
role="radiogroup" aria-labelledby="field-title_4211261180717" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do u want overall better health & longevity? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180717" data-redactor-toolbar="no"
field="li_title__4211261180717"
style="">Do u want overall better health &amp; longevity? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do u want overall better health & longevity? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do u want overall better health & longevity? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180717');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180717"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="8"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180718"
role="radiogroup" aria-labelledby="field-title_4211261180718" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you need help with Weight Management & Metabolism? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180718" data-redactor-toolbar="no"
field="li_title__4211261180718"
style="">Do you need help with Weight Management &amp; Metabolism? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need help with Weight Management & Metabolism? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need help with Weight Management & Metabolism? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180718');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180718"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="9"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="4211261180719"
role="radiogroup" aria-labelledby="field-title_4211261180719" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to improve Energy & Endurance?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_4211261180719" data-redactor-toolbar="no"
field="li_title__4211261180719"
style="">Do you want to improve Energy &amp; Endurance?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to improve Energy & Endurance?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to improve Energy & Endurance?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','4211261180719');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_4211261180719"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="10"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807110"
role="radiogroup" aria-labelledby="field-title_2112611807110" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you facing issues with your Gut & Digestive Health? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807110" data-redactor-toolbar="no"
field="li_title__2112611807110"
style="">Are you facing issues with your Gut &amp; Digestive Health? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you facing issues with your Gut & Digestive Health? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you facing issues with your Gut & Digestive Health? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807110');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807110"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="11"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807111"
role="radiogroup" aria-labelledby="field-title_2112611807111" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you need an antioxidant Boost?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807111" data-redactor-toolbar="no"
field="li_title__2112611807111"
style="">Do you need an antioxidant Boost?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need an antioxidant Boost?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you need an antioxidant Boost?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807111');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807111"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="12"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807112"
role="radiogroup" aria-labelledby="field-title_2112611807112" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you getting sick quickly and looking to improve your immunity? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807112" data-redactor-toolbar="no"
field="li_title__2112611807112"
style="">Are you getting sick quickly and looking to improve your immunity? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you getting sick quickly and looking to improve your immunity? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you getting sick quickly and looking to improve your immunity? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807112');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807112"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="13"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807113"
role="radiogroup" aria-labelledby="field-title_2112611807113" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you bones and joints in pain?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807113" data-redactor-toolbar="no"
field="li_title__2112611807113"
style="">Are you bones and joints in pain?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you bones and joints in pain?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you bones and joints in pain?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807113');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807113"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="14"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807115"
role="radiogroup" aria-labelledby="field-title_2112611807115" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you have trouble focusing and need Mental Clarity?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807115" data-redactor-toolbar="no"
field="li_title__2112611807115"
style="">Do you have trouble focusing and need Mental Clarity?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you have trouble focusing and need Mental Clarity?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you have trouble focusing and need Mental Clarity?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807115');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807115"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="15"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807116"
role="radiogroup" aria-labelledby="field-title_2112611807116" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807116" data-redactor-toolbar="no"
field="li_title__2112611807116"
style="">Is your hair falling and weak, is your skin looking dull &amp; your nails braking easily?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Is your hair falling and weak, is your skin looking dull & your nails braking easily?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807116');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807116"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="16"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807117"
role="radiogroup" aria-labelledby="field-title_2112611807117" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Are you having trouble sleeping and relaxing?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807117" data-redactor-toolbar="no"
field="li_title__2112611807117"
style="">Are you having trouble sleeping and relaxing?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you having trouble sleeping and relaxing?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Are you having trouble sleeping and relaxing?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807117');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807117"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="17"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807118"
role="radiogroup" aria-labelledby="field-title_2112611807118" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to do a Detox & Liver Cleanse?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807118" data-redactor-toolbar="no"
field="li_title__2112611807118"
style="">Do you want to do a Detox &amp; Liver Cleanse?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to do a Detox & Liver Cleanse?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to do a Detox & Liver Cleanse?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807118');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807118"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="18"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="2112611807119"
role="radiogroup" aria-labelledby="field-title_2112611807119" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to live a longer and healthier life?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_2112611807119" data-redactor-toolbar="no"
field="li_title__2112611807119"
style="">Do you want to live a longer and healthier life?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to live a longer and healthier life?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to live a longer and healthier life?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','2112611807119');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807119"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="19"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="1751560815522"
role="radiogroup" aria-labelledby="field-title_1751560815522" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you suffer from Stress & Anxiety?"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_1751560815522" data-redactor-toolbar="no"
field="li_title__1751560815522"
style="">Do you suffer from Stress &amp; Anxiety?</div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you suffer from Stress & Anxiety?"
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you suffer from Stress & Anxiety?"
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','1751560815522');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_1751560815522"></div> </div> </div> </div> <div
class="t-quiz__screen-wrapper t-step-form__step" data-step-index="20"> <div
class=" t-input-group t-input-group_rd t-input-group_two-cols " data-input-lid="1751560834966"
role="radiogroup" aria-labelledby="field-title_1751560834966" data-field-radcb="rb" data-field-async="true" data-field-type="rd" data-field-name="Do you want to look younger, need Collagen & Skin Elasticity support? "> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <div 
class="t-input-title t-heading t-heading_xs"
id="field-title_1751560834966" data-redactor-toolbar="no"
field="li_title__1751560834966"
style="">Do you want to look younger, need Collagen &amp; Skin Elasticity support? </div> </div> </div> <div> <div class="t-input-block " style="border-radius:4px;"> <div class="t-radio__wrapper t-radio__wrapper_button"> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to look younger, need Collagen & Skin Elasticity support? "
value="Yes"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>Yes</span> </label> <label
class="t-radio__item t-radio__control t-text t-text_xs"
style=""> <input
type="radio"
name="Do you want to look younger, need Collagen & Skin Elasticity support? "
value="No"
class="t-radio js-tilda-rule" data-tilda-req="1" aria-required="true"> <div
class="t-radio__indicator"
style="border-color:#6a9050"></div> <span>No</span> </label> <script>t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-variant-select-1.0.min.js',function() {t_onFuncLoad('t_input_radiobuttons_init',function() {try {t_input_radiobuttons_init('**********','1751560834966');} catch(e) {console.log(e)}})})});});</script> <style>#rec********** .t-radio__indicator:after{background:#6a9050;}</style> <style>#rec********** .t-radio__wrapper_button .t-radio__item{background-color:#ffffff;border:0px solid #000000;border-radius:4px;}</style> </div> </div> <div class="t-input-error" aria-live="polite" id="error_1751560834966"></div> </div> </div> </div> <div
class="t-quiz__contact-form t-quiz__screen-wrapper t-step-form__step" data-submit-title="Submit" data-step-index="21"> <div class="t-quiz__contact-form__layout row t-input-group_two-cols"> <div class="t-quiz__contact-form__header"> <div class="t-quiz__contact-form__header__text-wrapper"> <div
class="t-quiz__contact-form__header__title t-heading t-heading_xs"
field="li_contacts_title__2112611807120" data-redactor-nohref='yes'>
Leave your details and we’ll contact you soon
</div> </div> </div> <div class="t-quiz__contact-form__main"> <div class="t-quiz__inputs-wrapper"> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_nm t-input-group_one-col " data-input-lid="2112611807121" data-field-type="nm" data-field-name="Name"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <label for='input_2112611807121'
class="t-input-title t-descr t-descr_xs"
id="field-title_2112611807121" data-redactor-toolbar="no"
field="li_title__2112611807121"
style="">Your name</label> </div> </div> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="text"
autocomplete="name"
name="Name"
id="input_2112611807121"
class="t-input js-tilda-rule"
value=""
placeholder="Sarah" data-tilda-req="1" aria-required="true" data-tilda-rule="name"
aria-describedby="error_2112611807121"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807121"></div> </div> </div> </div> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_em t-input-group_one-col " data-input-lid="2112611807122" data-field-type="em" data-field-name="Email"> <div class=" t-input-group__header"> <div class="t-input-group__title-container"> <label for='input_2112611807122'
class="t-input-title t-descr t-descr_xs"
id="field-title_2112611807122" data-redactor-toolbar="no"
field="li_title__2112611807122"
style="">Your Email</label> </div> </div> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="email"
autocomplete="email"
name="Email"
id="input_2112611807122"
class="t-input js-tilda-rule"
value=""
placeholder="<EMAIL>" data-tilda-req="1" aria-required="true" data-tilda-rule="email"
aria-describedby="error_2112611807122"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> </div> <div class="t-input-error" aria-live="polite" id="error_2112611807122"></div> </div> </div> </div> <div
class="t-quiz__step__input"> <div
class=" t-input-group t-input-group_ph t-input-group_one-col " data-input-lid="1751560280387" data-field-async="true" data-field-type="ph" data-field-name="Phone"> <div> <div class="t-input-block t-input-block_rd-flex t-input-block_rd-width50 " style="border-radius:4px;"> <input
type="tel"
autocomplete="tel"
name="Phone"
id="input_1751560280387" data-phonemask-init="no" data-phonemask-id="**********" data-phonemask-lid="1751560280387"
class="t-input js-phonemask-input js-tilda-rule"
value=""
placeholder="+1(999)999-9999" data-tilda-req="1" aria-required="true" aria-describedby="error_1751560280387"
style="color:#1a1919;border:0px solid #000;background-color:#ffffff;border-radius:4px;"> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-phone-mask-1.1.min.js',function() {t_onFuncLoad('t_form_phonemask_load',function() {var phoneMasks=document.querySelectorAll('#rec********** [data-phonemask-lid="1751560280387"]');t_form_phonemask_load(phoneMasks);});})})});</script> </div> <div class="t-input-error" aria-live="polite" id="error_1751560280387"></div> </div> </div> </div> </div> </div> </div> </div> <div class="t-quiz__result t-quiz__result_ordinary t-quiz-hidden"> <div class="t-quiz__successbox-wrapper"> <div class="js-successbox t-form__successbox t-text t-text_md"
aria-live="polite"
style="display:none;" data-success-message="Thank you for submitting your information, We advise you to take the following products to improve all your health"></div> </div> </div> <div class="t-form__errorbox-middle"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> <div class="t-form__errorbox-bottom"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> </div> <div
class="t-quiz__btn-wrapper t-quiz__btn-wrapper_mobile"> <button
type="button"
class="t-btn t-quiz__btn_prev t-quiz__btn_sm"
style="color:#7579ed;border:1px solid #dddfff;border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <svg class="t-btn__icon t-btn__icon_arrow left" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="button"
class="t-btn t-quiz__btn_next t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Next</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="submit"
class="t-btn t-quiz__btn_submit t-quiz__btn_md t-submit t-quiz-hidden"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Go to the result</span> </button> </div> <div class="t-quiz__footer-sticky"> <div class="t-quiz__footer"> <div class="t-quiz__footer__text-container"> <div class="t-quiz__counter-container t-descr t-descr_xxs"> <span class="t-quiz__counter-title">Шаг:&nbsp;</span><span class="t-quiz__counter"></span> </div> </div> <div
class="t-quiz__btn-wrapper"> <button
type="button"
class="t-btn t-quiz__btn_prev t-quiz__btn_sm"
style="color:#7579ed;border:1px solid #dddfff;border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <svg class="t-btn__icon t-btn__icon_arrow left" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="button"
class="t-btn t-quiz__btn_next t-quiz__btn_md"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Next</span> <svg class="t-btn__icon t-btn__icon_arrow right" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M14.25 9H3.75" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> <path d="M9 3.75L3.75 9L9 14.25" stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/> </svg> </button> <button
type="submit"
class="t-btn t-quiz__btn_submit t-quiz__btn_md t-submit t-quiz-hidden"
style="color:#ffffff;background-image:linear-gradient(0.249turn,rgba(134,137,242,1) 0%,rgba(82,87,222,1) 100%);border-radius:4px; -moz-border-radius:4px; -webkit-border-radius:4px;"> <span>Go to the result</span> </button> </div> </div> </div> </div> </div> </div> </div> </form> <style>#rec********** input::-webkit-input-placeholder {color:#1a1919;opacity:0.5;}#rec********** input::-moz-placeholder{color:#1a1919;opacity:0.5;}#rec********** input:-moz-placeholder {color:#1a1919;opacity:0.5;}#rec********** input:-ms-input-placeholder{color:#1a1919;opacity:0.5;}#rec********** textarea::-webkit-input-placeholder {color:#1a1919;opacity:0.5;}#rec********** textarea::-moz-placeholder{color:#1a1919;opacity:0.5;}#rec********** textarea:-moz-placeholder {color:#1a1919;opacity:0.5;}#rec********** textarea:-ms-input-placeholder{color:#1a1919;opacity:0.5;}</style> </div> </div> </div> </div> <style>.t-form__screen-hiderecord{opacity:0 !important;}</style> <script>t_onReady(function() {var rec=document.querySelector('#rec**********');rec&&rec.classList.add('t-form__screen-hiderecord');function showRecord() {rec&&rec.classList.remove('t-form__screen-hiderecord');}
var opacityTimeout=setTimeout(showRecord,1500);t_onFuncLoad('t_quiz__step_manager',function() {t_onFuncLoad('t_quiz__init',function() {t_quiz__init('**********');showRecord();clearTimeout(opacityTimeout);});})});</script> <style> #rec********** .t-quiz__cover__title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__cover__title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__cover__title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__cover__title{font-size:30px;line-height:0.85;}}#rec********** .t-quiz__contact-form__header__title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__contact-form__header__title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__contact-form__header__title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__contact-form__header__title{font-size:30px;line-height:0.85;}}#rec********** .t-quiz__result-title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-quiz__result-title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__result-title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__result-title{font-size:30px;line-height:0.85;}}</style> <style> #rec********** .t-quiz__contact-form__header__descr{color:#6377d4;}#rec********** .t-quiz__contact-form__footer__text{color:#6377d4;}#rec********** .t-rate__text{color:#6377d4;}#rec********** .t-range__interval-txt{color:#6377d4;}#rec********** .t-contact-method__type-label{color:#6377d4;}</style> <style> #rec********** .t-quiz__cover__title{color:#6377d4;font-weight:900;font-family:'GillSans';padding-bottom:40px;}@media screen and (min-width:900px){#rec********** .t-quiz__cover__title{font-size:40px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-quiz__cover__title{font-size:35px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-quiz__cover__title{font-size:35px;line-height:0.8;}}</style> <style> #rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{color:#6377d4;font-weight:900;padding-top:15px;font-family:'GillSans';}@media screen and (min-width:900px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:35px;line-height:0.85;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-step-form__step:not(.t-quiz__contact-form) .t-input-title{font-size:30px;line-height:0.85;}}</style> <style> #rec********** .t-step-form__step.t-quiz__contact-form .t-input-title{color:#6377d4;font-family:'GillSans';}</style> <style> @media screen and (min-width:900px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:35px;}}@media screen and (min-width:480px) and (max-width:900px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:30px;}}@media screen and (max-width:480px),(orientation:landscape) and (max-height:480px){#rec********** .t-step-form__step.t-quiz__contact-form .t-input-group__title-container{font-size:30px;}}</style> <style> #rec********** .t-radio__control{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}#rec********** .t-checkbox__control{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}#rec********** .t-input-ownanswer{font-size:14px;line-height:1.2;color:#1a1919;font-weight:400;font-family:'GillSans';}</style> <style>#rec********** .t1040 .t-quiz__quiz {box-shadow:0px 30px 40px -10px rgba(0,0,0,0.1);}</style> <style>#rec********** .t-quiz{--quiz-background-color:#f6f6f6;--consultant-msg-bubble-bg:rgba(0,0,0,0.05);--panel-background-color:#f4f4f4;--btn-wrapper-background-color:rgba(246,246,246,0.95);--content-padding:0px;--border-size:1px;--border-radius:25px;--outer-border-radius:25px;--inner-border-radius:max(0px,var(--border-radius) - var(--border-size));--content-padding-radius:calc(var(--border-radius) - var(--content-padding));--btn-close-popup-icon-color:#101010;--btn-close-popup-icon-color-mob:#000000;--btn-close-popup-bg-color:rgba(255,255,255,0.7);--btn-close-popup-bg-color-mob:var(--btn-close-popup-bg-color);--secondary-text-font-size-mob:clamp(14px,var(--page-font-size) - 4px,16px);}#rec********** .t-quiz:has(.t-quiz__cover.t-step-form__step_active,.t-quiz__result.t-step-form__step_active){--content-padding:0px;}#rec********** .t-quiz.fullscreen{--outer-border-radius:0 !important;--inner-border-radius:0 !important;}#rec********** .t-quiz:not(.popup.fullscreen) .t-quiz__quiz,#rec********** .t-quiz.popup.fullscreen .t-quiz__quiz-wrapper{border-style:solid;border-width:1px;border-color:#e6e6e6;}#rec********** .t-quiz__quiz-wrapper{height:500px;}#rec********** .t-step-form__step.t-quiz__contact-form .t-input-title{font-size:clamp(14px,1em * 0.55,24px);}.t-quiz__btn-wrapper_mobile{display:none;}@media screen and (max-width:640px){#rec********** .t-quiz{--inner-border-radius:0 !important;--outer-border-radius:0 !important;--content-padding-radius:0 !important;--content-padding:0px;--prev-btn-border-radius:4px;}}</style> </div> <div id="rec1126402946" class="r t-rec" style=" " data-record-type="270"> <div class="t270"></div> <script>t_onReady(function() {var hash=window.location.hash;t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3);});setTimeout(function() {var curPath=window.location.pathname;var curFullPath=window.location.origin + curPath;var recs=document.querySelectorAll('.r');Array.prototype.forEach.call(recs,function(rec) {var selects='a[href^="#"]:not([href="#"]):not(.carousel-control):not(.t-carousel__control):not([href^="#price"]):not([href^="#submenu"]):not([href^="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([href^="#prodpopup"]):not([href^="#order"]):not([href^="#!"]):not([target="_blank"]),' +
'a[href^="' + curPath + '#"]:not([href*="#!/tfeeds/"]):not([href*="#!/tproduct/"]):not([href*="#!/tab/"]):not([href*="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([target="_blank"]),' +
'a[href^="' + curFullPath + '#"]:not([href*="#!/tfeeds/"]):not([href*="#!/tproduct/"]):not([href*="#!/tab/"]):not([href*="#popup"]):not([href*="#zeropopup"]):not([href*="#closepopup"]):not([href*="#closeallpopup"]):not([target="_blank"])';var elements=rec.querySelectorAll(selects);Array.prototype.forEach.call(elements,function(element) {element.addEventListener('click',function(event) {event.preventDefault();var hash=this.hash.trim();t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3);});});});});if(document.querySelectorAll('.js-store').length>0||document.querySelectorAll('.js-feed').length>0) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,-3,1);});}},500);setTimeout(function() {var hash=window.location.hash;if(hash&&document.querySelectorAll('a[name="' + hash.slice(1) + '"], div[id="' + hash.slice(1) + '"]').length>0) {if(window.isMobile) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});} else {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});}}},1000);window.addEventListener('popstate',function() {var hash=window.location.hash;if(hash&&document.querySelectorAll('a[name="' + hash.slice(1) + '"], div[id="' + hash.slice(1) + '"]').length>0) {if(window.isMobile) {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});} else {t_onFuncLoad('t270_scroll',function() {t270_scroll(hash,0);});}}});});</script> </div> <div id="rec1126862896" class="r t-rec" style=" " data-animationappear="off" data-record-type="131"> <!-- T123 --> <div class="t123"> <div class="t-container_100 "> <div class="t-width t-width_100 "> <!-- nominify begin --> <!--NOLIM--><!--NLM082--><!--settings{"comment":"","blockId":"#rec1126836916","openLink":"#menu","closeClass":"nolimClose082","animationSide":"0","t396overflow":"auto","darkBackground":"1","backgroundColor":"","opacity":"100","blockWidth":"300","widthPxOrPercent":"0","blocksZindex":"","addMobileMenuSize960":{"blockSize960":"","widthPxOrPercent960":"0"},"addMobileMenuSize640":{"blockSize640":"","widthPxOrPercent640":"0"},"addMobileMenuSize480":{"blockSize480":"","widthPxOrPercent480":"0"},"addMobileMenuSize320":{"blockSize320":"","widthPxOrPercent320":"0"},"scrollbarOff":"0","plusScrollbarWidth":"0","animEveryOpen":"1","notBlockScroll":"0"}settingsend--><!--ts1750859404595ts--> <script> (function() { if (typeof window.nlm082blocks == 'undefined') { window.nlm082blocks = []; window.nlm082blocks.push('#rec1126836916'); } else { window.nlm082blocks.push('#rec1126836916'); } if (typeof window.nlm082openLinks == 'undefined') { window.nlm082openLinks = []; window.nlm082openLinks.push('#menu'); } else { window.nlm082openLinks.push('#menu'); } window.nlm082open = false; function t_ready(e) { "loading" != document.readyState ? e() : document.addEventListener ? document.addEventListener("DOMContentLoaded", e) : document.attachEvent("onreadystatechange", (function() { "loading" != document.readyState && e() } )) } t_ready((function() { setTimeout(function() { window.t_animate__removeInlineAnimStyles = null; let blk = document.querySelector("#rec1126836916"); let isMac; if (window.navigator.userAgent.toLowerCase().indexOf('mac') !== -1) { isMac = true; } else { isMac = false; } let into = setInterval(function() { var c = document.querySelectorAll("[href='#menu']"); if (c.length > 0) { clearInterval(into); var menuId = "rec1126836916"; let menuBlock = document.querySelector("#rec1126836916 .t396"); let menuBlockArt = document.querySelector("#rec1126836916 .t396__artboard"); menuBlock.style.display = "none"; setTimeout(function() { menuBlock.style.display = "block"; }, 0); var scrollWidth = 0; function removeAnimation(blk) { let block = document.querySelector(blk); let elemList = block.querySelectorAll(".t396__elem"); elemList.forEach(function(el) { if (el.hasAttribute('data-animate-sbs-event') && el.getAttribute('data-animate-sbs-event') != "hover" && el.getAttribute('data-animate-sbs-event') != "click" && el.getAttribute('data-animate-sbs-event') != "scroll") { el.classList.remove('t-sbs-anim_started'); } if (el.classList.contains('t-sbs-anim_reversed')) { el.classList.remove('t-sbs-anim_reversed'); el.classList.remove('t-sbs-anim_started'); } if (el.classList.contains('t-sbs-anim_playing')) { el.classList.remove('t-sbs-anim_playing'); } if (el.hasAttribute('data-animate-style')) { el.classList.remove('t-animate_started'); } }); } function addAnimation(blk) { let block = document.querySelector(blk); let elemList = block.querySelectorAll(".t396__elem"); elemList.forEach(function(el) { if (el.hasAttribute('data-animate-sbs-event') && el.getAttribute('data-animate-sbs-event') != "hover" && el.getAttribute('data-animate-sbs-event') != "click" && el.getAttribute('data-animate-sbs-event') != "scroll") { el.classList.add('t-sbs-anim_started'); } if (el.hasAttribute('data-animate-style')) { el.classList.add('t-animate_started'); } }); } let isIos = function() { var agent = window.navigator.userAgent; var start = agent.indexOf( 'OS ' ); if( ( agent.indexOf( 'iPhone' ) > -1 || agent.indexOf( 'iPad' ) > -1 ) && start > -1 ){ return true; } return false; }; let isAndroid = function() { var agent = window.navigator.userAgent; return agent.toLowerCase().indexOf("android") > -1; }; var scrollTop; function iosLockScroll() { if (isIos()) { scrollTop = window.pageYOffset || document.documentElement.scrollTop; document.body.classList.add('locked'); document.body.style.top = -(scrollTop) + 'px'; } } function iosUnlockScroll(x = "nolink") { if (isIos()) { if (document.body.classList.contains('locked')) { document.body.classList.remove('locked'); window.scrollTo(0, scrollTop); if (x && x != "nolink" && !window.nlm082openLinks.includes(x)) { setTimeout(function() { document.querySelector(`a[href="${x}"]`).click(); }, 500); } } } } function androidScrollFix(x) { if (isAndroid()) { setTimeout(function() { if (document.querySelector(`${x}`)) { document.querySelector(`${x}`).scrollIntoView({ behavior: 'auto', block: 'start' }); } }, 500); } } blk.querySelectorAll('[href]').forEach(function(item) { item.addEventListener("click", function(e) { iosUnlockScroll(item.getAttribute("href")); androidScrollFix(item.getAttribute("href")); }); }); var isAnimOnce = false; menuBlock.style.transform = 'translateX(100%)'; menuBlock.style.overflow = "hidden"; window.addEventListener("click", function(event) { let clickId = event.target.closest(".tn-elem"); if (document.querySelector(".t-body.nolimPopUp") && document.querySelector(".nolimShow1126836916") && !clickId && !event.target.hasAttribute("nlm082") && !event.target.classList.contains("t-slds__arrow")) { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; iosUnlockScroll(); } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } }); window.addEventListener("click", function(event) { if (document.querySelector(".t-body.nolimPopUp") && event.target.hasAttribute("nlm082") && event.target.getAttribute("nlm082") != "1126836916") { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } }); c.forEach((function(item) { item.setAttribute("nlm082", "1126836916"); item.addEventListener("click", (function(e) { e.preventDefault(); if (document.querySelector(".nolimShow1126836916")) { isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; iosUnlockScroll(); } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); } else { removeAnimation("#rec1126836916"); blk.style.display = "block"; setTimeout(function() { isMenuOpen = true; window.nlm082open = true; menuBlock.style.opacity = "1"; menuBlock.style.pointerEvents = "auto"; menuBlock.style.transform = `translateX(-${scrollWidth}px)`; menuBlock.style.marginRight = `-${scrollWidth}px`; document.querySelector("html").style.overflow = "hidden"; iosLockScroll(); setTimeout(function() { menuBlock.classList.add("nolimShow1126836916"); document.querySelector(".t-body").classList.add("nolimPopUp"); if (!isAnimOnce) { addAnimation("#rec1126836916"); } isAnimOnce = false; }, 400); setTimeout(function() { "y" === window.lazy && t_lazyload_update(); typeof t_slds_updateSlider != "undefined" && t_slds_updateSlider("1126836916"); if (document.querySelector("#rec1126836916") && document.querySelector("#rec1126836916").getAttribute("data-record-type") == "396") { t396_doResize('1126836916'); } }, 300); }, 0); } })); })); document.querySelectorAll(".nolimClose082").forEach((function(item) { item.classList.add("nolim_popup_close"); })); const isYaBrowser=navigator.userAgent.includes("YaBrowser"); menuBlock.querySelectorAll(".nolimClose082, .nolim_popup_close").forEach((function(item) { item.addEventListener("click", (function() { const href='#menu'; const selector='[name='+href.substr(1)+']'; const tildaAnchorLink=document.querySelector(selector); if(isYaBrowser && tildaAnchorLink){ tildaAnchorLink.setAttribute('id', href.substr(1)); const newLink=document.createElement("a"); newLink.setAttribute('href', href); menuBlock.appendChild(newLink); newLink.click(); newLink.remove(); } isMenuOpen = false; window.nlm082open = false; setTimeout(function() { menuBlock.style.opacity = "0"; menuBlock.style.pointerEvents = "none"; 							console.log('blk',blk); blk.style.display = "none"; document.querySelector(".t-body").classList.remove("nolimPopUp"); if (window.nlm020obj == undefined || (window.nlm020obj && !window.nlm020obj.isOpen)) { document.querySelector("html").style.overflow = "visible"; if (!item.querySelector(".tn-atom[href]") && !item.querySelector(".tn-atom a[href]")) { iosUnlockScroll(); } } removeAnimation("#rec1126836916"); }, 400); menuBlock.style.transform = 'translateX(100%)'; menuBlock.classList.remove("nolimShow1126836916"); })); })); } }); },500); })); }()); </script> <style> body.locked { overflow-y: scroll; width: 100%; } #rec1126836916 { display: none; z-index: 999999; } #rec1126836916 .t396__filter, #rec1126836916 .t396__carrier { pointer-events: none!important; height: 0!important; min-height: 0!important; } #rec1126836916 .t396__artboard { min-height: 0px!important; height: 0px!important; overflow: visible !important; position: relative !important; } .nolimClose082 { cursor: pointer; } #rec1126836916 .t396 { position: fixed; top: 0; right: 0; left: 0; bottom: 0; z-index: 999999!important; transition: transform ease-in-out 0.4s; opacity: 0; pointer-events: none; } .nolimPopUp { height: 100vh; min-height: 100vh; overflow: visible !important; } .nolimShow1126836916 { overflow-y: auto !important; } </style> <!-- nominify end --> </div> </div> </div> </div> <div id="rec1127385721" class="r t-rec" style=" " data-animationappear="off" data-record-type="890"> <!-- t890 --> <div class="t890" style="display: none; opacity:1; position:fixed; z-index:99990; bottom:20px;right:20px;"> <button type="button"
class="t890__arrow"
aria-label="Вернуться к началу страницы"
style="box-shadow:0px 0px 10px rgba(0,0,0,0.2);"> <svg role="presentation" width="50" height="50" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="50" height="50" rx="50" fill="#8191e2" fill-opacity="0.90" stroke="none" /> <path d="M14 28L25 18l10 10" stroke="#f6f6f6" stroke-width="1" fill="none"/> </svg> </button> </div> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t890_init',function(){t890_init('1127385721','700');});});</script> <style>@media screen and (min-width:981px){#rec1127385721 .t890__arrow:hover svg path{stroke:#f6f6f6;stroke-width:1;}#rec1127385721 .t890__arrow:focus-visible svg path{stroke:#f6f6f6;stroke-width:1;}#rec1127385721 .t890__arrow:hover svg rect{fill:#6377d4;fill-opacity:1;}#rec1127385721 .t890__arrow:focus-visible svg rect{fill:#6377d4;fill-opacity:1;}}#rec1127385721 .t890__arrow{border-radius:53px;}</style> </div> <div id="rec1136479131" class="r t-rec" style=" " data-animationappear="off" data-record-type="706"> <!--tcart--> <!-- @classes: t-text t-text_xs t-name t-name_xs t-name_md t-btn t-btn_sm --> <script>t_onReady(function() {setTimeout(function() {t_onFuncLoad('tcart__init',function() {tcart__init('1136479131');});},50);var userAgent=navigator.userAgent.toLowerCase();var body=document.body;if(!body) return;if(userAgent.indexOf('instagram')!==-1&&userAgent.indexOf('iphone')!==-1) {body.style.position='relative';}
var rec=document.querySelector('#rec1136479131');if(!rec) return;var cartWindow=rec.querySelector('.t706__cartwin,.t706__cartpage');var allRecords=document.querySelector('.t-records');var currentMode=allRecords.getAttribute('data-tilda-mode');if(cartWindow&&currentMode!=='edit'&&currentMode!=='preview') {cartWindow.addEventListener('scroll',t_throttle(function() {if(window.lazy==='y'||document.querySelector('#allrecords').getAttribute('data-tilda-lazy')==='yes') {t_onFuncLoad('t_lazyload_update',function() {t_lazyload_update();});}},500));}});</script> <div class="t706" data-opencart-onorder="yes" data-project-currency="AED" data-project-currency-side="r" data-project-currency-sep="." data-project-currency-dec="00" data-project-currency-code="AED" data-payment-system="stripe"> <div class="t706__carticon" style="top:initial;bottom:80px;right:20px;"> <div class="t706__carticon-text t-name t-name_xs"></div> <div class="t706__carticon-wrapper"> <div class="t706__carticon-imgwrap" style="background-color:#8191e2;"> <svg role="img" style="stroke:#f6f6f6;" class="t706__carticon-img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64"> <path fill="none" stroke-width="2" stroke-miterlimit="10" d="M44 18h10v45H10V18h10z"/> <path fill="none" stroke-width="2" stroke-miterlimit="10" d="M22 24V11c0-5.523 4.477-10 10-10s10 4.477 10 10v13"/> </svg> </div> <div class="t706__carticon-counter js-carticon-counter" style="background-color:#f5e39c;color:#6377d4;"></div> </div> </div> <div class="t706__cartwin" style="display: none;"> <div class="t706__close t706__cartwin-close"> <button type="button" class="t706__close-button t706__cartwin-close-wrapper" aria-label="Закрыть корзину"> <svg role="presentation" class="t706__close-icon t706__cartwin-close-icon" width="23px" height="23px" viewBox="0 0 23 23" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"> <g stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd"> <rect transform="translate(11.313708, 11.313708) rotate(-45.000000) translate(-11.313708, -11.313708) " x="10.3137085" y="-3.6862915" width="2" height="30"></rect> <rect transform="translate(11.313708, 11.313708) rotate(-315.000000) translate(-11.313708, -11.313708) " x="10.3137085" y="-3.6862915" width="2" height="30"></rect> </g> </svg> </button> </div> <div class="t706__cartwin-content"> <div class="t706__cartwin-top"> <div class="t706__cartwin-heading t-name t-name_xl"></div> </div> <div class="t706__cartwin-products"></div> <div class="t706__cartwin-bottom"> <div class="t706__cartwin-prodamount-wrap t-descr t-descr_sm"> <span class="t706__cartwin-prodamount-label"></span> <span class="t706__cartwin-prodamount"></span> </div> </div> <div class="t706__orderform t-input_nomargin"> <form
id="form1136479131" name='form1136479131' role="form" action='#' method='POST' data-formactiontype="2" data-inputbox=".t-input-group" 
class="t-form js-form-proccess t-form_inputs-total_3 t-form_bbonly" data-formsended-callback="t706_onSuccessCallback"> <input type="hidden" name="formservices[]" value="2516383d1e8f37b6028e03c607bef9ee" class="js-formaction-services"> <input type="hidden" name="formservices[]" value="42beafd0361231338f85499263a54b7a" class="js-formaction-services"> <input type="hidden" name="tildaspec-formname" tabindex="-1" value="Cart"> <!-- @classes t-title t-text t-btn --> <div class="js-successbox t-form__successbox t-text t-text_md"
aria-live="polite"
style="display:none;color:#8191e2;background-color:#f5e39c;" data-success-message="Payment Successful!&lt;br /&gt;&lt;br /&gt;Thank you for your order — it&#039;s been received and is now being processed. We&#039;ll be in touch soon with the next steps!"></div> <div class="t-form__inputsbox t-form__inputsbox_inrow"> <div
class=" t-input-group t-input-group_nm " data-input-lid="3311158466360" data-field-type="nm" data-field-name="Name"> <label
for='input_3311158466360'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466360" data-redactor-toolbar="no"
field="li_title__3311158466360"
style="color:;font-weight:900;font-family: 'GillSans';">Your Name</label> <div class="t-input-block "> <input
type="text"
autocomplete="name"
name="Name"
id="input_3311158466360"
class="t-input js-tilda-rule t-input_bbonly"
value="" data-tilda-req="1" aria-required="true" data-tilda-rule="name"
aria-describedby="error_3311158466360"
style="color:#000000;border:1px solid #000000;"> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466360"></div> </div> <div
class=" t-input-group t-input-group_em " data-input-lid="3311158466361" data-field-type="em" data-field-name="Email"> <label
for='input_3311158466361'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466361" data-redactor-toolbar="no"
field="li_title__3311158466361"
style="color:;font-weight:900;font-family: 'GillSans';">Your Email</label> <div class="t-input-block "> <input
type="email"
autocomplete="email"
name="Email"
id="input_3311158466361"
class="t-input js-tilda-rule t-input_bbonly"
value="" data-tilda-req="1" aria-required="true" data-tilda-rule="email"
aria-describedby="error_3311158466361"
style="color:#000000;border:1px solid #000000;"> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466361"></div> </div> <div
class=" t-input-group t-input-group_ph " data-input-lid="3311158466362" data-field-async="true" data-field-type="ph" data-field-name="Phone"> <label
for='input_3311158466362'
class="t-input-title t-descr t-descr_md"
id="field-title_3311158466362" data-redactor-toolbar="no"
field="li_title__3311158466362"
style="color:;font-weight:900;font-family: 'GillSans';">Phone</label> <div class="t-input-block "> <input
type="tel"
autocomplete="tel"
name="Phone"
id="input_3311158466362" data-phonemask-init="no" data-phonemask-id="1136479131" data-phonemask-lid="3311158466362" data-phonemask-maskcountry="AE" class="t-input js-phonemask-input js-tilda-rule t-input_bbonly"
value=""
placeholder="+1(999)999-9999" data-tilda-req="1" aria-required="true" aria-describedby="error_3311158466362"
style="color:#000000;border:1px solid #000000;"> <script type="text/javascript">t_onReady(function() {t_onFuncLoad('t_loadJsFile',function() {t_loadJsFile('../static.tildacdn.one/js/tilda-phone-mask-1.1.min.js',function() {t_onFuncLoad('t_form_phonemask_load',function() {var phoneMasks=document.querySelectorAll('#rec1136479131 [data-phonemask-lid="3311158466362"]');t_form_phonemask_load(phoneMasks);});})})});</script> </div> <div class="t-input-error" aria-live="polite" id="error_3311158466362"></div> </div> <div class="t-form__errorbox-middle"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> <div class="t-form__submit"> <button
type="submit"
class="t-submit"
style="color:#ffffff;background-color:#8191e2;border-radius:40px; -moz-border-radius:40px; -webkit-border-radius:40px;font-family:GillSans;font-weight:400;" data-field="buttontitle" data-buttonfieldset="button">
Order Now </button> </div> </div> <div class="t-form__errorbox-bottom"> <!--noindex--> <div
class="js-errorbox-all t-form__errorbox-wrapper"
style="display:none;" data-nosnippet
tabindex="-1"
aria-label="Ошибки при заполнении формы"> <ul
role="list"
class="t-form__errorbox-text t-text t-text_md"> <li class="t-form__errorbox-item js-rule-error js-rule-error-all"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-req"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-email"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-name"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-phone"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-minlength"></li> <li class="t-form__errorbox-item js-rule-error js-rule-error-string"></li> </ul> </div> <!--/noindex--> </div> </form> <style> #rec1136479131 .t-form__successbox,#rec1136479131 .t-form__errorbox-wrapper{border-radius:15px;}</style> <style>#rec1136479131 input::-webkit-input-placeholder {color:#000000;opacity:0.5;}#rec1136479131 input::-moz-placeholder{color:#000000;opacity:0.5;}#rec1136479131 input:-moz-placeholder {color:#000000;opacity:0.5;}#rec1136479131 input:-ms-input-placeholder{color:#000000;opacity:0.5;}#rec1136479131 textarea::-webkit-input-placeholder {color:#000000;opacity:0.5;}#rec1136479131 textarea::-moz-placeholder{color:#000000;opacity:0.5;}#rec1136479131 textarea:-moz-placeholder {color:#000000;opacity:0.5;}#rec1136479131 textarea:-ms-input-placeholder{color:#000000;opacity:0.5;}</style> </div> <div class="t706__form-bottom-text t-text t-text_xs">Our manager will follow up with you shortly</div> </div> </div> <div class="t706__cartdata"> </div> </div> <style></style> <style>@media (hover:hover),(min-width:0\0) {#rec1136479131 .t-submit:hover {background-color:#6377d4 !important;}#rec1136479131 .t-submit:focus-visible {background-color:#6377d4 !important;}}</style> <style>.t-menuwidgeticons__cart .t-menuwidgeticons__icon-counter{background-color:#f5e39c;}</style> <style>.t-menuwidgeticons__cart .t-menuwidgeticons__icon-counter{color:#6377d4 !important;}</style> <style> #rec1136479131 .t706__cartwin-content{border-radius:30px;}</style> <!--/tcart--> </div> </header> <!--/header--> <div id="rec1126876471" class="r t-rec" style=" " data-animationappear="off" data-record-type="396"> <!-- T396 --> <style>#rec1126876471 .t396__artboard {height:1337px;background-color:#f5f5f5;}#rec1126876471 .t396__filter {height:1337px;}#rec1126876471 .t396__carrier{height:1337px;background-position:center center;background-attachment:scroll;background-size:cover;background-repeat:no-repeat;}@media screen and (max-width:1199px) {#rec1126876471 .t396__artboard,#rec1126876471 .t396__filter,#rec1126876471 .t396__carrier {}#rec1126876471 .t396__filter {}#rec1126876471 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:959px) {#rec1126876471 .t396__artboard,#rec1126876471 .t396__filter,#rec1126876471 .t396__carrier {height:2277px;}#rec1126876471 .t396__filter {}#rec1126876471 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:639px) {#rec1126876471 .t396__artboard,#rec1126876471 .t396__filter,#rec1126876471 .t396__carrier {}#rec1126876471 .t396__filter {}#rec1126876471 .t396__carrier {background-attachment:scroll;}}@media screen and (max-width:413px) {#rec1126876471 .t396__artboard,#rec1126876471 .t396__filter,#rec1126876471 .t396__carrier {height:2700px;}#rec1126876471 .t396__filter {}#rec1126876471 .t396__carrier {background-attachment:scroll;}}#rec1126876471 .tn-elem[data-elem-id="1750696614681"]{z-index:3;top:156px;left:calc(50% - 600px + 119px);width:428px;height:398px;}#rec1126876471 .tn-elem[data-elem-id="1750696614681"] .tn-atom {border-radius:16px 16px 16px 16px;background-color:#e2ddd5;background-position:center center;background-size:cover;background-repeat:no-repeat;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614681"] {display:table;left:calc(50% - 480px + 20px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614681"] {display:table;top:106px;left:calc(50% - 320px + 90px);width:460px;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614681"] {display:table;left:calc(50% - 207px + 15px);width:384px;height:375px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614681"] {display:table;width:360px;height:350px;}}#rec1126876471 .tn-elem[data-elem-id="1750696614716"]{color:#000000;text-align:LEFT;z-index:3;top:163px;left:calc(50% - 600px + 587px);width:582px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614716"] .tn-atom {vertical-align:middle;color:#000000;font-size:40px;font-family:'GillSans',Arial,sans-serif;line-height:0.85;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614716"] {display:table;top:162px;left:calc(50% - 480px + 477px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614716"] {display:table;top:534px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614716"] {display:table;top:511px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614716"] .tn-atom{font-size:35px;line-height:1;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614716"] {display:table;top:491px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614722"]{color:#000000;text-align:LEFT;z-index:3;top:208px;left:calc(50% - 600px + 587px);width:212px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614722"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614722"] {display:table;top:207px;left:calc(50% - 480px + 477px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614722"] {display:table;top:578px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614722"] {display:table;top:550px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614722"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614722"] {display:table;top:534px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614726"]{color:#000000;text-align:LEFT;z-index:3;top:256px;left:calc(50% - 600px + 587px);width:476px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614726"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614726"] {display:table;top:254px;left:calc(50% - 480px + 477px);width:432px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614726"] {display:table;top:625px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614726"] {display:table;top:597px;left:calc(50% - 207px + 15px);width:382px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614726"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614726"] {display:table;top:581px;width:361px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614730"]{color:#000000;text-align:LEFT;z-index:3;top:939px;left:calc(50% - 600px + 587px);width:476px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614730"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614730"] {display:table;top:942px;left:calc(50% - 480px + 477px);width:432px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614730"] {display:table;top:1308px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614730"] {display:table;top:1295px;left:calc(50% - 207px + 15px);width:382px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614730"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614730"] {display:table;top:1706px;width:361px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614734"]{color:#000000;text-align:LEFT;z-index:3;top:1056px;left:calc(50% - 600px + 587px);width:458px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614734"] .tn-atom {vertical-align:middle;color:#000000;font-size:20px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614734"] {display:table;top:1059px;left:calc(50% - 480px + 477px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614734"] {display:table;top:1425px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614734"] {display:table;top:1410px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614734"] {display:table;top:1821px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614738"]{color:#000000;text-align:LEFT;z-index:3;top:1096px;left:calc(50% - 600px + 589px);width:458px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614738"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614738"] {display:table;top:1097px;left:calc(50% - 480px + 477px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614738"] {display:table;top:1463px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614738"] {display:table;top:1448px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614738"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614738"] {display:table;top:1859px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614741"]{color:#000000;text-align:LEFT;z-index:3;top:1182px;left:calc(50% - 600px + 589px);width:458px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614741"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614741"] {display:table;top:1183px;left:calc(50% - 480px + 477px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614741"] {display:table;top:1549px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614741"] {display:table;top:1537px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614741"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614741"] {display:table;top:1962px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614745"]{color:#000000;text-align:LEFT;z-index:3;top:1117px;left:calc(50% - 600px + 589px);width:476px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614745"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614745"] {display:table;top:1118px;left:calc(50% - 480px + 477px);width:432px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614745"] {display:table;top:1484px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614745"] {display:table;top:1469px;left:calc(50% - 207px + 15px);width:382px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614745"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614745"] {display:table;top:1880px;left:calc(50% - 195px + 15px);width:361px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614749"]{color:#000000;text-align:LEFT;z-index:3;top:1205px;left:calc(50% - 600px + 589px);width:476px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614749"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614749"] {display:table;top:1204px;left:calc(50% - 480px + 477px);width:432px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614749"] {display:table;top:1570px;left:calc(50% - 320px + 90px);width:458px;height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614749"] {display:table;top:1558px;left:calc(50% - 207px + 15px);width:382px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614749"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614749"] {display:table;top:1983px;left:calc(50% - 195px + 15px);width:361px;height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614752"]{color:#000000;text-align:LEFT;z-index:3;top:336px;left:calc(50% - 600px + 587px);width:62px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614752"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614752"] {display:table;top:339px;left:calc(50% - 480px + 477px);width:58px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614752"] {display:table;top:705px;left:calc(50% - 320px + 90px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614752"] .tn-atom {background-size:cover;opacity:1;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614752"] {display:table;top:680px;left:calc(50% - 207px + 15px);width:60px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614752"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614752"] {display:table;top:660px;left:calc(50% - 195px + 15px);width:57px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614752"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750696614757"]{color:#000000;text-align:LEFT;z-index:3;top:397px;left:calc(50% - 600px + 587px);width:47px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614757"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614757"] {display:table;top:400px;left:calc(50% - 480px + 477px);width:45px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614757"] {display:table;top:766px;left:calc(50% - 320px + 90px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614757"] .tn-atom {background-size:cover;opacity:1;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614757"] {display:table;top:739px;left:calc(50% - 207px + 15px);width:46px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614757"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614757"] {display:table;top:734px;left:calc(50% - 195px + 15px);width:43px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614757"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750696614763"]{color:#000000;text-align:LEFT;z-index:3;top:336px;left:calc(50% - 600px + 667px);width:396px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614763"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614763"] {display:table;top:339px;left:calc(50% - 480px + 548px);width:361px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614763"] {display:table;top:705px;left:calc(50% - 320px + 167px);width:355px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614763"] .tn-atom {background-size:cover;opacity:1;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614763"] {display:table;top:680px;left:calc(50% - 207px + 92px);width:305px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614763"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614763"] {display:table;top:660px;left:calc(50% - 195px + 88px);width:288px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614763"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750696614769"]{color:#000000;text-align:LEFT;z-index:3;top:397px;left:calc(50% - 600px + 667px);width:396px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614769"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614769"] {display:table;top:400px;left:calc(50% - 480px + 548px);width:361px;height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614769"] {display:table;top:766px;left:calc(50% - 320px + 167px);width:355px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614769"] .tn-atom {background-size:cover;opacity:1;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614769"] {display:table;top:739px;left:calc(50% - 207px + 92px);width:273px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614769"] .tn-atom {vertical-align:middle;white-space:normal;font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614769"] {display:table;top:734px;left:calc(50% - 195px + 88px);width:288px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614769"] .tn-atom {vertical-align:middle;white-space:normal;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750698733465"]{z-index:3;top:464px;left:calc(50% - 600px + 587px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750698733465"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698733465"] {display:table;top:467px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698733465"] {display:table;top:833px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698733465"] {display:table;top:820px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698733465"] {display:table;top:815px;width:360px;}}#rec1126876471 .tn-elem[data-elem-id="1750698851388"]{z-index:3;top:905px;left:calc(50% - 600px + 587px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750698851388"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698851388"] {display:table;top:908px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698851388"] {display:table;top:1274px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698851388"] {display:table;top:1261px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698851388"] {display:table;top:1672px;width:360px;}}#rec1126876471 .tn-elem[data-elem-id="1750698882807"]{z-index:3;top:1022px;left:calc(50% - 600px + 587px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750698882807"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698882807"] {display:table;top:1025px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698882807"] {display:table;top:1391px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698882807"] {display:table;top:1376px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698882807"] {display:table;top:1787px;width:360px;}}#rec1126876471 .tn-elem[data-elem-id="1750698891577"]{z-index:3;top:1272px;left:calc(50% - 600px + 587px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750698891577"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698891577"] {display:table;top:1287px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698891577"] {display:table;top:1637px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698891577"] {display:table;top:1639px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698891577"] {display:table;top:2064px;width:360px;}}#rec1126876471 .tn-elem[data-elem-id="1750697936303"]{color:#000000;text-align:center;z-index:3;top:101px;left:calc(50% - 600px + 42px);width:200px;height:55px;}#rec1126876471 .tn-elem[data-elem-id="1750697936303"] .tn-atom{color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750697936303"] {display:table;left:calc(50% - 480px + -58px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750697936303"] {display:table;top:51px;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750697936303"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750697936303"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750854833026"]{z-index:3;top:626px;left:calc(50% - 600px + 821px);width:224px;height:118px;}#rec1126876471 .tn-elem[data-elem-id="1750854833026"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833026"] {display:table;top:629px;left:calc(50% - 480px + 711px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833026"] {display:table;top:995px;left:calc(50% - 320px + 324px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833026"] {display:table;top:982px;left:calc(50% - 207px + 212px);width:187px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833026"] {display:table;top:1329px;left:calc(50% - 195px + 15px);width:360px;height:150px;}}#rec1126876471 .tn-elem[data-elem-id="1750854833055"]{z-index:3;top:754px;left:calc(50% - 600px + 587px);width:224px;height:118px;}#rec1126876471 .tn-elem[data-elem-id="1750854833055"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833055"] {display:table;top:757px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833055"] {display:table;top:1123px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833055"] {display:table;top:1110px;left:calc(50% - 207px + 15px);width:187px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833055"] {display:table;top:1489px;left:calc(50% - 195px + 15px);width:360px;height:150px;}}#rec1126876471 .tn-elem[data-elem-id="1750854833042"]{z-index:3;top:626px;left:calc(50% - 600px + 587px);width:224px;height:118px;}#rec1126876471 .tn-elem[data-elem-id="1750854833042"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833042"] {display:table;top:629px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833042"] {display:table;top:995px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833042"] {display:table;top:982px;left:calc(50% - 207px + 15px);width:187px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833042"] {display:table;top:1169px;left:calc(50% - 195px + 15px);width:360px;height:150px;}}#rec1126876471 .tn-elem[data-elem-id="1750854833085"]{color:#000000;text-align:center;z-index:3;top:842px;left:calc(50% - 600px + 743px);width:53px;height:22px;}#rec1126876471 .tn-elem[data-elem-id="1750854833085"] .tn-atom{color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833085"] {display:table;top:845px;left:calc(50% - 480px + 633px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833085"] {display:table;top:1211px;left:calc(50% - 320px + 246px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833085"] {display:table;top:1196px;left:calc(50% - 207px + 150px);width:38px;height:17px;}#rec1126876471 .tn-elem[data-elem-id="1750854833085"] .tn-atom{line-height:1.2;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833085"] {display:table;top:1607px;left:calc(50% - 195px + 322px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833083"]{color:#ffffff;text-align:center;z-index:3;top:795px;left:calc(50% - 600px + 604px);width:65px;height:21px;}#rec1126876471 .tn-elem[data-elem-id="1750854833083"] .tn-atom{color:#ffffff;font-size:10px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:900;border-radius:30px 30px 30px 30px;background-color:#000000;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833083"] {display:table;top:798px;left:calc(50% - 480px + 494px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833083"] {display:table;top:1164px;left:calc(50% - 320px + 107px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833083"] {display:table;top:1151px;left:calc(50% - 207px + 27px);}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833083"] {display:table;top:1504px;left:calc(50% - 195px + 295px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833081"]{color:#000000;text-align:center;z-index:3;top:712px;left:calc(50% - 600px + 982px);width:53px;height:22px;}#rec1126876471 .tn-elem[data-elem-id="1750854833081"] .tn-atom{color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833081"] {display:table;top:715px;left:calc(50% - 480px + 872px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833081"] {display:table;top:1081px;left:calc(50% - 320px + 485px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833081"] {display:table;top:1068px;left:calc(50% - 207px + 347px);width:38px;height:17px;}#rec1126876471 .tn-elem[data-elem-id="1750854833081"] .tn-atom{line-height:1.2;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833081"] {display:table;top:1447px;left:calc(50% - 195px + 322px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833004"]{z-index:3;top:498px;left:calc(50% - 600px + 821px);width:224px;height:118px;}#rec1126876471 .tn-elem[data-elem-id="1750854833004"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833004"] {display:table;top:501px;left:calc(50% - 480px + 711px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833004"] {display:table;top:867px;left:calc(50% - 320px + 324px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833004"] {display:table;top:854px;left:calc(50% - 207px + 210px);width:187px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833004"] {display:table;top:1009px;left:calc(50% - 195px + 15px);width:360px;height:150px;}}#rec1126876471 .tn-elem[data-elem-id="1750854832988"]{z-index:3;top:498px;left:calc(50% - 600px + 587px);width:224px;height:118px;}#rec1126876471 .tn-elem[data-elem-id="1750854832988"] .tn-atom {border-radius:10px 10px 10px 10px;background-color:#e2efd9;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854832988"] {display:table;top:501px;left:calc(50% - 480px + 477px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854832988"] {display:table;top:867px;left:calc(50% - 320px + 90px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854832988"] {display:table;top:854px;left:calc(50% - 207px + 15px);width:187px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854832988"] {display:table;top:849px;left:calc(50% - 195px + 15px);width:360px;height:150px;}}#rec1126876471 .tn-elem[data-elem-id="1750854833078"]{color:#ffffff;text-align:center;z-index:3;top:665px;left:calc(50% - 600px + 836px);width:65px;height:21px;}#rec1126876471 .tn-elem[data-elem-id="1750854833078"] .tn-atom{color:#ffffff;font-size:10px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:900;border-radius:30px 30px 30px 30px;background-color:#000000;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833078"] {display:table;top:668px;left:calc(50% - 480px + 726px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833078"] {display:table;top:1034px;left:calc(50% - 320px + 339px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833078"] {display:table;top:1021px;left:calc(50% - 207px + 227px);}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833078"] {display:table;top:1344px;left:calc(50% - 195px + 295px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833075"]{color:#000000;text-align:center;z-index:3;top:712px;left:calc(50% - 600px + 743px);width:53px;height:22px;}#rec1126876471 .tn-elem[data-elem-id="1750854833075"] .tn-atom{color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833075"] {display:table;top:715px;left:calc(50% - 480px + 633px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833075"] {display:table;top:1081px;left:calc(50% - 320px + 246px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833075"] {display:table;top:1068px;left:calc(50% - 207px + 152px);width:38px;height:17px;}#rec1126876471 .tn-elem[data-elem-id="1750854833075"] .tn-atom{line-height:1.2;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833075"] {display:table;top:1287px;left:calc(50% - 195px + 322px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833073"]{color:#ffffff;text-align:center;z-index:3;top:665px;left:calc(50% - 600px + 602px);width:65px;height:21px;}#rec1126876471 .tn-elem[data-elem-id="1750854833073"] .tn-atom{color:#ffffff;font-size:10px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:900;border-radius:30px 30px 30px 30px;background-color:#000000;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833073"] {display:table;top:668px;left:calc(50% - 480px + 492px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833073"] {display:table;top:1034px;left:calc(50% - 320px + 105px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833073"] {display:table;top:1021px;left:calc(50% - 207px + 27px);}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833073"] {display:table;top:1184px;left:calc(50% - 195px + 295px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833070"]{color:#000000;text-align:center;z-index:3;top:583px;left:calc(50% - 600px + 982px);width:53px;height:22px;}#rec1126876471 .tn-elem[data-elem-id="1750854833070"] .tn-atom{color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:400;border-radius:30px 30px 30px 30px;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833070"] {display:table;top:586px;left:calc(50% - 480px + 872px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833070"] {display:table;top:952px;left:calc(50% - 320px + 485px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833070"] {display:table;top:939px;left:calc(50% - 207px + 347px);width:38px;height:17px;}#rec1126876471 .tn-elem[data-elem-id="1750854833070"] .tn-atom{line-height:1.2;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833070"] {display:table;top:1125px;left:calc(50% - 195px + 322px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833068"]{color:#ffffff;text-align:center;z-index:3;top:537px;left:calc(50% - 600px + 835px);width:65px;height:21px;}#rec1126876471 .tn-elem[data-elem-id="1750854833068"] .tn-atom{color:#ffffff;font-size:10px;font-family:'GillSans',Arial,sans-serif;line-height:1.55;font-weight:900;border-radius:30px 30px 30px 30px;background-color:#000000;background-position:center center;border-color:transparent ;border-style:solid;transition:background-color 0.2s ease-in-out,color 0.2s ease-in-out,border-color 0.2s ease-in-out;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833068"] {display:table;top:540px;left:calc(50% - 480px + 725px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833068"] {display:table;top:906px;left:calc(50% - 320px + 338px);}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833068"] {display:table;top:893px;left:calc(50% - 207px + 224px);}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833068"] {display:table;top:1024px;left:calc(50% - 195px + 295px);}}#rec1126876471 .tn-elem[data-elem-id="1750854833065"]{color:#000000;text-align:LEFT;z-index:3;top:827px;left:calc(50% - 600px + 602px);width:97px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833065"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833065"] {display:table;top:830px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833065"] {display:table;top:1196px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833065"] {display:table;top:1183px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833065"] {display:table;top:1584px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833065"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:18px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833061"]{color:#000000;text-align:LEFT;z-index:3;top:845px;left:calc(50% - 600px + 602px);width:70px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833061"] .tn-atom {vertical-align:middle;color:#000000;font-size:10px;font-family:'GillSans',Arial,sans-serif;font-weight:400;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833061"] {display:table;top:848px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833061"] {display:table;top:1214px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833061"] {display:table;top:1201px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833061"] {display:table;top:1608px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833061"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:13px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833058"]{color:#000000;text-align:LEFT;z-index:3;top:771px;left:calc(50% - 600px + 602px);width:163px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833058"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833058"] {display:table;top:774px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833058"] {display:table;top:1140px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833058"] {display:table;top:1127px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833058"] {display:table;top:1506px;left:calc(50% - 195px + 27px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833058"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:16px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833052"]{color:#000000;text-align:LEFT;z-index:3;top:699px;left:calc(50% - 600px + 602px);width:154px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833052"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833052"] {display:table;top:702px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833052"] {display:table;top:1068px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833052"] {display:table;top:1055px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833052"] {display:table;top:1264px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833052"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:18px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833050"]{color:#000000;text-align:LEFT;z-index:3;top:717px;left:calc(50% - 600px + 602px);width:63px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833050"] .tn-atom {vertical-align:middle;color:#000000;font-size:10px;font-family:'GillSans',Arial,sans-serif;font-weight:400;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833050"] {display:table;top:720px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833050"] {display:table;top:1086px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833050"] {display:table;top:1073px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833050"] {display:table;top:1288px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833050"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:13px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833046"]{color:#000000;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 602px);width:155px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833046"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833046"] {display:table;top:646px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833046"] {display:table;top:1012px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833046"] {display:table;top:999px;left:calc(50% - 207px + 27px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833046"] {display:table;top:1186px;left:calc(50% - 195px + 27px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833046"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:16px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833038"]{color:#000000;text-align:LEFT;z-index:3;top:699px;left:calc(50% - 600px + 836px);width:154px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833038"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833038"] {display:table;top:702px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833038"] {display:table;top:1068px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833038"] {display:table;top:1055px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833038"] {display:table;top:1424px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833038"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:18px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833036"]{color:#000000;text-align:LEFT;z-index:3;top:717px;left:calc(50% - 600px + 836px);width:71px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833036"] .tn-atom {vertical-align:middle;color:#000000;font-size:10px;font-family:'GillSans',Arial,sans-serif;font-weight:400;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833036"] {display:table;top:720px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833036"] {display:table;top:1086px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833036"] {display:table;top:1073px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833036"] {display:table;top:1448px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833036"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:13px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833029"]{color:#000000;text-align:LEFT;z-index:3;top:643px;left:calc(50% - 600px + 836px);width:155px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833029"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833029"] {display:table;top:646px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833029"] {display:table;top:1012px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833029"] {display:table;top:999px;left:calc(50% - 207px + 227px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833029"] {display:table;top:1346px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833029"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:16px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833019"]{color:#000000;text-align:LEFT;z-index:3;top:571px;left:calc(50% - 600px + 836px);width:112px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833019"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833019"] {display:table;top:574px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833019"] {display:table;top:940px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833019"] {display:table;top:927px;left:calc(50% - 207px + 224px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833019"] {display:table;top:1104px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833019"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:18px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833015"]{color:#000000;text-align:LEFT;z-index:3;top:589px;left:calc(50% - 600px + 836px);width:93px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833015"] .tn-atom {vertical-align:middle;color:#000000;font-size:10px;font-family:'GillSans',Arial,sans-serif;font-weight:400;opacity:0.5;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833015"] {display:table;top:592px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833015"] {display:table;top:958px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833015"] {display:table;top:945px;left:calc(50% - 207px + 224px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833015"] {display:table;top:1128px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833015"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:13px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854833011"]{color:#000000;text-align:LEFT;z-index:3;top:515px;left:calc(50% - 600px + 836px);width:152px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833011"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854833011"] {display:table;top:518px;left:calc(50% - 480px + 726px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854833011"] {display:table;top:884px;left:calc(50% - 320px + 339px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854833011"] {display:table;top:871px;left:calc(50% - 207px + 224px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854833011"] {display:table;top:1026px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854833011"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:16px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854832999"]{color:#000000;text-align:LEFT;z-index:3;top:585px;left:calc(50% - 600px + 751px);width:38px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832999"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854832999"] {display:table;top:588px;left:calc(50% - 480px + 641px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854832999"] {display:table;top:954px;left:calc(50% - 320px + 254px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854832999"] {display:table;top:941px;left:calc(50% - 207px + 153px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832999"] .tn-atom{line-height:1.2;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854832999"] {display:table;top:967px;left:calc(50% - 195px + 322px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750854832996"]{color:#000000;text-align:LEFT;z-index:3;top:584px;left:calc(50% - 600px + 602px);width:97px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832996"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854832996"] {display:table;top:587px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854832996"] {display:table;top:953px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854832996"] {display:table;top:940px;left:calc(50% - 207px + 28px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854832996"] {display:table;top:957px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832996"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:18px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750854832992"]{color:#000000;text-align:LEFT;z-index:3;top:515px;left:calc(50% - 600px + 602px);width:142px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832992"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:900;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750854832992"] {display:table;top:518px;left:calc(50% - 480px + 492px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750854832992"] {display:table;top:884px;left:calc(50% - 320px + 105px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750854832992"] {display:table;top:871px;left:calc(50% - 207px + 26px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750854832992"] {display:table;top:866px;left:calc(50% - 195px + 30px);width:auto;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750854832992"] .tn-atom {vertical-align:middle;white-space:nowrap;font-size:16px;background-size:cover;}}#rec1126876471 .tn-elem[data-elem-id="1750782167876"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782167876"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782167876"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782167876"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782167876"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782167876"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750782178787"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782178787"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782178787"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782178787"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782178787"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782178787"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750782198609"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782198609"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782198609"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782198609"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782198609"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782198609"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750782241236"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782241236"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782241236"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782241236"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782241236"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782241236"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750782269056"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782269056"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782269056"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782269056"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782269056"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782269056"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750782277341"]{z-index:3;top:1037px;left:calc(50% - 600px + 2000px);width:460px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750782277341"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750782277341"] {display:table;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750782277341"] {display:table;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750782277341"] {display:table;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750782277341"] {display:table;}}#rec1126876471 .tn-elem[data-elem-id="1750698212226"]{z-index:3;top:936px;left:calc(50% - 600px + 118px);width:428px;height:1px;}#rec1126876471 .tn-elem[data-elem-id="1750698212226"] .tn-atom {border-radius:0px 0px 0px 0px;background-color:#c4c4c4;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698212226"] {display:table;top:936px;left:calc(50% - 480px + 18px);}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698212226"] {display:table;top:2016px;left:calc(50% - 320px + 90px);width:460px;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698212226"] {display:table;top:2017px;left:calc(50% - 207px + 15px);width:384px;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698212226"] {display:table;top:2442px;width:360px;}}#rec1126876471 .tn-elem[data-elem-id="1750696614997"]{color:#000000;text-align:LEFT;z-index:3;top:625px;left:calc(50% - 600px + 119px);width:91px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614997"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614997"] {display:table;top:625px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614997"] {display:table;top:1702px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614997"] {display:table;top:1704px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614997"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614997"] {display:table;top:2129px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615003"]{color:#000000;text-align:LEFT;z-index:3;top:625px;left:calc(50% - 600px + 352px);width:119px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615003"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615003"] {display:table;top:625px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615003"] {display:table;top:1702px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615003"] {display:table;top:1704px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615003"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615003"] {display:table;top:2129px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615009"]{color:#000000;text-align:LEFT;z-index:3;top:691px;left:calc(50% - 600px + 119px);width:185px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615009"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615009"] {display:table;top:691px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615009"] {display:table;top:1768px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615009"] {display:table;top:1770px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615009"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615009"] {display:table;top:2195px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615016"]{color:#000000;text-align:LEFT;z-index:3;top:691px;left:calc(50% - 600px + 352px);width:130px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615016"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615016"] {display:table;top:691px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615016"] {display:table;top:1768px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615016"] {display:table;top:1770px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615016"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615016"] {display:table;top:2195px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615022"]{color:#000000;text-align:LEFT;z-index:3;top:658px;left:calc(50% - 600px + 119px);width:173px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615022"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615022"] {display:table;top:658px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615022"] {display:table;top:1735px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615022"] {display:table;top:1737px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615022"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615022"] {display:table;top:2162px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615028"]{color:#000000;text-align:LEFT;z-index:3;top:658px;left:calc(50% - 600px + 352px);width:130px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615028"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615028"] {display:table;top:658px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615028"] {display:table;top:1735px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615028"] {display:table;top:1737px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615028"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615028"] {display:table;top:2162px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615034"]{color:#000000;text-align:LEFT;z-index:3;top:724px;left:calc(50% - 600px + 119px);width:147px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615034"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615034"] {display:table;top:724px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615034"] {display:table;top:1801px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615034"] {display:table;top:1803px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615034"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615034"] {display:table;top:2228px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615041"]{color:#000000;text-align:LEFT;z-index:3;top:724px;left:calc(50% - 600px + 352px);width:119px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615041"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615041"] {display:table;top:724px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615041"] {display:table;top:1801px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615041"] {display:table;top:1803px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615041"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615041"] {display:table;top:2228px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615047"]{color:#000000;text-align:LEFT;z-index:3;top:757px;left:calc(50% - 600px + 119px);width:177px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615047"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615047"] {display:table;top:757px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615047"] {display:table;top:1834px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615047"] {display:table;top:1836px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615047"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615047"] {display:table;top:2261px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615054"]{color:#000000;text-align:LEFT;z-index:3;top:757px;left:calc(50% - 600px + 352px);width:142px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615054"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615054"] {display:table;top:757px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615054"] {display:table;top:1834px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615054"] {display:table;top:1836px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615054"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615054"] {display:table;top:2261px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615060"]{color:#000000;text-align:LEFT;z-index:3;top:790px;left:calc(50% - 600px + 119px);width:114px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615060"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615060"] {display:table;top:790px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615060"] {display:table;top:1867px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615060"] {display:table;top:1869px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615060"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615060"] {display:table;top:2294px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615066"]{color:#000000;text-align:LEFT;z-index:3;top:790px;left:calc(50% - 600px + 352px);width:100px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615066"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615066"] {display:table;top:790px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615066"] {display:table;top:1867px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615066"] {display:table;top:1869px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615066"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615066"] {display:table;top:2294px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615072"]{color:#000000;text-align:LEFT;z-index:3;top:823px;left:calc(50% - 600px + 119px);width:194px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615072"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615072"] {display:table;top:823px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615072"] {display:table;top:1900px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615072"] {display:table;top:1902px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615072"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615072"] {display:table;top:2327px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615080"]{color:#000000;text-align:LEFT;z-index:3;top:823px;left:calc(50% - 600px + 352px);width:111px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615080"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615080"] {display:table;top:823px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615080"] {display:table;top:1900px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615080"] {display:table;top:1902px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615080"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615080"] {display:table;top:2327px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615089"]{color:#000000;text-align:LEFT;z-index:3;top:856px;left:calc(50% - 600px + 119px);width:169px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615089"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615089"] {display:table;top:856px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615089"] {display:table;top:1933px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615089"] {display:table;top:1935px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615089"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615089"] {display:table;top:2360px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615097"]{color:#000000;text-align:LEFT;z-index:3;top:856px;left:calc(50% - 600px + 352px);width:150px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615097"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615097"] {display:table;top:856px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615097"] {display:table;top:1933px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615097"] {display:table;top:1935px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615097"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615097"] {display:table;top:2360px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615109"]{color:#000000;text-align:LEFT;z-index:3;top:889px;left:calc(50% - 600px + 119px);width:88px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615109"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615109"] {display:table;top:889px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615109"] {display:table;top:1966px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615109"] {display:table;top:1968px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615109"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615109"] {display:table;top:2393px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615116"]{color:#000000;text-align:LEFT;z-index:3;top:889px;left:calc(50% - 600px + 352px);width:150px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615116"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615116"] {display:table;top:889px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615116"] {display:table;top:1966px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615116"] {display:table;top:1968px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615116"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615116"] {display:table;top:2393px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705255899"]{z-index:3;top:649px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705255899"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705255899"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705255899"] {display:table;top:649px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705255899"] {display:table;top:1726px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705255899"] {display:table;top:1728px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705255899"] {display:table;top:2153px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705293701"]{z-index:3;top:682px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705293701"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705293701"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705293701"] {display:table;top:682px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705293701"] {display:table;top:1759px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705293701"] {display:table;top:1761px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705293701"] {display:table;top:2186px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705297836"]{z-index:3;top:715px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705297836"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705297836"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705297836"] {display:table;top:715px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705297836"] {display:table;top:1792px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705297836"] {display:table;top:1794px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705297836"] {display:table;top:2219px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705302452"]{z-index:3;top:748px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705302452"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705302452"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705302452"] {display:table;top:748px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705302452"] {display:table;top:1825px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705302452"] {display:table;top:1827px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705302452"] {display:table;top:2252px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705306708"]{z-index:3;top:781px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705306708"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705306708"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705306708"] {display:table;top:781px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705306708"] {display:table;top:1858px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705306708"] {display:table;top:1860px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705306708"] {display:table;top:2285px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705311579"]{z-index:3;top:814px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705311579"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705311579"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705311579"] {display:table;top:814px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705311579"] {display:table;top:1891px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705311579"] {display:table;top:1893px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705311579"] {display:table;top:2318px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705316484"]{z-index:3;top:847px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705316484"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705316484"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705316484"] {display:table;top:847px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705316484"] {display:table;top:1924px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705316484"] {display:table;top:1926px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705316484"] {display:table;top:2351px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705319436"]{z-index:3;top:880px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705319436"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705319436"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705319436"] {display:table;top:880px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705319436"] {display:table;top:1957px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705319436"] {display:table;top:1959px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705319436"] {display:table;top:2384px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696614777"]{color:#000000;text-align:LEFT;z-index:3;top:594px;left:calc(50% - 600px + 119px);width:285px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614777"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696614777"] {display:table;top:594px;left:calc(50% - 480px + 19px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696614777"] {display:table;top:1671px;left:calc(50% - 320px + 91px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696614777"] {display:table;top:1673px;left:calc(50% - 207px + 16px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696614777"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696614777"] {display:table;top:2098px;left:calc(50% - 195px + 16px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750698062426"]{z-index:3;top:594px;left:calc(50% - 600px + 527px);width:14px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750698062426"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750698062426"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698062426"] {display:table;top:594px;left:calc(50% - 480px + 427px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698062426"] {display:table;top:1671px;left:calc(50% - 320px + 536px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698062426"] {display:table;top:1673px;left:calc(50% - 207px + 385px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698062426"] {display:table;top:2098px;left:calc(50% - 195px + 361px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705384264"]{z-index:3;top:1012px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705384264"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705384264"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705384264"] {display:table;top:1012px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705384264"] {display:table;top:2106px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705384264"] {display:table;top:2106px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705384264"] {display:table;top:2531px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705394866"]{z-index:3;top:1045px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705394866"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705394866"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705394866"] {display:table;top:1045px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705394866"] {display:table;top:2139px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705394866"] {display:table;top:2138px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705394866"] {display:table;top:2563px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750705402420"]{z-index:3;top:1078px;left:calc(50% - 600px + 119px);width:361px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750705402420"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750705402420"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750705402420"] {display:table;top:1078px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750705402420"] {display:table;top:2172px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750705402420"] {display:table;top:2170px;left:calc(50% - 207px + 15px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750705402420"] {display:table;top:2595px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615357"]{color:#000000;text-align:LEFT;z-index:3;top:956px;left:calc(50% - 600px + 119px);width:328px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615357"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615357"] {display:table;top:956px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615357"] {display:table;top:2050px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615357"] {display:table;top:2051px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615357"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615357"] {display:table;top:2476px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615372"]{color:#000000;text-align:LEFT;z-index:3;top:988px;left:calc(50% - 600px + 119px);width:70px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615372"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615372"] {display:table;top:988px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615372"] {display:table;top:2082px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615372"] {display:table;top:2083px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615372"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615372"] {display:table;top:2508px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615379"]{color:#000000;text-align:LEFT;z-index:3;top:988px;left:calc(50% - 600px + 352px);width:99px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615379"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615379"] {display:table;top:988px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615379"] {display:table;top:2082px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615379"] {display:table;top:2083px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615379"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615379"] {display:table;top:2508px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615405"]{color:#000000;text-align:LEFT;z-index:3;top:1021px;left:calc(50% - 600px + 119px);width:80px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615405"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615405"] {display:table;top:1021px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615405"] {display:table;top:2115px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615405"] {display:table;top:2115px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615405"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615405"] {display:table;top:2540px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615415"]{color:#000000;text-align:LEFT;z-index:3;top:1021px;left:calc(50% - 600px + 352px);width:117px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615415"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615415"] {display:table;top:1021px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615415"] {display:table;top:2115px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615415"] {display:table;top:2115px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615415"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615415"] {display:table;top:2540px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615423"]{color:#000000;text-align:LEFT;z-index:3;top:1054px;left:calc(50% - 600px + 119px);width:75px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615423"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615423"] {display:table;top:1054px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615423"] {display:table;top:2148px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615423"] {display:table;top:2147px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615423"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615423"] {display:table;top:2572px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615433"]{color:#000000;text-align:LEFT;z-index:3;top:1054px;left:calc(50% - 600px + 352px);width:131px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615433"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615433"] {display:table;top:1054px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615433"] {display:table;top:2148px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615433"] {display:table;top:2147px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615433"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615433"] {display:table;top:2572px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615443"]{color:#000000;text-align:LEFT;z-index:3;top:1087px;left:calc(50% - 600px + 119px);width:186px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615443"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615443"] {display:table;top:1087px;left:calc(50% - 480px + 18px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615443"] {display:table;top:2181px;left:calc(50% - 320px + 90px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615443"] {display:table;top:2179px;left:calc(50% - 207px + 15px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615443"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615443"] {display:table;top:2604px;left:calc(50% - 195px + 15px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750696615453"]{color:#000000;text-align:LEFT;z-index:3;top:1087px;left:calc(50% - 600px + 352px);width:64px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615453"] .tn-atom {vertical-align:middle;color:#000000;font-size:14px;font-family:'GillSans',Arial,sans-serif;font-weight:400;background-position:center center;border-color:transparent ;border-style:solid;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750696615453"] {display:table;top:1087px;left:calc(50% - 480px + 251px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750696615453"] {display:table;top:2181px;left:calc(50% - 320px + 323px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750696615453"] {display:table;top:2179px;left:calc(50% - 207px + 248px);height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750696615453"] .tn-atom{font-size:13px;background-size:cover;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750696615453"] {display:table;top:2604px;left:calc(50% - 195px + 248px);height:auto;}}#rec1126876471 .tn-elem[data-elem-id="1750698110531"]{z-index:3;top:957px;left:calc(50% - 600px + 527px);width:14px;height:auto;}#rec1126876471 .tn-elem[data-elem-id="1750698110531"] .tn-atom {border-radius:0px 0px 0px 0px;background-position:center center;border-color:transparent ;border-style:solid;}#rec1126876471 .tn-elem[data-elem-id="1750698110531"] .tn-atom__img {border-radius:0px 0px 0px 0px;object-position:center center;}@media screen and (max-width:1199px) {#rec1126876471 .tn-elem[data-elem-id="1750698110531"] {display:table;top:957px;left:calc(50% - 480px + 427px);height:auto;}}@media screen and (max-width:959px) {#rec1126876471 .tn-elem[data-elem-id="1750698110531"] {display:table;top:2051px;left:calc(50% - 320px + 536px);height:auto;}}@media screen and (max-width:639px) {#rec1126876471 .tn-elem[data-elem-id="1750698110531"] {display:table;top:2052px;left:calc(50% - 207px + 385px);height:auto;}}@media screen and (max-width:413px) {#rec1126876471 .tn-elem[data-elem-id="1750698110531"] {display:table;top:2477px;left:calc(50% - 195px + 361px);height:auto;}}</style> <div class='t396'> <div class="t396__artboard" data-artboard-recid="1126876471" data-artboard-screens="390,414,640,960,1200" data-artboard-height="1337" data-artboard-valign="center" data-artboard-upscale="window" data-artboard-height-res-390="2700" data-artboard-height-res-640="2277"> <div class="t396__carrier" data-artboard-recid="1126876471"></div> <div class="t396__filter" data-artboard-recid="1126876471"></div> <div class='t396__elem tn-elem tn-elem__11268764711750696614681' data-elem-id='1750696614681' data-elem-type='shape' data-field-top-value="156" data-field-left-value="119" data-field-height-value="398" data-field-width-value="428" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-height-res-390-value="350" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-left-res-414-value="15" data-field-height-res-414-value="375" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-field-top-res-640-value="106" data-field-left-res-640-value="90" data-field-width-res-640-value="460" data-field-widthmode-res-640-value="fixed" data-field-left-res-960-value="20"> <div class='tn-atom t-bgimg' data-original="https://static.tildacdn.one/tild6134-6561-4831-a164-306361393633/Frame_74.png"
aria-label='' role="img"> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614716 t-animate' data-elem-id='1750696614716' data-elem-type='text' data-field-top-value="163" data-field-left-value="587" data-field-height-value="34" data-field-width-value="582" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="491" data-field-top-res-414-value="511" data-field-left-res-414-value="15" data-field-top-res-640-value="534" data-field-left-res-640-value="90" data-field-top-res-960-value="162" data-field-left-res-960-value="477"> <div class='tn-atom'field='tn_text_1750696614716'>Sleep &amp; Relaxation</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614722 t-animate' data-elem-id='1750696614722' data-elem-type='text' data-field-top-value="208" data-field-left-value="587" data-field-height-value="17" data-field-width-value="212" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="534" data-field-top-res-414-value="550" data-field-left-res-414-value="15" data-field-top-res-640-value="578" data-field-left-res-640-value="90" data-field-top-res-960-value="207" data-field-left-res-960-value="477"> <div class='tn-atom'field='tn_text_1750696614722'>Super-Shots </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614726 t-animate' data-elem-id='1750696614726' data-elem-type='text' data-field-top-value="256" data-field-left-value="587" data-field-height-value="50" data-field-width-value="476" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="581" data-field-width-res-390-value="361" data-field-top-res-414-value="597" data-field-left-res-414-value="15" data-field-width-res-414-value="382" data-field-top-res-640-value="625" data-field-left-res-640-value="90" data-field-top-res-960-value="254" data-field-left-res-960-value="477" data-field-width-res-960-value="432"> <div class='tn-atom'field='tn_text_1750696614726'>A calming 60ml herbal shot designed to relax the nervous system, reduce cortisol, and support deeper, more restorative sleep — all without sedatives.</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614730 t-animate' data-elem-id='1750696614730' data-elem-type='text' data-field-top-value="939" data-field-left-value="587" data-field-height-value="50" data-field-width-value="476" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1706" data-field-width-res-390-value="361" data-field-top-res-414-value="1295" data-field-left-res-414-value="15" data-field-width-res-414-value="382" data-field-top-res-640-value="1308" data-field-left-res-640-value="90" data-field-top-res-960-value="942" data-field-left-res-960-value="477" data-field-width-res-960-value="432" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'field='tn_text_1750696614730'>One-time purchases are available on Deliveroo, Talabat, Careem, Noon, and Instashop. One-time purchases are also available on Deliveroo, Talabat, Careem &amp; Noon</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614734 t-animate' data-elem-id='1750696614734' data-elem-type='text' data-field-top-value="1056" data-field-left-value="587" data-field-height-value="24" data-field-width-value="458" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1821" data-field-left-res-390-value="15" data-field-top-res-414-value="1410" data-field-left-res-414-value="15" data-field-top-res-640-value="1425" data-field-left-res-640-value="90" data-field-top-res-960-value="1059" data-field-left-res-960-value="477"> <div class='tn-atom'field='tn_text_1750696614734'>Ingredients</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614738 t-animate' data-elem-id='1750696614738' data-elem-type='text' data-field-top-value="1096" data-field-left-value="589" data-field-height-value="17" data-field-width-value="458" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1859" data-field-left-res-390-value="15" data-field-top-res-414-value="1448" data-field-left-res-414-value="15" data-field-top-res-640-value="1463" data-field-left-res-640-value="90" data-field-top-res-960-value="1097" data-field-left-res-960-value="477"> <div class='tn-atom'field='tn_text_1750696614738'><u>Main Shake (700ml):</u></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614741 t-animate' data-elem-id='1750696614741' data-elem-type='text' data-field-top-value="1182" data-field-left-value="589" data-field-height-value="17" data-field-width-value="458" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1962" data-field-left-res-390-value="15" data-field-top-res-414-value="1537" data-field-left-res-414-value="15" data-field-top-res-640-value="1549" data-field-left-res-640-value="90" data-field-top-res-960-value="1183" data-field-left-res-960-value="477"> <div class='tn-atom'field='tn_text_1750696614741'><u>Add-On Shot (60ml):</u></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614745 t-animate' data-elem-id='1750696614745' data-elem-type='text' data-field-top-value="1117" data-field-left-value="589" data-field-height-value="50" data-field-width-value="476" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1880" data-field-left-res-390-value="15" data-field-width-res-390-value="361" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1469" data-field-left-res-414-value="15" data-field-width-res-414-value="382" data-field-top-res-640-value="1484" data-field-left-res-640-value="90" data-field-top-res-960-value="1118" data-field-left-res-960-value="477" data-field-width-res-960-value="432" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'field='tn_text_1750696614745'>Banana, avocado, green apple, pineapple, lemon juice, spinach, cucumber, parsley, coconut water, oat milk, almond milk, sunflower seeds, almonds, chia seeds, sea moss gel, date syrup</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614749 t-animate' data-elem-id='1750696614749' data-elem-type='text' data-field-top-value="1205" data-field-left-value="589" data-field-height-value="34" data-field-width-value="476" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1983" data-field-left-res-390-value="15" data-field-width-res-390-value="361" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1558" data-field-left-res-414-value="15" data-field-width-res-414-value="382" data-field-top-res-640-value="1570" data-field-left-res-640-value="90" data-field-width-res-640-value="458" data-field-top-res-960-value="1204" data-field-left-res-960-value="477" data-field-width-res-960-value="432" data-field-widthmode-res-960-value="fixed"> <div class='tn-atom'field='tn_text_1750696614749'>Reishi mushroom powder, lemon balm powder, ashwagandha powder, tart cherry juice, holy basil powder, date syrup, filtered water</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614752 t-animate' data-elem-id='1750696614752' data-elem-type='text' data-field-top-value="336" data-field-left-value="587" data-field-height-value="17" data-field-width-value="62" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="660" data-field-left-res-390-value="15" data-field-height-res-390-value="15" data-field-width-res-390-value="57" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="680" data-field-left-res-414-value="15" data-field-height-res-414-value="15" data-field-width-res-414-value="60" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="705" data-field-left-res-640-value="90" data-field-top-res-960-value="339" data-field-left-res-960-value="477" data-field-width-res-960-value="58"> <div class='tn-atom'field='tn_text_1750696614752'>Purpose</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614757 t-animate' data-elem-id='1750696614757' data-elem-type='text' data-field-top-value="397" data-field-left-value="587" data-field-height-value="17" data-field-width-value="47" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="734" data-field-left-res-390-value="15" data-field-height-res-390-value="15" data-field-width-res-390-value="43" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="739" data-field-left-res-414-value="15" data-field-height-res-414-value="15" data-field-width-res-414-value="46" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="766" data-field-left-res-640-value="90" data-field-top-res-960-value="400" data-field-left-res-960-value="477" data-field-width-res-960-value="45"> <div class='tn-atom'field='tn_text_1750696614757'>Flavor</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614763 t-animate' data-elem-id='1750696614763' data-elem-type='text' data-field-top-value="336" data-field-left-value="667" data-field-height-value="50" data-field-width-value="396" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="660" data-field-left-res-390-value="88" data-field-height-res-390-value="48" data-field-width-res-390-value="288" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="680" data-field-left-res-414-value="92" data-field-height-res-414-value="64" data-field-width-res-414-value="305" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="705" data-field-left-res-640-value="167" data-field-width-res-640-value="355" data-field-top-res-960-value="339" data-field-left-res-960-value="548" data-field-width-res-960-value="361"> <div class='tn-atom'field='tn_text_1750696614763'>To promote natural melatonin production, reduce anxiety, and support restful sleep through whole-food, adaptogenic, and nervine ingredients. </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614769 t-animate' data-elem-id='1750696614769' data-elem-type='text' data-field-top-value="397" data-field-left-value="667" data-field-height-value="34" data-field-width-value="396" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="734" data-field-left-res-390-value="88" data-field-height-res-390-value="32" data-field-width-res-390-value="288" data-field-container-res-390-value="grid" data-field-heightunits-res-390-value="px" data-field-textfit-res-390-value="autoheight" data-field-top-res-414-value="739" data-field-left-res-414-value="92" data-field-height-res-414-value="64" data-field-width-res-414-value="273" data-field-container-res-414-value="grid" data-field-heightunits-res-414-value="px" data-field-textfit-res-414-value="autoheight" data-field-top-res-640-value="766" data-field-left-res-640-value="167" data-field-width-res-640-value="355" data-field-top-res-960-value="400" data-field-left-res-960-value="548" data-field-width-res-960-value="361"> <div class='tn-atom'field='tn_text_1750696614769'>Earthy and mildly sweet with soothing herbal undertones and a touch of tartness from cherry juice.</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698733465 t-animate' data-elem-id='1750698733465' data-elem-type='shape' data-field-top-value="464" data-field-left-value="587" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="815" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="820" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="833" data-field-left-res-640-value="90" data-field-top-res-960-value="467" data-field-left-res-960-value="477"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698851388 t-animate' data-elem-id='1750698851388' data-elem-type='shape' data-field-top-value="905" data-field-left-value="587" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1672" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1261" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="1274" data-field-left-res-640-value="90" data-field-top-res-960-value="908" data-field-left-res-960-value="477"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698882807 t-animate' data-elem-id='1750698882807' data-elem-type='shape' data-field-top-value="1022" data-field-left-value="587" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1787" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1376" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="1391" data-field-left-res-640-value="90" data-field-top-res-960-value="1025" data-field-left-res-960-value="477"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698891577 t-animate' data-elem-id='1750698891577' data-elem-type='shape' data-field-top-value="1272" data-field-left-value="587" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="2064" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="1639" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="1637" data-field-left-res-640-value="90" data-field-top-res-960-value="1287" data-field-left-res-960-value="477"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750697936303' data-elem-id='1750697936303' data-elem-type='button' data-field-top-value="101" data-field-left-value="42" data-field-height-value="55" data-field-width-value="200" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-mobile="y" data-field-top-res-640-value="51" data-field-left-res-960-value="-58"> <a class='tn-atom' href="catalog.html#ct">← Back</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833026 t-animate' data-elem-id='1750854833026' data-elem-type='shape' data-field-top-value="626" data-field-left-value="821" data-field-height-value="118" data-field-width-value="224" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1329" data-field-left-res-390-value="15" data-field-height-res-390-value="150" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="982" data-field-left-res-414-value="212" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="995" data-field-left-res-640-value="324" data-field-top-res-960-value="629" data-field-left-res-960-value="711"> <a class='tn-atom' href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833055 t-animate' data-elem-id='1750854833055' data-elem-type='shape' data-field-top-value="754" data-field-left-value="587" data-field-height-value="118" data-field-width-value="224" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1489" data-field-left-res-390-value="15" data-field-height-res-390-value="150" data-field-width-res-390-value="360" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="1110" data-field-left-res-414-value="15" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1123" data-field-left-res-640-value="90" data-field-top-res-960-value="757" data-field-left-res-960-value="477"> <a class='tn-atom' href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833042 t-animate' data-elem-id='1750854833042' data-elem-type='shape' data-field-top-value="626" data-field-left-value="587" data-field-height-value="118" data-field-width-value="224" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1169" data-field-left-res-390-value="15" data-field-height-res-390-value="150" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="982" data-field-left-res-414-value="15" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="995" data-field-left-res-640-value="90" data-field-top-res-960-value="629" data-field-left-res-960-value="477"> <a class='tn-atom' href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833085 t-animate' data-elem-id='1750854833085' data-elem-type='button' data-field-top-value="842" data-field-left-value="743" data-field-height-value="22" data-field-width-value="53" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1607" data-field-left-res-390-value="322" data-field-top-res-414-value="1196" data-field-left-res-414-value="150" data-field-height-res-414-value="17" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1211" data-field-left-res-640-value="246" data-field-top-res-960-value="845" data-field-left-res-960-value="633"> <a class='tn-atom' href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800">Buy→</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833083 t-animate' data-elem-id='1750854833083' data-elem-type='button' data-field-top-value="795" data-field-left-value="604" data-field-height-value="21" data-field-width-value="65" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1504" data-field-left-res-390-value="295" data-field-top-res-414-value="1151" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1164" data-field-left-res-640-value="107" data-field-top-res-960-value="798" data-field-left-res-960-value="494"> <a class='tn-atom' href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800">save 20%</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833081 t-animate' data-elem-id='1750854833081' data-elem-type='button' data-field-top-value="712" data-field-left-value="982" data-field-height-value="22" data-field-width-value="53" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1447" data-field-left-res-390-value="322" data-field-top-res-414-value="1068" data-field-left-res-414-value="347" data-field-height-res-414-value="17" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1081" data-field-left-res-640-value="485" data-field-top-res-960-value="715" data-field-left-res-960-value="872"> <a class='tn-atom' href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355">Buy→</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833004 t-animate' data-elem-id='1750854833004' data-elem-type='shape' data-field-top-value="498" data-field-left-value="821" data-field-height-value="118" data-field-width-value="224" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1009" data-field-left-res-390-value="15" data-field-height-res-390-value="150" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="854" data-field-left-res-414-value="210" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="867" data-field-left-res-640-value="324" data-field-top-res-960-value="501" data-field-left-res-960-value="711"> <a class='tn-atom' href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854832988 t-animate' data-elem-id='1750854832988' data-elem-type='shape' data-field-top-value="498" data-field-left-value="587" data-field-height-value="118" data-field-width-value="224" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="849" data-field-left-res-390-value="15" data-field-height-res-390-value="150" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-heightmode-res-390-value="fixed" data-field-top-res-414-value="854" data-field-left-res-414-value="15" data-field-width-res-414-value="187" data-field-widthmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="867" data-field-left-res-640-value="90" data-field-top-res-960-value="501" data-field-left-res-960-value="477"> <a class='tn-atom' href="#order:Sleep &amp; Relaxation One-Time Purchase =35"> </a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833078 t-animate' data-elem-id='1750854833078' data-elem-type='button' data-field-top-value="665" data-field-left-value="836" data-field-height-value="21" data-field-width-value="65" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-top-res-390-value="1344" data-field-left-res-390-value="295" data-field-top-res-414-value="1021" data-field-left-res-414-value="227" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1034" data-field-left-res-640-value="339" data-field-top-res-960-value="668" data-field-left-res-960-value="726"> <a class='tn-atom' href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355">save 15%</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833075 t-animate' data-elem-id='1750854833075' data-elem-type='button' data-field-top-value="712" data-field-left-value="743" data-field-height-value="22" data-field-width-value="53" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1287" data-field-left-res-390-value="322" data-field-top-res-414-value="1068" data-field-left-res-414-value="152" data-field-height-res-414-value="17" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1081" data-field-left-res-640-value="246" data-field-top-res-960-value="715" data-field-left-res-960-value="633"> <a class='tn-atom' href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835">Buy→</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833073 t-animate' data-elem-id='1750854833073' data-elem-type='button' data-field-top-value="665" data-field-left-value="602" data-field-height-value="21" data-field-width-value="65" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1184" data-field-left-res-390-value="295" data-field-top-res-414-value="1021" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1034" data-field-left-res-640-value="105" data-field-top-res-960-value="668" data-field-left-res-960-value="492"> <a class='tn-atom' href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835">save 10%</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833070 t-animate' data-elem-id='1750854833070' data-elem-type='button' data-field-top-value="583" data-field-left-value="982" data-field-height-value="22" data-field-width-value="53" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-top-res-390-value="1125" data-field-left-res-390-value="322" data-field-top-res-414-value="939" data-field-left-res-414-value="347" data-field-height-res-414-value="17" data-field-width-res-414-value="38" data-field-widthmode-res-414-value="fixed" data-field-heightmode-res-414-value="fixed" data-animate-delay-res-414="0.1" data-field-top-res-640-value="952" data-field-left-res-640-value="485" data-field-top-res-960-value="586" data-field-left-res-960-value="872"> <a class='tn-atom' href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50">Buy→</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833068 t-animate' data-elem-id='1750854833068' data-elem-type='button' data-field-top-value="537" data-field-left-value="835" data-field-height-value="21" data-field-width-value="65" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="1024" data-field-left-res-390-value="295" data-field-top-res-414-value="893" data-field-left-res-414-value="224" data-animate-delay-res-414="0.1" data-field-top-res-640-value="906" data-field-left-res-640-value="338" data-field-top-res-960-value="540" data-field-left-res-960-value="725"> <a class='tn-atom' href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50">save 5%</a> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833065 t-animate' data-elem-id='1750854833065' data-elem-type='text' data-field-top-value="827" data-field-left-value="602" data-field-height-value="17" data-field-width-value="97" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1584" data-field-left-res-390-value="30" data-field-width-res-390-value="124" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1183" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1196" data-field-left-res-640-value="105" data-field-top-res-960-value="830" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800"style="color: inherit">28.00 AED/day</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833061 t-animate' data-elem-id='1750854833061' data-elem-type='text' data-field-top-value="845" data-field-left-value="602" data-field-height-value="12" data-field-width-value="70" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1608" data-field-left-res-390-value="30" data-field-width-res-390-value="89" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1201" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1214" data-field-left-res-640-value="105" data-field-top-res-960-value="848" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800"style="color: inherit">10,800.00 AED</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833058 t-animate' data-elem-id='1750854833058' data-elem-type='text' data-field-top-value="771" data-field-left-value="602" data-field-height-value="17" data-field-width-value="163" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1506" data-field-left-res-390-value="27" data-field-width-res-390-value="186" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1127" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1140" data-field-left-res-640-value="105" data-field-top-res-960-value="774" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:12-Monthly Subscription Sleep &amp; Relaxation Shot =10800"style="color: inherit">12-Month Subscription</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833052 t-animate' data-elem-id='1750854833052' data-elem-type='text' data-field-top-value="699" data-field-left-value="602" data-field-height-value="17" data-field-width-value="154" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1264" data-field-left-res-390-value="30" data-field-width-res-390-value="124" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1055" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1068" data-field-left-res-640-value="105" data-field-top-res-960-value="702" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835"style="color: inherit">31.50 AED/day</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833050 t-animate' data-elem-id='1750854833050' data-elem-type='text' data-field-top-value="717" data-field-left-value="602" data-field-height-value="12" data-field-width-value="63" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1288" data-field-left-res-390-value="30" data-field-width-res-390-value="82" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1073" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1086" data-field-left-res-640-value="105" data-field-top-res-960-value="720" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835"style="color: inherit">2,835.00 AED</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833046 t-animate' data-elem-id='1750854833046' data-elem-type='text' data-field-top-value="643" data-field-left-value="602" data-field-height-value="17" data-field-width-value="155" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1186" data-field-left-res-390-value="27" data-field-width-res-390-value="177" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="999" data-field-left-res-414-value="27" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1012" data-field-left-res-640-value="105" data-field-top-res-960-value="646" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:3-Monthly Subscription Sleep &amp; Relaxation Shot =2835"style="color: inherit">3-Month Subscription</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833038 t-animate' data-elem-id='1750854833038' data-elem-type='text' data-field-top-value="699" data-field-left-value="836" data-field-height-value="17" data-field-width-value="154" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1424" data-field-left-res-390-value="30" data-field-width-res-390-value="124" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1055" data-field-left-res-414-value="227" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1068" data-field-left-res-640-value="339" data-field-top-res-960-value="702" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355"style="color: inherit">29.75 AED/day</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833036 t-animate' data-elem-id='1750854833036' data-elem-type='text' data-field-top-value="717" data-field-left-value="836" data-field-height-value="12" data-field-width-value="71" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1448" data-field-left-res-390-value="30" data-field-width-res-390-value="89" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="1073" data-field-left-res-414-value="227" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1086" data-field-left-res-640-value="339" data-field-top-res-960-value="720" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355"style="color: inherit">5,355.00 AED</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833029 t-animate' data-elem-id='1750854833029' data-elem-type='text' data-field-top-value="643" data-field-left-value="836" data-field-height-value="17" data-field-width-value="155" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1346" data-field-left-res-390-value="30" data-field-width-res-390-value="177" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="999" data-field-left-res-414-value="227" data-animate-delay-res-414="0.1" data-field-top-res-640-value="1012" data-field-left-res-640-value="339" data-field-top-res-960-value="646" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:6-Monthly Subscription Sleep &amp; Relaxation Shot =5355"style="color: inherit">6-Month Subscription</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833019 t-animate' data-elem-id='1750854833019' data-elem-type='text' data-field-top-value="571" data-field-left-value="836" data-field-height-value="17" data-field-width-value="112" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1104" data-field-left-res-390-value="30" data-field-width-res-390-value="124" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="927" data-field-left-res-414-value="224" data-animate-delay-res-414="0.1" data-field-top-res-640-value="940" data-field-left-res-640-value="339" data-field-top-res-960-value="574" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50"style="color: inherit">33.25 AED/day</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833015 t-animate' data-elem-id='1750854833015' data-elem-type='text' data-field-top-value="589" data-field-left-value="836" data-field-height-value="12" data-field-width-value="93" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1128" data-field-left-res-390-value="30" data-field-width-res-390-value="82" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="945" data-field-left-res-414-value="224" data-animate-delay-res-414="0.1" data-field-top-res-640-value="958" data-field-left-res-640-value="339" data-field-top-res-960-value="592" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50"style="color: inherit">997.50 AED</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854833011 t-animate' data-elem-id='1750854833011' data-elem-type='text' data-field-top-value="515" data-field-left-value="836" data-field-height-value="17" data-field-width-value="152" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.2" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="1026" data-field-left-res-390-value="30" data-field-width-res-390-value="174" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="871" data-field-left-res-414-value="224" data-animate-delay-res-414="0.1" data-field-top-res-640-value="884" data-field-left-res-640-value="339" data-field-top-res-960-value="518" data-field-left-res-960-value="726"> <div class='tn-atom'><a href="#order:Monthly Subscription Sleep &amp; Relaxation Shot =997.50"style="color: inherit">Monthly Subscription</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854832999 t-animate' data-elem-id='1750854832999' data-elem-type='text' data-field-top-value="585" data-field-left-value="751" data-field-height-value="18" data-field-width-value="38" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="967" data-field-left-res-390-value="322" data-field-top-res-414-value="941" data-field-left-res-414-value="153" data-animate-delay-res-414="0.1" data-field-top-res-640-value="954" data-field-left-res-640-value="254" data-field-top-res-960-value="588" data-field-left-res-960-value="641"> <div class='tn-atom'><a href="#order:Sleep &amp; Relaxation One-Time Purchase =35"style="color: inherit">Buy→</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854832996 t-animate' data-elem-id='1750854832996' data-elem-type='text' data-field-top-value="584" data-field-left-value="602" data-field-height-value="17" data-field-width-value="97" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="957" data-field-left-res-390-value="30" data-field-width-res-390-value="124" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="940" data-field-left-res-414-value="28" data-animate-delay-res-414="0.1" data-field-top-res-640-value="953" data-field-left-res-640-value="105" data-field-top-res-960-value="587" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:Sleep &amp; Relaxation One-Time Purchase =35"style="color: inherit">35.00 AED/day</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750854832992 t-animate' data-elem-id='1750854832992' data-elem-type='text' data-field-top-value="515" data-field-left-value="602" data-field-height-value="17" data-field-width-value="142" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="866" data-field-left-res-390-value="30" data-field-width-res-390-value="162" data-field-heightunits-res-390-value="px" data-field-widthunits-res-390-value="px" data-field-textfit-res-390-value="autowidth" data-field-top-res-414-value="871" data-field-left-res-414-value="26" data-animate-delay-res-414="0.1" data-field-top-res-640-value="884" data-field-left-res-640-value="105" data-field-top-res-960-value="518" data-field-left-res-960-value="492"> <div class='tn-atom'><a href="#order:Sleep &amp; Relaxation One-Time Purchase =35"style="color: inherit">One-Time Purchase</a></div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782167876' data-elem-id='1750782167876' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782178787' data-elem-id='1750782178787' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782198609' data-elem-id='1750782198609' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782241236' data-elem-id='1750782241236' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782269056' data-elem-id='1750782269056' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750782277341' data-elem-id='1750782277341' data-elem-type='shape' data-field-top-value="1037" data-field-left-value="2000" data-field-height-value="1" data-field-width-value="460" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698212226 t-animate' data-elem-id='1750698212226' data-elem-type='shape' data-field-top-value="936" data-field-left-value="118" data-field-height-value="1" data-field-width-value="428" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-widthmode-value="fixed" data-field-heightmode-value="fixed" data-field-top-res-390-value="2442" data-field-width-res-390-value="360" data-field-widthmode-res-390-value="fixed" data-field-top-res-414-value="2017" data-field-left-res-414-value="15" data-field-width-res-414-value="384" data-field-widthmode-res-414-value="fixed" data-field-top-res-640-value="2016" data-field-left-res-640-value="90" data-field-width-res-640-value="460" data-field-widthmode-res-640-value="fixed" data-field-top-res-960-value="936" data-field-left-res-960-value="18"> <div class='tn-atom'> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614997 t-animate' data-elem-id='1750696614997' data-elem-type='text' data-field-top-value="625" data-field-left-value="119" data-field-height-value="17" data-field-width-value="91" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2129" data-field-left-res-390-value="15" data-field-top-res-414-value="1704" data-field-left-res-414-value="15" data-field-top-res-640-value="1702" data-field-left-res-640-value="90" data-field-top-res-960-value="625" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696614997'>Energy</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615003 t-animate' data-elem-id='1750696615003' data-elem-type='text' data-field-top-value="625" data-field-left-value="352" data-field-height-value="17" data-field-width-value="119" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2129" data-field-left-res-390-value="248" data-field-top-res-414-value="1704" data-field-left-res-414-value="248" data-field-top-res-640-value="1702" data-field-left-res-640-value="323" data-field-top-res-960-value="625" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615003'>~27 kcal</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615009 t-animate' data-elem-id='1750696615009' data-elem-type='text' data-field-top-value="691" data-field-left-value="119" data-field-height-value="17" data-field-width-value="185" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2195" data-field-left-res-390-value="15" data-field-top-res-414-value="1770" data-field-left-res-414-value="15" data-field-top-res-640-value="1768" data-field-left-res-640-value="90" data-field-top-res-960-value="691" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615009'>Saturated Fat</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615016 t-animate' data-elem-id='1750696615016' data-elem-type='text' data-field-top-value="691" data-field-left-value="352" data-field-height-value="17" data-field-width-value="130" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2195" data-field-left-res-390-value="248" data-field-top-res-414-value="1770" data-field-left-res-414-value="248" data-field-top-res-640-value="1768" data-field-left-res-640-value="323" data-field-top-res-960-value="691" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615016'>0 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615022 t-animate' data-elem-id='1750696615022' data-elem-type='text' data-field-top-value="658" data-field-left-value="119" data-field-height-value="17" data-field-width-value="173" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2162" data-field-left-res-390-value="15" data-field-top-res-414-value="1737" data-field-left-res-414-value="15" data-field-top-res-640-value="1735" data-field-left-res-640-value="90" data-field-top-res-960-value="658" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615022'>Total Fat</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615028 t-animate' data-elem-id='1750696615028' data-elem-type='text' data-field-top-value="658" data-field-left-value="352" data-field-height-value="17" data-field-width-value="130" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2162" data-field-left-res-390-value="248" data-field-top-res-414-value="1737" data-field-left-res-414-value="248" data-field-top-res-640-value="1735" data-field-left-res-640-value="323" data-field-top-res-960-value="658" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615028'>0.1 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615034 t-animate' data-elem-id='1750696615034' data-elem-type='text' data-field-top-value="724" data-field-left-value="119" data-field-height-value="17" data-field-width-value="147" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2228" data-field-left-res-390-value="15" data-field-top-res-414-value="1803" data-field-left-res-414-value="15" data-field-top-res-640-value="1801" data-field-left-res-640-value="90" data-field-top-res-960-value="724" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615034'>Carbohydrates</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615041 t-animate' data-elem-id='1750696615041' data-elem-type='text' data-field-top-value="724" data-field-left-value="352" data-field-height-value="17" data-field-width-value="119" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2228" data-field-left-res-390-value="248" data-field-top-res-414-value="1803" data-field-left-res-414-value="248" data-field-top-res-640-value="1801" data-field-left-res-640-value="323" data-field-top-res-960-value="724" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615041'> 6.4 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615047 t-animate' data-elem-id='1750696615047' data-elem-type='text' data-field-top-value="757" data-field-left-value="119" data-field-height-value="17" data-field-width-value="177" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2261" data-field-left-res-390-value="15" data-field-top-res-414-value="1836" data-field-left-res-414-value="15" data-field-top-res-640-value="1834" data-field-left-res-640-value="90" data-field-top-res-960-value="757" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615047'>Sugars</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615054 t-animate' data-elem-id='1750696615054' data-elem-type='text' data-field-top-value="757" data-field-left-value="352" data-field-height-value="17" data-field-width-value="142" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2261" data-field-left-res-390-value="248" data-field-top-res-414-value="1836" data-field-left-res-414-value="248" data-field-top-res-640-value="1834" data-field-left-res-640-value="323" data-field-top-res-960-value="757" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615054'>4.5 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615060 t-animate' data-elem-id='1750696615060' data-elem-type='text' data-field-top-value="790" data-field-left-value="119" data-field-height-value="17" data-field-width-value="114" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2294" data-field-left-res-390-value="15" data-field-top-res-414-value="1869" data-field-left-res-414-value="15" data-field-top-res-640-value="1867" data-field-left-res-640-value="90" data-field-top-res-960-value="790" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615060'>Dietary Fibe</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615066 t-animate' data-elem-id='1750696615066' data-elem-type='text' data-field-top-value="790" data-field-left-value="352" data-field-height-value="17" data-field-width-value="100" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2294" data-field-left-res-390-value="248" data-field-top-res-414-value="1869" data-field-left-res-414-value="248" data-field-top-res-640-value="1867" data-field-left-res-640-value="323" data-field-top-res-960-value="790" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615066'>0.8 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615072 t-animate' data-elem-id='1750696615072' data-elem-type='text' data-field-top-value="823" data-field-left-value="119" data-field-height-value="17" data-field-width-value="194" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2327" data-field-left-res-390-value="15" data-field-top-res-414-value="1902" data-field-left-res-414-value="15" data-field-top-res-640-value="1900" data-field-left-res-640-value="90" data-field-top-res-960-value="823" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615072'>Protein</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615080 t-animate' data-elem-id='1750696615080' data-elem-type='text' data-field-top-value="823" data-field-left-value="352" data-field-height-value="17" data-field-width-value="111" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2327" data-field-left-res-390-value="248" data-field-top-res-414-value="1902" data-field-left-res-414-value="248" data-field-top-res-640-value="1900" data-field-left-res-640-value="323" data-field-top-res-960-value="823" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615080'> 0.4 g</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615089 t-animate' data-elem-id='1750696615089' data-elem-type='text' data-field-top-value="856" data-field-left-value="119" data-field-height-value="17" data-field-width-value="169" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2360" data-field-left-res-390-value="15" data-field-top-res-414-value="1935" data-field-left-res-414-value="15" data-field-top-res-640-value="1933" data-field-left-res-640-value="90" data-field-top-res-960-value="856" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615089'>Sodium</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615097 t-animate' data-elem-id='1750696615097' data-elem-type='text' data-field-top-value="856" data-field-left-value="352" data-field-height-value="17" data-field-width-value="150" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2360" data-field-left-res-390-value="248" data-field-top-res-414-value="1935" data-field-left-res-414-value="248" data-field-top-res-640-value="1933" data-field-left-res-640-value="323" data-field-top-res-960-value="856" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615097'>~12 mg</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615109 t-animate' data-elem-id='1750696615109' data-elem-type='text' data-field-top-value="889" data-field-left-value="119" data-field-height-value="17" data-field-width-value="88" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2393" data-field-left-res-390-value="15" data-field-top-res-414-value="1968" data-field-left-res-414-value="15" data-field-top-res-640-value="1966" data-field-left-res-640-value="90" data-field-top-res-960-value="889" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615109'>Potassium</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615116 t-animate' data-elem-id='1750696615116' data-elem-type='text' data-field-top-value="889" data-field-left-value="352" data-field-height-value="17" data-field-width-value="150" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2393" data-field-left-res-390-value="248" data-field-top-res-414-value="1968" data-field-left-res-414-value="248" data-field-top-res-640-value="1966" data-field-left-res-640-value="323" data-field-top-res-960-value="889" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615116'>~65 mg</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705255899 t-animate' data-elem-id='1750705255899' data-elem-type='image' data-field-top-value="649" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2153" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1728" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1726" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="649" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705255899'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705293701 t-animate' data-elem-id='1750705293701' data-elem-type='image' data-field-top-value="682" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2186" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1761" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1759" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="682" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705293701'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705297836 t-animate' data-elem-id='1750705297836' data-elem-type='image' data-field-top-value="715" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2219" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1794" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1792" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="715" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705297836'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705302452 t-animate' data-elem-id='1750705302452' data-elem-type='image' data-field-top-value="748" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2252" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1827" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1825" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="748" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705302452'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705306708 t-animate' data-elem-id='1750705306708' data-elem-type='image' data-field-top-value="781" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2285" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1860" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1858" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="781" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705306708'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705311579 t-animate' data-elem-id='1750705311579' data-elem-type='image' data-field-top-value="814" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2318" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1893" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1891" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="814" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705311579'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705316484 t-animate' data-elem-id='1750705316484' data-elem-type='image' data-field-top-value="847" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2351" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1926" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1924" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="847" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705316484'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705319436 t-animate' data-elem-id='1750705319436' data-elem-type='image' data-field-top-value="880" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2384" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="1959" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="1957" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="880" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705319436'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696614777 t-animate' data-elem-id='1750696614777' data-elem-type='text' data-field-top-value="594" data-field-left-value="119" data-field-height-value="17" data-field-width-value="285" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2098" data-field-left-res-390-value="16" data-field-top-res-414-value="1673" data-field-left-res-414-value="16" data-field-top-res-640-value="1671" data-field-left-res-640-value="91" data-field-top-res-960-value="594" data-field-left-res-960-value="19"> <div class='tn-atom'field='tn_text_1750696614777'>Nutrition Facts (per 60ml shot): </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698062426 t-animate' data-elem-id='1750698062426' data-elem-type='image' data-field-top-value="594" data-field-left-value="527" data-field-height-value="15" data-field-width-value="14" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="10" data-field-fileheight-value="11" data-field-heightmode-value="hug" data-field-top-res-390-value="2098" data-field-left-res-390-value="361" data-field-height-res-390-value="15" data-field-top-res-414-value="1673" data-field-left-res-414-value="385" data-field-height-res-414-value="15" data-field-top-res-640-value="1671" data-field-left-res-640-value="536" data-field-height-res-640-value="15" data-field-top-res-960-value="594" data-field-left-res-960-value="427" data-field-height-res-960-value="15"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3566-3736-4161-b131-363731386133/photo.svg'
src='https://static.tildacdn.one/tild3566-3736-4161-b131-363731386133/photo.svg'
alt='' imgfield='tn_img_1750698062426'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705384264 t-animate' data-elem-id='1750705384264' data-elem-type='image' data-field-top-value="1012" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2531" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="2106" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="2106" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="1012" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705384264'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705394866 t-animate' data-elem-id='1750705394866' data-elem-type='image' data-field-top-value="1045" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2563" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="2138" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="2139" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="1045" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705394866'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750705402420 t-animate' data-elem-id='1750705402420' data-elem-type='image' data-field-top-value="1078" data-field-left-value="119" data-field-height-value="2" data-field-width-value="361" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="361" data-field-fileheight-value="2" data-field-heightmode-value="hug" data-field-top-res-390-value="2595" data-field-left-res-390-value="15" data-field-height-res-390-value="2" data-field-top-res-414-value="2170" data-field-left-res-414-value="15" data-field-height-res-414-value="2" data-field-top-res-640-value="2172" data-field-left-res-640-value="90" data-field-height-res-640-value="2" data-field-top-res-960-value="1078" data-field-left-res-960-value="18" data-field-height-res-960-value="2"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
src='https://static.tildacdn.one/tild3332-6163-4166-b163-373665336637/Line_16.svg'
alt='' imgfield='tn_img_1750705402420'
/> </div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615357 t-animate' data-elem-id='1750696615357' data-elem-type='text' data-field-top-value="956" data-field-left-value="119" data-field-height-value="17" data-field-width-value="328" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2476" data-field-left-res-390-value="15" data-field-top-res-414-value="2051" data-field-left-res-414-value="15" data-field-top-res-640-value="2050" data-field-left-res-640-value="90" data-field-top-res-960-value="956" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615357'>Vitamins &amp; Minerals</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615372 t-animate' data-elem-id='1750696615372' data-elem-type='text' data-field-top-value="988" data-field-left-value="119" data-field-height-value="17" data-field-width-value="70" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2508" data-field-left-res-390-value="15" data-field-top-res-414-value="2083" data-field-left-res-414-value="15" data-field-top-res-640-value="2082" data-field-left-res-640-value="90" data-field-top-res-960-value="988" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615372'>Vitamin C</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615379 t-animate' data-elem-id='1750696615379' data-elem-type='text' data-field-top-value="988" data-field-left-value="352" data-field-height-value="17" data-field-width-value="99" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2508" data-field-left-res-390-value="248" data-field-top-res-414-value="2083" data-field-left-res-414-value="248" data-field-top-res-640-value="2082" data-field-left-res-640-value="323" data-field-top-res-960-value="988" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615379'>~12 mg 13%</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615405 t-animate' data-elem-id='1750696615405' data-elem-type='text' data-field-top-value="1021" data-field-left-value="119" data-field-height-value="17" data-field-width-value="80" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2540" data-field-left-res-390-value="15" data-field-top-res-414-value="2115" data-field-left-res-414-value="15" data-field-top-res-640-value="2115" data-field-left-res-640-value="90" data-field-top-res-960-value="1021" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615405'>Iron</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615415 t-animate' data-elem-id='1750696615415' data-elem-type='text' data-field-top-value="1021" data-field-left-value="352" data-field-height-value="17" data-field-width-value="117" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2540" data-field-left-res-390-value="248" data-field-top-res-414-value="2115" data-field-left-res-414-value="248" data-field-top-res-640-value="2115" data-field-left-res-640-value="323" data-field-top-res-960-value="1021" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615415'>~0.3 mg 2%</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615423 t-animate' data-elem-id='1750696615423' data-elem-type='text' data-field-top-value="1054" data-field-left-value="119" data-field-height-value="17" data-field-width-value="75" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2572" data-field-left-res-390-value="15" data-field-top-res-414-value="2147" data-field-left-res-414-value="15" data-field-top-res-640-value="2148" data-field-left-res-640-value="90" data-field-top-res-960-value="1054" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615423'>Magnesium</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615433 t-animate' data-elem-id='1750696615433' data-elem-type='text' data-field-top-value="1054" data-field-left-value="352" data-field-height-value="17" data-field-width-value="131" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2572" data-field-left-res-390-value="248" data-field-top-res-414-value="2147" data-field-left-res-414-value="248" data-field-top-res-640-value="2148" data-field-left-res-640-value="323" data-field-top-res-960-value="1054" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615433'> ~8 mg 2%</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615443 t-animate' data-elem-id='1750696615443' data-elem-type='text' data-field-top-value="1087" data-field-left-value="119" data-field-height-value="34" data-field-width-value="186" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2604" data-field-left-res-390-value="15" data-field-top-res-414-value="2179" data-field-left-res-414-value="15" data-field-top-res-640-value="2181" data-field-left-res-640-value="90" data-field-top-res-960-value="1087" data-field-left-res-960-value="18"> <div class='tn-atom'field='tn_text_1750696615443'>Melatonin (from cherry) Natural source</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750696615453 t-animate' data-elem-id='1750696615453' data-elem-type='text' data-field-top-value="1087" data-field-left-value="352" data-field-height-value="17" data-field-width-value="64" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-textfit-value="autoheight" data-field-top-res-390-value="2604" data-field-left-res-390-value="248" data-field-top-res-414-value="2179" data-field-left-res-414-value="248" data-field-top-res-640-value="2181" data-field-left-res-640-value="323" data-field-top-res-960-value="1087" data-field-left-res-960-value="251"> <div class='tn-atom'field='tn_text_1750696615453'> —</div> </div> <div class='t396__elem tn-elem tn-elem__11268764711750698110531 t-animate' data-elem-id='1750698110531' data-elem-type='image' data-field-top-value="957" data-field-left-value="527" data-field-height-value="15" data-field-width-value="14" data-field-axisy-value="top" data-field-axisx-value="left" data-field-container-value="grid" data-field-topunits-value="px" data-field-leftunits-value="px" data-field-heightunits-value="px" data-field-widthunits-value="px" data-animate-style="fadeinup" data-animate-duration="2" data-animate-delay="0.1" data-animate-distance="20" data-animate-mobile="y" data-field-filewidth-value="10" data-field-fileheight-value="11" data-field-heightmode-value="hug" data-field-top-res-390-value="2477" data-field-left-res-390-value="361" data-field-height-res-390-value="15" data-field-top-res-414-value="2052" data-field-left-res-414-value="385" data-field-height-res-414-value="15" data-field-top-res-640-value="2051" data-field-left-res-640-value="536" data-field-height-res-640-value="15" data-field-top-res-960-value="957" data-field-left-res-960-value="427" data-field-height-res-960-value="15"> <div class='tn-atom'> <img class='tn-atom__img t-img' data-original='https://static.tildacdn.one/tild3566-3736-4161-b131-363731386133/photo.svg'
src='https://static.tildacdn.one/tild3566-3736-4161-b131-363731386133/photo.svg'
alt='' imgfield='tn_img_1750698110531'
/> </div> </div> </div> </div> <script>t_onFuncLoad('t396_initialScale',function() {t396_initialScale('1126876471');});t_onReady(function() {t_onFuncLoad('t396_init',function() {t396_init('1126876471');});});</script> <!-- /T396 --> </div> </div> <!--/allrecords--> <!-- Stat --> <script type="text/javascript">if(!window.mainTracker) {window.mainTracker='tilda';}
window.tildastatcookie='no';setTimeout(function(){(function(d,w,k,o,g) {var n=d.getElementsByTagName(o)[0],s=d.createElement(o),f=function(){n.parentNode.insertBefore(s,n);};s.type="text/javascript";s.async=true;s.key=k;s.id="tildastatscript";s.src=g;if(w.opera=="[object Opera]") {d.addEventListener("DOMContentLoaded",f,false);} else {f();}})(document,window,'dc45e60d2b39e40a7f6983dc50529e6e','script','../static.tildacdn.one/js/tilda-stat-1.0.min.js');},2000);</script> </body> 
<!-- Mirrored from multivitshakes.com/sleeprelaxation by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 03 Aug 2025 14:10:13 GMT -->
</html>