var t_quiz__hashMap=new Map,t_quiz__getDebouncedFunc=function t(e,n,i){if(void 0===i&&(i=200),t_quiz__hashMap.has(n))return t_quiz__hashMap.get(n);var o=t_quiz__debounce(e,i);return t_quiz__hashMap.set(n,o),o};function t_quiz__init(t,e){var n=document.querySelector("#rec"+t);if(!n)throw new Error("rec not found. id: "+t);var i=n.querySelector(".t-quiz");if(!i)throw new Error("quiz wrapper not found. recid: "+t);var o=n.querySelector(".t-form");if(!o)throw new Error("form wrapper not found. recid: "+t);var r=n.querySelector(".t-quiz__main"),u=n.querySelector(".t-quiz__quiz-wrapper"),a=n.querySelectorAll(".t-input-group"),_=n.querySelectorAll(".t-quiz__btn_next"),s=n.querySelectorAll(".t-quiz__btn_submit"),c=n.querySelectorAll(".t-quiz__btn_prev"),l=n.querySelectorAll(".t-quiz__result-btn:not(.t-result-btn_link):not(.t-result-btn_restart):not(.t-result-btn_prevstep)"),d=n.querySelectorAll(".t-result-btn_restart"),p=n.querySelectorAll(".t-quiz__btn_start"),f=n.querySelector(".t-quiz__panel"),q=n.querySelector(".t-quiz__footer"),v=n.querySelector(".t-quiz__btn-wrapper_mobile"),z=n.querySelector(".t-quiz__progressbar"),m=n.querySelector(".js-footer-text-container"),y=n.querySelector(".t-quiz__consultant"),h=u.getAttribute("data-consultant-msg"),g=n.querySelector(".t-quiz__main__description-container"),w=n.querySelector(".t-popup"),b=n.querySelector(".t-popup__container"),S=n.querySelector(".t-form__errorbox-middle");S&&t_quiz__hide(S),o.addEventListener("tildaform:aftererror",(function(){o.classList.contains("js-send-form-error")&&t_quiz__show(S)})),o.classList.remove("js-form-proccess"),t_quiz__workWithAnswerCode(n);var L=u.classList.contains("t-quiz__quiz-published"),E;if(!u.classList.contains("t-quiz__quiz-wrapper_fixed-height")){var A="500px",C;n.querySelectorAll(".t-quiz__cover").forEach((function(t){t.style.height=A}))}var T={desktop:u.getAttribute("data-clamp-consultant-desktop"),mobile:u.getAttribute("data-clamp-consultant-mobile")},x=Boolean(T.desktop||T.mobile),k=Array.from(n.querySelectorAll(".t-quiz__screen-wrapper:not(.t-form__hidden)")),F=n.querySelector(".t-quiz__result_ordinary"),P=[].concat(k,[F]).map((function(t){return{type:t_quiz__getStepType(t),element:t}})),I=Boolean(u.getAttribute("data-analytics-enabled")),M=Boolean(u.getAttribute("data-auto-step-change")),H=!!o.querySelector(".t-conditional"),j=t_quiz__step_manager({recId:t,steps:P,activeStepClass:"t-step-form__step_active",hiddenStepClass:"t-quiz-hidden",enableAutoStepChange:M,enableConditionals:H,onProgressChange:O,onConditionalStateChange:B,animation:J(),onInit:function t(){e&&e(),t_quiz__adjustStepToHeight({rec:n})},checkIfCanChangeStep:V});function V(t,e,n){return void 0===n&&(n={}),!(e&&e.step&&e.step.element)||("prev"===t?(ot(e.step.element),!0):(!e.isLast||!n.isAutoTransition)&&(!(!e.isLast||!n.isOnSuccess)||(!e.isLast||"result"!==e.type)&&(!ut()&&(ot(e.step.element),!0))));var i}function O(t){t_quiz__setProgress(n,t.current,t.total),t.current&&t_quiz__showCounter(n,t.current,t.total),requestAnimationFrame((function(){t_quiz__adjustStepToHeight({rec:n})}))}function B(t){var e=t.target,n=t.detail&&!0===t.detail.hidden,i,o=j.getCurrentStepInfo().step.element.contains(e);n&&o&&t_quiz__pauseVideos(e),$(),Y(),U()?c.forEach((function(t){return t.disabled=!0})):c.forEach((function(t){return t.disabled=!1}))}function R(){n.setAttribute("data-animationappear","off"),n.style.opacity=1,c.forEach((function(t){t.addEventListener("click",(function(){W()}))})),_.forEach((function(t){t.addEventListener("click",(function(){N()}))})),l.forEach((function(t){t.addEventListener("click",(function(){N()}))})),Array.prototype.forEach.call(a,(function(t){t.addEventListener("keypress",(function(t){var e="Enter"===t.code||13===t.keyCode,n=t.target instanceof HTMLTextAreaElement,i=o.classList.contains("js-form-proccess");!e||i||n||N()}))})),s.forEach((function(t){t.addEventListener("click",(function(){Q()}))})),p.forEach((function(t){t.addEventListener("click",(function(){N()}))})),d.forEach((function(t){t.addEventListener("click",(function(){D()}))})),i.classList.contains("popup")&&t_quiz__openToHook(n),x&&t_quiz__updateConsultantClampOnResize(n,t,T),t_quiz__adjustHeightOnResize(n,t),t_quiz__applyTranslation(n)}function W(){j.go("prev")}function N(){j.go("next")}function D(){t_onFuncLoad("t_forms__clearForm",(function(){window.t_forms__clearForm(o).then((function(){j.restart(),lt(),window.t_forms__updateAllVariables(o)})).catch((function(t){console.error("Failed to restart Quiz: ",t)}))}))}function Q(){ut(),rt()}function K(){return j.getCurrentStepInfo().isLast}function U(){return j.getCurrentStepInfo().isFirst}function Y(){if(K())return _.forEach((function(t){t_quiz__hide(t)})),void s.forEach((function(t){t_quiz__show(t),t.disabled=!1}));_.forEach((function(t){t_quiz__show(t)})),s.forEach((function(t){t_quiz__hide(t),t.disabled=!0}))}function J(){return{type:{cover:"fade",result:"fade",contacts:"fade-with-move",step:"fade-with-move"},callbacks:{onNewStepAnimationStart:function e(i,o){if(o){var r=K();$(),t_quiz__runLazyLoad(),i&&t_quiz__pauseVideos(i.element),t_quiz__initStepVideos(o.element),t_quiz__updateRangeSlider(o.element),x&&t_quiz__addConsultantClamp({rec:n,recId:t,lines:T}),it(o),_t(),"step"!==o.type&&"contacts"!==o.type||(t_quiz__show(z),t_quiz__show(q),t_quiz__show(v),nt()),"cover"!==o.type&&"result"!==o.type||(t_quiz__hide(q),t_quiz__hide(v),et(),t_quiz__hide(z)),"contacts"===o.type&&tt(o.element),Z(o),Y(),"cover"===o.type&&r?G():"result"===o.type&&r?c.forEach((function(t){return t.disabled=!0})):X(),i&&t_quiz__scrollPageToTopOf(u),dt()}},onAnimationStart:function t(){G()}}}}function Z(t){if(m){var e=t.element.getAttribute("data-step-footer-text");if(e){if(m){var n=m.querySelector(".js-footer-text");n&&(t_quiz__show(m),n.textContent=e)}}else t_quiz__hide(m)}}function G(){p.forEach((function(t){t.disabled=!0})),c.forEach((function(t){t.disabled=!0})),_.forEach((function(t){t.disabled=!0})),s.forEach((function(t){t.disabled=!0})),l.forEach((function(t){t.disabled=!0}))}function X(){p.forEach((function(t){t.disabled=!1})),_.forEach((function(t){t.disabled=!1})),U()||c.forEach((function(t){t.disabled=!1})),s.forEach((function(t){t.disabled=!1})),l.forEach((function(t){t.disabled=!1}))}function $(){o.classList.toggle("js-form-proccess",K())}function tt(t){if(t&&K()){var e=t.getAttribute("data-submit-title");e&&s.forEach((function(t){var n=t.querySelector("span");n.innerHTML=e,t_onFuncLoad("t_forms__parseVariables",(function(){t_forms__parseVariables(n)}))}))}}function et(){t_quiz__hide(f),t_quiz__hide(g),u.classList.add("panel-hidden")}function nt(){t_quiz__show(f),t_quiz__show(g),u.classList.remove("panel-hidden")}function it(t){if(y){var e=y.querySelector(".t-quiz__consultant__container"),n=y.querySelector(".js-consultant-msg");if(e){var i=t.element.getAttribute("data-consultant-msg");if(i||h){var o=n.querySelector(".t-text-clamp__inner");o&&(n=o),n.innerHTML=i||h,t_onFuncLoad("t_forms__parseVariables",(function(){t_forms__parseVariables(n).finally((function(){t_quiz__show(e)}))}))}else t_quiz__hide(e)}}}function ot(t){if(t){var e;t.querySelectorAll(".t-input-group").forEach((function(t){t.classList.remove("js-error-control-box")})),t_quiz__hide(S);var n=t.querySelector(".t-input-error");n&&(n.innerHTML="")}}function rt(){if(!L)return!1;if("object"!=typeof window.tildaForm)return!1;t_quiz__hide(S);var t=window.tildaForm.validate(o),e=t&&t.length>0;return e&&(t_quiz__show(S),requestAnimationFrame((function(){t_quiz__adjustStepToHeight({rec:n})}))),e}function ut(){var t;if(L){var e,i=null==(t=j.getCurrentStepInfo().step)?void 0:t.element;if(!i)return!1;if("object"!=typeof window.tildaForm)return!1;ot(i);var o=window.tildaForm.validate(i);if(!o||0===o.length)return!1;var r=at(o),u=!!r.length;return u&&(window.tildaForm.showErrors(i,r,{inputBoxSelector:".t-input-group"}),requestAnimationFrame((function(){t_quiz__adjustStepToHeight({rec:n})}))),u}}function at(t){return t.filter((function(t){return t.type.some((function(t){return"emptyfill"!==t}))}))}function _t(){var e=n.querySelector(".t-step-form__step_active .t-quiz__cover__side-cover .t-quiz__cover__img, .t-step-form__step_active .t-quiz__result-img-wrapper");if(e){var i=window.getComputedStyle(e).getPropertyValue("background-image").slice(5,-2),o=document.createElement("img");o.src=i,o.onload=function(){var n,i,r=(o.width/o.height).toFixed(1),u=Math.max(r,1);function a(){t_quiz__getDebouncedFunc(st,t+"_spec-ratio")(e,u)}st(e,u),window.addEventListener("resize",a),setTimeout((function(){window.removeEventListener("resize",a)}),1e4)}}}function st(t,e){var n=e;t_quiz__isMobile()?n=e:t.classList.contains("t-quiz__cover__img")?n="unset":t.classList.contains("t-quiz__result-img-wrapper")&&(n="16/9"),t.style.aspectRatio=n}function ct(){try{var t=t_quiz__analytics({rec:n});if(!t)return;if("IntersectionObserver"in window){var e=new IntersectionObserver((function(e,n){e.forEach((function(e){if(e.isIntersecting){var i=e.target,o=j.getCurrentStepInfo();if(!o.step||i===o.step.element){var r=o.step.element.getAttribute("data-step-index")||o.index,u=Number(r)+1;t.viewStep(u),n.unobserve(i)}}}))}));P.forEach((function(t){e.observe(t.element)}))}}catch(i){console.warn(i)}}function lt(){var t=t_quiz__analytics({rec:n});t&&(t.restart(),ct())}function dt(){var t;b&&b.classList.contains("t-popup__container-static")?w.scrollTo(0,0):r.scrollTo(0,0)}I&&ct(),R(),j.init(),u.StepsManager=j,t_quiz__displayStickyBtnWrapper(n),i.classList.contains(".popup")||t_onFuncLoad("t_throttle",(function(){window.addEventListener("scroll",t_throttle((function(){t_quiz__displayStickyBtnWrapper(n)})))}))}function t_quiz__hide(t){var e="t-quiz-hidden";t&&t.classList.add(e)}function t_quiz__show(t){var e="t-quiz-hidden";t&&t.classList.remove(e)}function t_quiz__getStepType(t){return t.classList.contains("t-quiz__cover")?"cover":t.classList.contains("t-quiz__contact-form")?"contacts":t.classList.contains("t-quiz__result")?"result":"step"}function t_quiz__displayStickyBtnWrapper(t){if(t_quiz__isMobile()){var e=t.querySelector(".t-quiz__quiz");if(e){var n=t.querySelector(".t-quiz__btn-wrapper_mobile");if(n){var i=null!==e.closest(".popup"),o=e.getBoundingClientRect(),r=500,u=-30,a=window.innerHeight-r;(i||!i&&o.top<a&&o.bottom>=u)&&(n.classList.add("t-quiz__btn-wrapper_visible"),n.classList.remove("t-quiz__btn-wrapper_hidden")),!i&&o.top>=a&&(n.classList.remove("t-quiz__btn-wrapper_visible"),n.classList.add("t-quiz__btn-wrapper_hidden"))}}}}function t_quiz__addConsultantClamp(t){var e=t.recId,n=t.rec,i=t.lines,o;if(n.querySelector(".t-quiz__consultant")){var r=".js-consultant-msg",u=".t-step-form__step_active .js-consultant-msg,.t-quiz__panel .js-consultant-msg",a=t_quiz__isMobile(),_=!a,s=i.mobile&&!i.desktop,c=i.desktop&&!i.mobile,l=i.desktop&&i.mobile,d=s&&_||c&&a,p=Boolean(i.mobile&&a);if(c){if(i.desktop<=0)return;t_onFuncLoad("t_textClamp__init",(function(){t_textClamp__init(e,i.desktop,u)}))}else if(s&&p){if(i.mobile<=0)return;t_onFuncLoad("t_textClamp__init",(function(){t_textClamp__init(e,i.mobile,u)}))}else{if(l){var f=p?i.mobile:i.desktop;t_onFuncLoad("t_textClamp__init",(function(){t_textClamp__init(e,f,u),t_onFuncLoad("t_textClamp__updateClampLines",(function(){t_textClamp__updateClampLines(e,f,u)}))}))}d&&t_onFuncLoad("t_textClamp__reset",(function(){t_textClamp__reset(e,r)}))}}}function t_quiz__workWithAnswerCode(t){var e=t.querySelector(".t-input-group_ri");if(e){var n=e.querySelectorAll("input");Array.prototype.forEach.call(n,(function(t){var e;if(-1!==t.value.indexOf("value::")){t_quiz__setAnswerCode(t);var n,i=t.parentNode.querySelector(".t-img-select__text");i.textContent=i.textContent.split("value::")[0].trim()}}))}var i=t.querySelector(".t-input-group_rd");if(i){var o=i.querySelectorAll("input");Array.prototype.forEach.call(o,(function(t){var e,n;-1!==t.value.indexOf("value::")&&(t_quiz__setAnswerCode(t),t.parentNode.innerHTML=function(){var e;return t.innerHTML.split("value::")[0].trim()})}))}var r=t.querySelector(".t-input-group_sb");if(r){var u=r.querySelectorAll("option");Array.prototype.forEach.call(u,(function(t){var e;-1!==t.value.indexOf("value::")&&(t_quiz__setAnswerCode(t),t.textContent=t.textContent.split("value::")[0].trim())}))}}function t_quiz__runLazyLoad(){var t;if(document.querySelector(".t-records:not([data-tilda-mode])")){var e=document.querySelector("#allrecords");"y"!==window.lazy&&"yes"!==e.getAttribute("data-tilda-lazy")||t_onFuncLoad("t_lazyload_update",(function(){t_lazyload_update()}))}}function t_quiz__setAnswerCode(t){var e=t.value.split("value::")[1].trim();t.value=e}function t_quiz__showCounter(t,e,n){var i=t.querySelectorAll(".t-quiz__counter");i&&i.forEach((function(t){t.innerHTML=e+"/"+n}))}function t_quiz__setProgress(t,e,n){var i=t.querySelector(".t-quiz__progressbar");if(i&&null!=e){var o=parseFloat(getComputedStyle(i).width),r=t.querySelector(".t-quiz__progress"),u=o/n,a=100*Math.abs(e*u/o)+"%";r&&(r.style.width=a)}}function t_quiz__scrollPageToTopOf(t){var e=t.getBoundingClientRect().top+window.pageYOffset,n=t.closest(".t-quiz");if(!(e>=window.scrollY||n.classList.contains("t-quiz_scroll-disabled"))){var i;if(!!document.querySelector(".t228__positionfixed")){var o=document.querySelector(".t228__positionfixed:not(.t228__mobile)"),r=parseFloat(window.getComputedStyle(o).height);window.isMobile||isNaN(r)||(e=e-r-5)}window.scrollTo(0,e)}}function t_quiz__adjustHeightOnResize(t,e){window.addEventListener("resize",(function(){t_quiz__getDebouncedFunc(t_quiz__adjustStepToHeight,e+"_step-height")({rec:t})}))}function t_quiz__updateConsultantClampOnResize(t,e,n){window.addEventListener("resize",(function(){t_quiz__getDebouncedFunc(t_quiz__addConsultantClamp,e+"_cons-clamp")({rec:t,recId:e,lines:n})}))}function t_quiz__adjustStepToHeight(t){var e=t.rec,n=e.querySelector(".t-quiz"),i=e.querySelector(".t-quiz__quiz-wrapper"),o=e.querySelector(".t-step-form__step_active"),r=o&&o.classList.contains("t-quiz__result"),u=n.classList.contains("popup"),a=n.classList.contains("fixed-height"),_=n.classList.contains("fullscreen");if(a||_)u&&t_quiz__resizePopup(e);else if(!u||!t_quiz__isMobile()){var s=c();u&&t_quiz__resizePopup(e,s),i.style.overflow="hidden",setTimeout((function(){i.style.overflow=""}),300),r&&d(o,s),t_quiz__isMobile()||d(i,s)}function c(){var t=500,o=getComputedStyle(n),u=e.querySelector(".t-quiz__main"),a=e.querySelector(".t-quiz__panel"),_=e.querySelector(".t-quiz__content-wrapper > .t-quiz__footer"),s=o.getPropertyValue("--panel-type"),c="header"===s,d="sidebar"===s,p=u?u.offsetHeight:0,f=c?a.offsetHeight:0,q=_?_.offsetHeight:0,v=2*parseInt(o.getPropertyValue("--content-padding"),10);if(p<=0)return t;var z=p+f+q+v;d&&(z=Math.max(l(a),z));var m=parseInt(getComputedStyle(i).height,10);return m<=t&&z<=m&&(z=m),r&&(z=Math.max(z,t)),z}function l(t){var n=e.querySelector(".t-quiz__panel__main"),i=e.querySelector(".t-quiz__text-extra"),o=n?n.offsetHeight:0,r=i?i.offsetHeight:0,u=getComputedStyle(t),a,_,s,c;return o+r+parseInt(u.getPropertyValue("--sidebar-padding-top"),10)+parseInt(u.getPropertyValue("--sidebar-padding-bottom"),10)+(i?parseInt(u.getPropertyValue("--sidebar-gap"),10):0)}function d(t,e){t.style.height=e+"px"}}function t_quiz__getStepsManager(t){var e;return t.StepsManager}function t_quiz__onSuccess(t){var e;t instanceof Element||(t=t[0]);var n,i=t_quiz__getStepsManager(t.querySelector(".t-quiz__quiz-wrapper")),o=i.getCurrentStepInfo();if(o.isLast){var r=null==(e=o.step)?void 0:e.element;r&&t_quiz__pauseVideos(r),i.go("next",{onNewStepAnimationStart:function e(n,i){var o=i.element,r;if(o&&o.classList.contains("t-quiz__result_redirect")){var u=t.querySelector(".t-quiz__result_ordinary");o.classList.remove("t-step-form__step_active"),u.classList.add("t-step-form__step_active"),t_quiz__show(u),setTimeout((function(){t_quiz__handleRedirectResult(o)}),500)}}},{isOnSuccess:!0})}}function t_quiz__handleRedirectResult(t){if(t){var e=t.getAttribute("data-result-url");window.location.href=e}}function t_quiz__openToHook(t){var e=t.querySelector(".t-popup"),n=t.querySelectorAll(".t-popup__close"),i=e.getAttribute("data-tooltip-hook"),o=e.getAttribute("data-track-popup"),r=f.bind(e,t);function u(e,n){var i=t_quiz__analytics({rec:t});i&&i.openPopup(e,n)}function a(){var n;t.querySelectorAll('a[href*="#"]').forEach((function(e){e.addEventListener("click",(function(){var e=this.getAttribute("href");(!e||"#price:"!=e.substring(0,7)&&"#order:"!=e.substring(0,7))&&(s(t,r),e&&"#popup:"!=e.substring(0,7)||setTimeout((function(){"function"==typeof t_triggerEvent&&t_triggerEvent(document.body,"popupShowed"),document.body.classList.add("t-body_popupshowed")}),300))}))}));var i=e.querySelector(".t-submit"),o=i&&i.getAttribute("href");o&&"#order:"===o.substring(0,7)&&i.addEventListener("click",(function(){s(t,r)}))}function _(t,e){var n=t.querySelector(".t-popup");n.style.display="block";var i=document.body;t_triggerEvent(i,"popupShowed"),i.classList.add("t-body_popupshowed"),i.classList.add("t-quiz__body_popupshowed"),t_quiz__runLazyLoad();var o=t.querySelector(".t-range");o&&t_triggerEvent(o,"popupOpened"),setTimeout((function(){n.focus(),n.classList.add("t-popup_show");var t=n.querySelector(".t-popup__container"),e;t.classList.add("t-popup__container-animated"),t.classList.add("t-popup__container_no-transform"),t_onFuncLoad("t_popup__trapFocus",(function(){t_popup__trapFocus(n)})),window.innerWidth<=640&&c(),q("1","1")}),50),/iPhone|iPad|iPod/i.test(navigator.userAgent)&&!window.MSStream&&setTimeout((function(){d()}),500),document.addEventListener("keydown",e)}function s(t,e){var n=t.querySelector(".t-popup"),i=n.querySelector(".t-popup__container"),o=document.querySelector(".t-popup.t-popup_show"),r=t.querySelector(".t-step-form__step_active");if(r&&t_quiz__pauseVideos(r),n===o){var u=document.body;t_triggerEvent(u,"popupHidden"),u.classList.remove("t-body_popupshowed"),u.classList.remove("t-quiz__body_popupshowed")}n.classList.remove("t-popup_show"),i.style.removeProperty("transition"),i.classList.remove("t-popup__container_no-transform"),/iPhone|iPad|iPod/i.test(navigator.userAgent)&&!window.MSStream&&p(),t_onFuncLoad("t_popup__addFocusOnTriggerButton",(function(){t_popup__addFocusOnTriggerButton()})),q("2147483647","2147483648"),setTimeout((function(){var t;document.querySelectorAll(".t-popup:not(.t-popup_show)").forEach((function(t){t.style.display="none"}))}),300),document.removeEventListener("keydown",e)}function c(){l(),window.addEventListener("resize",l)}function l(){var t=.01*window.innerHeight;document.documentElement.style.setProperty("--vh",t+"px")}function d(){var t=document.body;if(!t.classList.contains("t-body_scroll-locked")){var e=window.scrollY||t.scrollTop;t.classList.add("t-body_scroll-locked"),t.style.top="-"+e+"px",t.setAttribute("data-popup-scrolltop",e)}}function p(){var t=document.body;if(t.classList.contains("t-body_scroll-locked")){var e=t.getAttribute("data-popup-scrolltop");t.classList.remove("t-body_scroll-locked"),t.style.top="",t.removeAttribute("data-popup-scrolltop"),window.scrollTo(0,parseInt(e))}}function f(t,e){"Escape"===e.key&&s(t,e)}function q(t,e){for(var n=document.querySelectorAll("._show_1e.wrap_mW.__jivoMobileButton"),i=0;i<n.length;i++)n[i].style.zIndex=t;for(var o=document.querySelectorAll(".label_39#jvlabelWrap"),i=0;i<o.length;i++)o[i].style.zIndex=e}i&&(document.addEventListener("click",(function(e){var n;e.target.closest('a[href="'+i+'"]')&&(e.preventDefault(),_(t,r),setTimeout((function(){t_quiz__adjustStepToHeight({rec:t})}),50),t_quiz__runLazyLoad(),t_quiz__displayStickyBtnWrapper(t),a(),o&&u(o,i))})),t_onFuncLoad("t_popup__addClassOnTriggerButton",(function(){t_popup__addClassOnTriggerButton(document,i)})),t_onFuncLoad("t_popup__addAttributesForAccessibility",(function(){t_popup__addAttributesForAccessibility(i)}))),e.addEventListener("click",(function(e){e.target===this&&s(t,r)})),n.length>0&&n.forEach((function(e){e.addEventListener("click",(function(){s(t,r)}))}))}function t_quiz__resizePopup(t,e){var n=120,i=window.innerHeight-n,o=t.querySelector(".t-popup__container"),r=getComputedStyle(o,null),u=parseInt(r.paddingTop)||0,a=parseInt(r.paddingBottom)||0,_=o.clientHeight-(u+a),s,c;(e||_)<=i?o.classList.remove("t-popup__container-static"):o.classList.add("t-popup__container-static")}function t_quiz__isMobile(){return window.isMobile||window.innerWidth<=640}function t_quiz__updateRangeSlider(t){var e=t.querySelectorAll(".t-range");e.length>0&&e.forEach((function(t){t_triggerEvent(t,"displayChanged")}))}function t_triggerEvent(t,e){var n;document.createEvent?(n=document.createEvent("HTMLEvents")).initEvent(e,!0,!1):document.createEventObject&&((n=document.createEventObject()).eventType=e),n.eventName=e,t.dispatchEvent?t.dispatchEvent(n):t.fireEvent?t.fireEvent("on"+n.eventType,n):t[e]?t[e]():t["on"+e]&&t["on"+e]()}function t_quiz__debounce(t,e){var n;return function(){var i=Array.prototype.slice.call(arguments);clearTimeout(n),n=setTimeout((function(){t.apply(null,i)}),e)}}function t_quiz__proccessVideo(t){return new Promise((function(e,n){t_onFuncLoad("t_video__createPlayer",(function(){t_video__createPlayer(t,{videoBlockId:t_quiz__getVideoId(t),jsApi:!0,playsinline:!0}).then((function(t){return e(t)})).catch((function(t){return n(t)}))}))}))}function t_quiz__initStepVideos(t){var e="data-qz-video-processed",n=t.querySelectorAll(".t-quiz__step__video-wrapper");n.length&&n.forEach((function(t){t.hasAttribute(e)||(t.setAttribute(e,"true"),t_quiz__proccessVideo(t).catch((function(){t.removeAttribute(e)})))}))}function t_quiz__pauseVideos(t,e){void 0===e&&(e=".t-quiz__step__video-wrapper");var n=t.querySelectorAll(e);n.length&&t_quiz__media__handlePauseVideos(n)}function t_quiz__media__handlePauseVideos(t){t_onFuncLoad("t_video__getPlayer",(function(){t.forEach((function(t){var e=t_video__getPlayer(t);e?e.pause():t_quiz__proccessVideo(t).then((function(t){return t.pause()}))}))}))}function t_quiz__getVideoId(t){var e=t.closest(".t-input-group"),n;return e?e.getAttribute("data-input-lid"):t_quiz__uniqueId()}function t_quiz__uniqueId(){return""+(Date.now()-Math.floor(1e3*Math.random())+10)}function t_quiz__analytics(t){var e=t.rec;if("QuizAnalytics"in e)return e.QuizAnalytics;if(window.Tilda&&window.Tilda.sendEventToStatistics){var n=e.querySelector(".t-form").id,i=t_quiz__analyitcs__tildaStatQueue(),o=new Set,r={viewStep:function t(e){var r="/tilda/"+n+"/step"+e;o.has(r)||(o.add(r),i.add((function(){window.Tilda.sendEventToStatistics(r)})))},openPopup:function t(e,n){var o=n.startsWith("#popup:")?n.replace("#popup:",""):n;i.add((function(){window.Tilda.sendEventToStatistics(e,o)}))},restart:function t(){o.clear()}};return e.QuizAnalytics=r,r}}function t_quiz__analyitcs__tildaStatQueue(){var t=[],e,n=function t(){return!0===window.tildastatload},i=function e(){for(;t.length>0;){var n;t.shift()()}},o=function t(){if(n())return i();e||(e=setInterval((function(){n()&&(clearInterval(e),i())}),300))},r;return{add:function e(n){t.push(n),o()}}}function t_quiz__applyTranslation(t){var e=t.querySelector(".t-quiz__counter-title");e&&(e.textContent=t_quiz__translate("step"))}function t_quiz__translate(t){var e={step:{RU:"Шаг: ",EN:"Step: ",DE:"Schritt: ",ES:"Paso: ",PT:"Passo: ",FR:"Étape: ",JA:"ステップ：",ZH:"步骤：",UK:"Крок: ",PL:"Krok: ",KK:"Қадам: ",IT:"Passo: ",LV:"Solis: "}};if(!e[t])return t;var n=t_quiz__getLang()||"EN";return e[t][n]}function t_quiz__getLang(){var t,e=document.getElementById("allrecords").getAttribute("data-tilda-project-lang"),n;return e||(window.navigator.userLanguage||window.navigator.language).toUpperCase().slice(0,2)}
