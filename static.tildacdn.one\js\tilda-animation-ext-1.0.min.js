function t_animationExt__init(){var t=document.querySelector(".t-records"),e=!!t&&"edit"===t.getAttribute("data-tilda-mode");/Bot/i.test(navigator.userAgent)||t_animateParallax__checkOldIE()||e||t_animationExt__isZeroBlocksRender((function(){t_animationExt__setAutoScaleInfo(),t_animationExt__wrapFixEls(),t_animationExt__wrapParallaxEls(),t_animateParallax__initScroll(),t_animateParallax__initMouse();var t=t_animationExt__getElsByBreakpoints("fix",[]),e;t_animationExt__createResizeObserver(t,t_animationExt__getElsByBreakpoints("prx",["mouse"])),t_animateFix__init(t)}))}function t_animationExt__setAutoScaleInfo(){if(void 0===window.shouldUseScaleFactor){var t=window.navigator.userAgent.match(/Firefox\/([0-9]+)\./),e=t?parseInt(t[1],10):126,a=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./),n=a&&parseInt(a[1],10)||0;window.isOnlyScalable=e<126,window.shouldUseScaleFactor=!window.isOnlyScalable&&!t&&n<=127}}function t_animationExt__isZeroBlocksRender(t){var e=t_animationExt__getArtboards("396",!0);if(e.length||(e=t_animationExt__getArtboards("121",!0)),e.length)if(e.every((function(t){return t.classList.contains("rendered")})))t();else if(document.querySelector('script[src*="tilda-blocks-2.7"]')){var a=Date.now();t_animationExt__backwardCompatibilityWait(e,a,t)}else{var n=e.filter((function(t){return t.classList.contains("rendered")}));e.forEach((function(a){a.classList.contains("rendered")||a.addEventListener("artBoardRendered",(function(){n.push(a),n.length===e.length&&t()}))}))}}function t_animationExt__getArtboards(t,e){var a='.r[data-record-type="'+t+'"]';return e&&(a+=":not(.t397__off):not(.t395__off):not(.t400__off)"),a+=" .t396__artboard",Array.prototype.slice.call(document.querySelectorAll(a))}function t_animationExt__backwardCompatibilityWait(t,e,a){if(t.every((function(t){return t.classList.contains("rendered")})))a();else{if(Date.now()-e>1e4)return void console.warn("all zero-blocks can't be rendered");setTimeout((function(){t_animationExt__backwardCompatibilityWait(t,e,a)}),500)}}function t_animationExt__createResizeObserver(t,e){if(e.length||t.length){var a=document.body.getClientRects();if(a.length&&"ResizeObserver"in window){var n=a[0].height,i;new ResizeObserver((function(i){i.forEach((function(i){var r=i.target;r.classList.contains("t-body_popupshowed")||r.classList.contains("t-body_scroll-locked")||i.contentRect.height===n||(n=a[0].height,e.forEach((function(t){t_animateParallax__cacheOffsets(t)})),t_animateFix__cacheElsInfo(t))}))})).observe(document.body)}}}function t_animationExt__wrapFixEls(){var t=t_animationExt__getElsByBreakpoints("fix",[]),e=t_animationExt__getArtBoardsScreens();t.forEach((function(t){var a;t_animationExt__getAttrByRes(t,"prx","data-animate-",0)&&e.forEach((function(e){var a;if(document.querySelector('[data-artboard-recid="'+e.recid+'"]')){var n=e.screens,i=e.screenMax;n.forEach((function(e){var a=e===i?"data-animate-prx":"data-animate-prx-res-"+e;t.removeAttribute(a)}))}}));var n=t.querySelector(".tn-molecule, .tn-atom"),i=t_animationExt__getScaleWrapper(n),r=t_animationExt__generateWrapperClassList(n,"sticky"),o=t_animationExt__generateWrapperSelector(n,"sticky"),l;t_animationExt__wrapEl(n,r,o,i),t_animationExt__updateBasicAnimationTriggerOnFixed(n.closest(o),t,e)}))}function t_animationExt__getScaleWrapper(t){if(!t)return null;var e=t.closest(".t396__group"),a=t.classList.contains("tn-atom");return e&&a?null:t.closest(".tn-atom__scale-wrapper")}function t_animationExt__updateBasicAnimationTriggerOnFixed(t,e,a){var n="t-animate";if(e.classList.contains(n)&&t){e.classList.remove(n),t.classList.add(n),e.classList.add("t-animate-for-wrapper");var i=["style","distance","duration","scale","delay"];a.forEach((function(a){var n;if(document.querySelector('[data-artboard-recid="'+a.recid+'"]')){var r=a.screens,o=a.screenMax;r.forEach((function(a){i.forEach((function(n){var i=t_animationExt__getAttrByRes(e,n,"data-animate-",a);i&&(a===o?(t.setAttribute("data-animate-"+n,i),e.removeAttribute("data-animate-"+n)):(t.setAttribute("data-animate-"+n+"-res-"+a,i),e.removeAttribute("data-animate-"+n+"-res-"+a)))}))}))}}))}}function t_animationExt__wrapParallaxEls(){var t;t_animationExt__getElsByBreakpoints("prx",["scroll","mouse"]).forEach((function(t){var e=t.querySelector(".tn-molecule, .tn-atom"),a=e.closest(".tn-atom__scale-wrapper, .tn-atom__sbs-anim-wrapper"),n,i;t_animationExt__wrapEl(e,t_animationExt__generateWrapperClassList(e,"prx"),t_animationExt__generateWrapperSelector(e,"prx"),a)}))}function t_animateFix__init(t){if(t.length){t_animateFix__cacheElsInfo(t),t_animateFix__updatePositions(t,!1);var e=document.getElementById("allrecords"),a=!!e&&"yes"===e.getAttribute("data-tilda-lazy");("y"===window.lazy||a)&&(window.t_animationExt__isLazy=!0,t_onFuncLoad("t_lazyload_update",(function(){t_lazyload_update()}))),window.addEventListener("resize",t_throttle((function(){t_animateFix__cacheElsInfo(t),t_animateFix__updatePositions(t,!0)}),100)),window.addEventListener("scroll",t_throttle((function(){t_animateFix__updatePositions(t,!1)}),30));var n=document.querySelectorAll(".t396");Array.prototype.forEach.call(n,(function(e){e.addEventListener("displayChanged",t_throttle((function(){t_animateFix__cacheElsInfo(t),t_animateFix__updatePositions(t,!0)}),30))}))}}function t_animateFix__updatePositions(t,e){var a=window.pageYOffset;t.forEach((function(t){if(t.fixedWrapperEl){var n=t_animate__getScaledOnePixShape(t);if(0!==t.distance){var i=t_animationExt__getZoom(t),r=a+t.triggerOffset*i,o=t.querySelector(".tn-atom__scale-wrapper");if(window.isOnlyScalable&&o&&t.fixedWrapperEl&&"fixed"!==t.fixedWrapperEl.style.position){var l,s,_=t.getBoundingClientRect().top-o.getBoundingClientRect().top;t.setAttribute("data-scaled-diff",_.toString())}var c=0;window.isOnlyScalable&&o&&(c=t.getAttribute("data-scaled-diff")||"0",c=parseInt(c,10),r=a+t.triggerOffset+c);var d=r>=t.topOffset,f=r<t.topOffset,m=r<t.end,u=r>=t.end,p=t.fixedWrapperEl.classList.contains("t-sticky_going"),g=t.fixedWrapperEl.classList.contains("t-sticky_ended"),x=t.classList.contains("tn-group"),E=x&&"hug"===t_animationExt__getAttrByRes(t,"heightmode","data-group-"),y,v=x&&"fill"===t_animationExt__getAttrByRes(t,"widthmode","data-group-")&&t.offsetWidth;if((d&&m&&(!p||e)||m&&g)&&(t.style.transform="",t.style.willChange="unset",t.fixedWrapperEl.style.position="fixed",t.fixedWrapperEl.style.top=(n?t.triggerOffset*i:t.triggerOffset)+c+"px",t.fixedWrapperEl.classList.add("t-sticky_going"),t.fixedWrapperEl.classList.remove("t-sticky_ended"),E&&(t.style.height=""),v&&(t.fixedWrapperEl.style.width=v+"px")),u&&!g){var w=window.isOnlyScalable?t.distance:t.distance/i;t.style.transform="translateY("+w+"px)",t.style.willChange="",t.fixedWrapperEl.style.top="",t.fixedWrapperEl.style.position="",t.fixedWrapperEl.classList.remove("t-sticky_going"),t.fixedWrapperEl.classList.add("t-sticky_ended"),E&&(t.style.height="initial"),v&&(t.fixedWrapperEl.style.width="inherit")}f&&t.fixedWrapperEl.classList.contains("t-sticky_going")&&(t.style.willChange="",t.fixedWrapperEl.style.top="",t.fixedWrapperEl.style.position="",t.fixedWrapperEl.classList.remove("t-sticky_going"),E&&(t.style.height="initial"),v&&(t.fixedWrapperEl.style.width="inherit"))}else e&&(t.fixedWrapperEl.style.position="",t.fixedWrapperEl.style.top="",t.fixedWrapperEl.classList.remove("t-sticky_going"),t.fixedWrapperEl.classList.remove("t-sticky_ended"))}}))}function t_animateFix__cacheElsInfo(t){var e=window.innerHeight;t.forEach((function(t){var a=t_animationExt__getZoom(t),n=parseInt(t.style.top,10);Number.isNaN(n)&&(n=t.offsetTop);var i=t_animate__getScaledOnePixShape(t);window.isOnlyScalable||i||(n*=a);var r=t_animateFix__getParentTopOffset(t),o,l=t_animationExt__generateWrapperSelector(t.querySelector(".tn-molecule, .tn-atom"),"sticky"),s=t.closest(".t396__group"),_=0;s&&s!==t&&(_=parseInt(s.style.top,10)||0,window.isOnlyScalable||(_*=a)),t.topOffset=r+n+_,t.trigger=parseFloat(t_animationExt__getAttrByRes(t,"fix","data-animate-",0))||0,t.distance=parseInt(t_animationExt__getAttrByRes(t,"fix-dist","data-animate-",0),10)||0,t.distance*=a,t.end=t.topOffset+t.distance,t.fixedWrapperEl=t.querySelector(l);var c=t.querySelector(".t-img");c?c.complete?t_animateFix__createOnloadImageListener(t,c,e):c.addEventListener("load",(function(){t_animateFix__createOnloadImageListener(t,c,e)})):t_animateFix__getElTrigger(t,e)}))}function t_animateFix__createOnloadImageListener(t,e,a){t_animateWaitForUploadImg(e,(function(){t_animateFix__getElTrigger(t,a),t_animateFix__updatePositions([t],!1)}))}function t_animateFix__getParentTopOffset(t){var e=t.closest(".r"),a,n;return e?e.getBoundingClientRect().top+window.pageYOffset+(parseInt(getComputedStyle(e).paddingTop,10)||0):0}function t_animate__getScaledOnePixShape(t){var e,a;if(!("shape"===t.getAttribute("data-elem-type")))return!1;if("yes"===t.getAttribute("data-scale-off"))return!0;var n=getComputedStyle(t);return(parseInt(n.height,10)<=2||parseInt(n.width,10)<=2)&&"0px"===n.borderWidth&&"none"===n.backgroundImage}function t_animateWaitForUploadImg(t,e){if(window.t_animationExt__isLazy)var a=setTimeout((function(){t.classList.contains("loaded")&&t.clientWidth&&t.src?(e(),clearTimeout(a)):t_animateWaitForUploadImg(t,e)}),300);else e()}function t_animateFix__getElTrigger(t,e){var a=t_animationExt__getZoom(t);if(t.triggerOffset=parseInt(t_animationExt__getAttrByRes(t,"fix-trgofst","data-animate-",0),10)||0,window.isOnlyScalable&&(t.triggerOffset*=a),.5===t.trigger||1===t.trigger){var n=e*t.trigger,i=t_animateFix__getPureElHeight(t)*t.trigger;i*=a,t.triggerOffset+=n,t.triggerOffset-=i,t.triggerOffset>t.topOffset&&t.triggerOffset<=n&&(t.triggerOffset=t.topOffset),window.isOnlyScalable||(t.triggerOffset/=a)}}function t_animateFix__getPureElHeight(t){var e=t_animationExt__getPureHeight(t),a;return e||t_animationExt__getPureHeight(t.querySelector(".tn-molecule, .tn-atom"))}function t_animateParallax__initMouse(){var t=t_animationExt__getElsByBreakpoints("prx",["mouse"]);t.length&&(t.forEach((function(t){var e;t.pathX=parseInt(t_animationExt__getAttrByRes(t,"prx-dx","data-animate-",0),10)||0,t.pathY=parseInt(t_animationExt__getAttrByRes(t,"prx-dy","data-animate-",0),10)||0,t.animEl=t.querySelectorAll(".tn-atom__prx-wrapper"),t_animateParallax__cacheOffsets(t),"image"===t.getAttribute("data-elem-type")&&t_animateParallax__cacheOffsets__OnImgLoad(t),t_animateParallax__moveEl(t)})),window.addEventListener("resize",t_throttle((function(){t.forEach((function(t){t_animateParallax__cacheOffsets(t)}))}),50)))}function t_animateParallax__cacheOffsets(t){var e=t_animationExt__getZoom(t);t.topOffset=t.getBoundingClientRect().top,window.shouldUseScaleFactor&&(t.topOffset*=e),t.topOffset+=window.pageYOffset;var a=t_animationExt__getPureHeight(t);t.bottomOffset=t.topOffset+a;var n=t.closest(".r"),i=n?n.getBoundingClientRect().top+window.pageYOffset:0,r,o=i+(n?t_animationExt__getPureHeight(n):0);i>t.topOffset&&(t.parentTopOffset=i),o<t.bottomOffset&&(t.parentBottomOffset=o)}function t_animateParallax__cacheOffsets__OnImgLoad(t){if(window.lazy){var e=t.querySelector("img");e&&e.addEventListener("load",(function(){t_animateParallax__cacheOffsets(t)}))}}function t_animateParallax__moveEl(t){if(!("ontouchend"in document&&document.documentElement.clientWidth<1200)){var e=window.innerHeight,a=window.innerWidth,n=t.pathX,i=t.pathY,r=0,o=0,l=0,s=0,_=!1,c=t.closest('[data-artboard-fixed="y"]'),d=Boolean(c),f=c&&c.getAttribute("data-artboard-fixed-trigger");t_animateParallax__preventAnimatedParentTrigger(t),document.body.addEventListener("mousemove",t_throttle((function(l){var s=window.pageYOffset,u,p=s+window.innerHeight<=t.topOffset||t.bottomOffset<=s;if(c&&f&&(d=c.classList.contains("t396__artboard-fixed-active")),void 0!==l&&(!p||d)){var g=l.pageY-l.clientY-100,x=l.pageY+e+100;if(d||!(window.innerWidth<1400&&(t.bottomOffset<g||t.topOffset>x)||t.parentTopOffset>l.pageY||t.parentBottomOffset<l.pageY)){if("number"==typeof n){var E=a/2,y,v=(E-l.clientX)/E;r=Math.round(n*v)}if("number"==typeof i){var w=e/2,h,b=(w-l.clientY)/w;o=Math.round(i*b)}_=!1,m()}}}),50))}function m(){_||(requestAnimationFrame(m),r&&(l+=.02*(r-l)),o&&(s+=.02*(o-s)),Math.abs(l-r)<1&&Math.abs(s-o)<1?_=!0:t&&t.animEl.length&&Array.prototype.forEach.call(t.animEl,(function(t){t.style.transform="translate3d("+l+"px, "+s+"px, 0px)"})))}}function t_animateParallax__preventAnimatedParentTrigger(t){if("none"!==getComputedStyle(t).pointerEvents){var e=t.querySelector(".tn-molecule, .tn-atom");e&&(t.style.pointerEvents="none",e.style.pointerEvents="auto")}}function t_animateParallax__initScroll(){var t=t_animationExt__getElsByBreakpoints("prx",["scroll"]);if(t.length){var e=t.filter((function(t){return t_animationExt__isElementHidden(t)}));t.forEach((function(t){if(!t_animationExt__isElementHidden(t)){var e=t_animationExt__getAttrByRes(t,"prx-s","data-animate-",0),a=Math.round((parseInt(e)-100)/10),n=t.querySelector(".tn-atom__prx-wrapper");n&&a&&n.setAttribute("data-parallax-speed",a.toString())}})),t.length&&t_animationExt__createScrollParallax("[data-parallax-speed]"),e.length&&window.addEventListener("scroll",t_throttle((function(){var t=e.filter((function(t,a){return!t_animationExt__isElementHidden(t)&&(e.splice(a,1),!0)}));if(t.length){var a,n=t_animationExt__getAttrByRes(t[t.length-1],"prx-s","data-animate-",0),i="parallax"+Date.now();t.forEach((function(t){var e=t.querySelector(".tn-atom__prx-wrapper"),a=Math.round((parseInt(n)-100)/10);e&&e.setAttribute("data-parallax-speed",a.toString()),t.classList.add(i)})),t_animationExt__createScrollParallax("."+i)}}),50))}}function t_animationExt__createScrollParallax(t){var e=Array.prototype.slice.call(document.querySelectorAll(t));if("IntersectionObserver"in window){var a=t_animationExt__findParallaxParents(e);t_animationExt__updateParallax(a,e,t),window.t_animationExt__isMobile?window.addEventListener("orientationchange",(function(){setTimeout((function(){t_animationExt__updateParallax(a,e,t)}),300)})):window.addEventListener("resize",t_throttle((function(){t_animationExt__updateParallax(a,e,t)}),50))}else t_animationExt__createParallaxByScroll(t)}function t_animationExt__updateParallax(t,e,a){t.forEach((function(t){t_animationExt__updateParallaxOffset(t,a)})),e.forEach((function(e){t_animationExt__updateParallaxParams(t,e)}))}function t_animationExt__updateParallaxOffset(t,e){var a;new IntersectionObserver((function(t,a){t.forEach((function(t){if(t.isIntersecting){var n=t.target,i;t_animationExt__setParallaxOffsets(Array.prototype.slice.call(n.querySelectorAll(e))),a.unobserve(n)}}))})).observe(t)}function t_animationExt__updateParallaxParams(t,e){var a;new IntersectionObserver((function(t,e){t.forEach((function(t){if(t.isIntersecting){var a=t.target;a.isReady?t_animationExt__animateParallaxOnScroll(a,"start"):a.addEventListener("parallaxReady",(function(){t_animationExt__animateParallaxOnScroll(a,"start")}),{once:!0}),e.unobserve(a)}}))})).observe(e)}function t_animationExt__setParallaxOffsets(t){t.forEach((function(t){var e=t.getAttribute("data-parallax-speed"),a=window.pageYOffset,n=t_animationExt__calcScaledDiff(t),i=t.getBoundingClientRect().top+a+n,r=t.clientHeight,o=a>i,l=t_animationExt__getZoom(t);window.shouldUseScaleFactor&&(i*=l);var s=10,_=e?t_animationExt__getParallaxSpeed(e,-10,s):0,c=document.createEvent("Event");c.initEvent("parallaxReady",!0,!0),t.isAboveParallax=o,t.speed=_,t.topPos=i,t.bottomPos=i+r,t.isStarted=!1,t.style.transform="";var d=t.querySelector(".t-img");d?d.complete&&0!==d.clientHeight&&d.src&&d.clientWidth?t_animationExt__triggerParallaxInit(t,c):d.addEventListener("load",(function(){t_animationExt__triggerParallaxInit(t,c)})):t_animationExt__triggerParallaxInit(t,c)}))}function t_animationExt__triggerParallaxInit(t,e){t.isReady=!0,t.dispatchEvent(e)}function t_animationExt__animateParallaxOnScroll(t,e){if("animate"!==e||t.isStarted){requestAnimationFrame((function(){t_animationExt__animateParallaxOnScroll(t,"animate")}));var a=document.documentElement.clientHeight,n,i,r=(window.pageYOffset+a-t.topPos)/(t.clientHeight+a),o=t_animationExt__getParallaxPosition(t.speed,r);"start"===e?(t.posY=o,t.isStarted=!0):(o-=t.posY,t.style.transform="translateY("+o+"px)")}}function t_animationExt__findParallaxParents(t){var e=[],a=[];return t.forEach((function(t){var n=t.closest(".r"),i;n&&(-1===e.indexOf(n.id)&&(e.push(n.id),a.push(n)))})),a}function t_animationExt__createParallaxByScroll(t){var e=Array.prototype.slice.call(document.querySelectorAll(t)),a=t_animationExt__getParallaxOffests(e);window.t_animationExt__isMobile?window.addEventListener("orientationchange",(function(){setTimeout((function(){t_animationExt__animateParallaxByScroll(a=t_animationExt__getParallaxOffests(e))}),300)})):window.addEventListener("resize",t_throttle((function(){t_animationExt__animateParallaxByScroll(a=t_animationExt__getParallaxOffests(e))}),50)),window.addEventListener("scroll",(function(){t_animationExt__animateParallaxByScroll(a)}))}function t_animationExt__getParallaxOffests(t){return t.map((function(t){var e=t.getAttribute("data-parallax-speed"),a=window.pageYOffset,n=t_animationExt__calcScaledDiff(t),i=t.getBoundingClientRect().top+a+n,r=t.clientHeight,o=a>i,l=t_animationExt__getZoom(t);window.shouldUseScaleFactor&&(i*=l);var s=.5,_=10,c=e?t_animationExt__getParallaxSpeed(e,-10,_):0,d;return{el:t,elHeight:r,elTopPos:i,elBottomPos:r+i,posY:t_animationExt__getParallaxPosition(c,s),speed:c,isAboveParallax:o}}))}function t_animationExt__animateParallaxByScroll(t){var e=document.documentElement.clientHeight,a=window.pageYOffset,n=a+e;t.forEach((function(t){var i=t.elTopPos<n&&t.elBottomPos>a&&t.elBottomPos<=n&&t.elTopPos>=a;if(t.isAboveParallax||i){var r=(n-t.elTopPos)/(t.elHeight+e),o=t_animationExt__getParallaxPosition(t.speed,r);t.isAboveParallax||(t.posY=o,t.isAboveParallax=!0),o-=t.posY,t.el.style.transform="translateY("+o+"px)"}}))}function t_animationExt__calcScaledDiff(t){var e=t.querySelector(".tn-atom__scale-wrapper");if(window.isOnlyScalable&&e&&t){var a,n,i=t.getBoundingClientRect().top-e.getBoundingClientRect().top;t.setAttribute("data-scaled-diff",i.toString())}var r=t.getAttribute("data-scaled-diff")||"0";return parseInt(r,10)}function t_animationExt__getParallaxSpeed(t,e,a){return t<=e?e:t>=a?a:t}function t_animationExt__getParallaxPosition(t,e){return Math.round(t*(100*(1-e)))}function t_animationExt__getPureHeight(t){return t?(t.clientHeight||t.offsetHeight||parseInt(window.getComputedStyle(t).height,10))-((t.style.paddingTop||0)+(t.style.paddingBottom||0)):0;var e,a,n}function t_animationExt__isElementHidden(t){return!t.offsetWidth&&!t.offsetHeight&&!t.getClientRects().length}function t_animateParallax__checkOldIE(){var t=window.navigator.userAgent.indexOf("MSIE");if(-1===t)return!1;var e=parseInt(window.navigator.userAgent.substring(t+5,window.navigator.userAgent.indexOf(".",t)),10);return 8===e||9===e||10===e}function t_animationExt__getZoom(t){if(void 0!==t.scaleFactor)return t.scaleFactor;var e=t.closest(".t396__artboard");if(!e)return 1;var a=e.getAttribute("data-artboard-recid");return e.classList.contains("t396__artboard_scale")||"window"===t_animationExt__getAttrByRes(e,"data-artboard-upscale","",0)?a&&window.tn["ab"+a]&&window.tn["ab"+a].scaleFactor||window.tn_scale_factor:1}function t_animationExt__getAttrByRes(t,e,a,n){if(!t)return"";var i="y"===t.getAttribute("data-animate-mobile"),r,o=[],l,s,_,c="ab"+t.closest(".t396__artboard").getAttribute("data-artboard-recid");if(void 0!==window.tn[c]?(n||(n=window.tn[c].curResolution),l=window.tn[c].curResolution_max,o=window.tn[c].screens.slice(0,window.tn[c].screens.length-1)):(n||(n=window.tn.curResolution),l=1200,o=[320,480,640,960]),n===l)return t.getAttribute(a+e);if(a&&!i&&n<1200)return t.style.transition="none","";if(!(r=t.getAttribute(a+e+"-res-"+n)))for(var d=0;d<o.length;d++){var f=o[d];if(!(f<=n)&&(r=f===l?t.getAttribute(a+e):t.getAttribute(a+e+"-res-"+f)))break}return r||(t.getAttribute(a+e)||"")}function t_animationExt__getElsByBreakpoints(t,e){var a=t_animationExt__getArtBoardsScreens(),n=[],i="fix"===t;return a.forEach((function(a){var r='[data-artboard-recid="'+a.recid+'"]';i&&(r+=':not([data-artboard-fixed="y"])');var o=document.querySelector(r);if(o){var l=a.screens,s=a.screenMax;l.forEach((function(a){if(e&&e.length)e.forEach((function(e){var i="[data-animate-"+t+'="'+e+'"]';a!==s&&(i="[data-animate-"+t+"-res"+a+'="'+e+'"]');var r=Array.prototype.slice.call(o.querySelectorAll(i));r.length&&(n=n.concat(r))}));else{var i=a===s?"[data-animate-"+t+"]":"[data-animate-"+t+"-res-"+a+"]",r=Array.prototype.slice.call(o.querySelectorAll(i));r.length&&(n=n.concat(r))}}))}})),n}function t_animationExt__getArtBoardsScreens(){var t=[],e=t_animationExt__getArtboards("396",!1);return e=e.concat(t_animationExt__getArtboards("121",!1)),Array.prototype.forEach.call(e,(function(e){var a=e.getAttribute("data-artboard-recid"),n="ab"+a,i=[],r;void 0!==window.tn[n]?(i=window.tn[n].screens.slice().reverse(),r=window.tn[n].curResolution_max):(i=[1200,960,640,480,320],r=1200),t.push({recid:a,screens:i,screenMax:r})})),t}function t_animationExt__wrapEl(t,e,a,n){if(!t.closest(a)){var i=t.closest(".tn-elem, .tn-group"),r=document.createElement("div"),o="image"===t.parentElement.getAttribute("data-elem-type");e.forEach((function(t){r.classList.add(t)})),r.style.display=o?"inherit":"table",r.style.width="inherit",r.style.height="inherit",n?r.appendChild(n):r.appendChild(t),i.appendChild(r),t_animationExt__updateStylesAfterWrapping(n||t,r,i)}}function t_animationExt__generateWrapperClassList(t,e){var a,n="__"+e+"-wrapper";return t.classList.contains("tn-molecule")?["tn-molecule"+n,"tn-atom"+n]:["tn-atom"+n]}function t_animationExt__generateWrapperSelector(t,e){var a,n="__"+e+"-wrapper";return t.classList.contains("tn-molecule")?".tn-molecule"+n:".tn-atom"+n+":not(.tn-molecule"+n+")"}function t_animationExt__updateStylesAfterWrapping(t,e,a){var n=a?a.getAttribute("data-elem-type"):"",i=window.getComputedStyle(a).getPropertyValue("border-radius");"shape"===n&&parseInt(i,10)&&(e.style.borderRadius=i);var r=["filter","backdrop-filter"],o;if((r=(r=r.map((function(t){var e="-webkit-"+t,n=window.getComputedStyle(a).getPropertyValue(t);if("none"!==n&&""!==n||(n=window.getComputedStyle(a).getPropertyValue(e)),"none"!==n&&""!==n)return{filter:t,webkitFilter:e,value:n}}))).filter((function(t){return t}))).forEach((function(n){-1!==navigator.userAgent.search("Firefox")&&(e.style.backfaceVisibility="visible"),e.style[n.webkitFilter]=n.value,e.style[n.filter]=n.value,a.style[n.webkitFilter]="none",a.style[n.filter]="none","none"===window.getComputedStyle(t).transform&&(t.style.transform="translateZ(0)")})),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&r.length&&"image"===n){var l=document.createEvent("Event");l.initEvent("backdropFilterImgWrappered",!0,!0),a.dispatchEvent(l),a.classList.add("t396__elem--backdrop-filter-img-wrappered")}t_animationExt__chromeFixBackdropFilter(t,e,r)}function t_animationExt__chromeFixBackdropFilter(t,e,a){var n=a.some((function(t){return"backdrop-filter"===t.filter}));if(-1!==navigator.userAgent.indexOf("Chrome")&&n){var i=window.getComputedStyle(t).getPropertyValue("background-color"),r=window.getComputedStyle(t).getPropertyValue("opacity");if("rgba(0, 0, 0, 0)"!==i&&"1"!==r){var o=i.substring(i.indexOf("(")+1,i.indexOf(")"));e.style.backgroundColor="rgba("+o+","+r+")",t.style.opacity="1",t.style.backgroundColor="transparent"}}}window.t_animationExt__isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)||"ontouchend"in document,t_onReady(t_animationExt__init);
