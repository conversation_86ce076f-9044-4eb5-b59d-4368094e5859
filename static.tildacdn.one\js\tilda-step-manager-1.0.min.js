function t_quiz__step_manager(t){void 0===t&&(t={steps:[],activeStepClass:""});var n={index:-1,step:null},e=t.steps,i=t.activeStepClass||"t-step-form__step_active",a=t.hiddenStepClass||"t-quiz-hidden",o=t.animation,r=t.enableConditionals,s=t.enableAutoStepChange,u=!1,c,l=300,p=n;function f(t){t.element.classList.add(i),t.element.classList.remove(a)}function d(t){t.element.classList.remove(i),t.element.classList.add(a)}function m(t){var n=t.element.querySelectorAll(".t-quiz__step__input"),e=n.length&&Array.from(n).every((function(t){return t_quiz__isElementHidden(t)}));return t_quiz__isElementHidden(t.element)||e}function v(){var t=e.findIndex((function(t,n){return n>p.index&&!m(t)})),n=Math.min(e.length,t);return{index:n,step:e[n]}}function _(){for(var t=p.index,n=-1===t?e.length-1:t-1,i=n;i>=0;i--){var a;if(!m(e[i])){n=i;break}}var o=Math.max(0,n);return{index:o,step:e[o]}}function g(t){return e.filter((function(n){return-1!==t.indexOf(n.type)&&!m(n)}))}function h(){var t=g(["step","contacts"]),n=t.indexOf(p.step);return{total:t.length,current:-1===n?null:n+1}}function S(n,e,i){var a=p.step,o;if(t.checkIfCanChangeStep&&!t.checkIfCanChangeStep(n,x(),i))return;var r="next"===n?v():_();p=r;var s=x();return b(a,r.step,e),s}function C(){return e.filter((function(t){return!m(t)||t===p.step}))}function A(){var t=C(),n=t.indexOf(p.step),e,i;return t.splice(n+1).every((function(t){return"step"!==t.type&&"contacts"!==t.type}))}function y(){var t=C(),n=t.indexOf(p.step),e;return 0===t.splice(0,n).length}function x(){var t,n;return{type:null==(t=p.step)?void 0:t.type,index:p.index,isFirst:y(),isLast:A(),step:p.step}}function L(){var t=["ri","rd","rs","sb","cb"],n;e.filter((function(n){var e=n.element,i;if(!e.querySelector(".t-quiz__inputs-wrapper")){var a=e.querySelector(".t-input-group");if(a){var o=t.includes(a.getAttribute("data-field-type")),r="cb"===a.getAttribute("data-field-radcb");return o&&!r}}})).forEach((function(t){var n;Array.from(t.element.querySelectorAll(".t-input-group")).forEach((function(t){t.addEventListener("change",(function(t){var n;t.target.classList.contains("t-radio-ownanswer")||t.target.classList.contains("t-input-ownanswer")||w()}))}))}))}function w(){u||(u=!0,c=setTimeout((function(){S("next",{},{isAutoTransition:!0})}),l))}function E(){s&&(clearTimeout(c),u=!1)}function b(n,e,r){void 0===r&&(r={});var s=t,u=s.onStepChange,c=s.onProgressChange,l=o.callbacks||{},p=l.onAnimationComplete,m=l.onAnimationStart,v=l.onNewStepAnimationStart;if(!o)return n&&d(n),f(e),u&&u(e),c&&c(h()),p&&p(n,e),r.onAnimationComplete&&r.onAnimationComplete(n,e),void E();t_quiz__animateSteps(n,e,{onAnimationStart:function t(n,e){m&&m(n,e),r.onAnimationStart&&r.onAnimationStart(n,e)},onNewStepAnimationStart:function t(n,e){v&&v(n,e),r.onNewStepAnimationStart&&r.onNewStepAnimationStart(n,e),u&&u(e),c&&c(h()),E()},onAnimationComplete:function t(n,e){p&&p(n,e),r.onAnimationComplete&&r.onAnimationComplete(n,e)}},{animation:{in:q(e),out:q(n)},stepActiveClass:i,stepHiddenClass:a})}function q(t){return t&&"string"!=typeof o?o.type[t.type]||"fade-with-move":o}function z(){r?t_onFuncLoad("t_form__conditionals_initFields",(function(){var n=t_form__conditionals_initFields(t.recId);n.addListeners((function(){t.onProgressChange&&t.onProgressChange(h())})),n.addStateChangeListeners((function(n){t.onConditionalStateChange&&t.onConditionalStateChange(n)})),s&&L(),S("next",{onAnimationComplete:t.onInit})})):(s&&L(),S("next",{onAnimationComplete:t.onInit}))}function I(){d(p.step),p=n,S("next")}return{go:S,init:z,restart:I,getStepsProgress:h,getCurrentStepInfo:x}}function t_quiz__animateSteps(t,n,e,i){var a=700,o=400,r="forwards",s="ease-in-out",u=i||{},c=u.animation,l=void 0===c?{in:"fade-with-move",out:"fade-with-move"}:c,p=u.stepActiveClass,f=u.stepHiddenClass,d=function t(){},m=e||{},v=m.onAnimationComplete,_=void 0===v?d:v,g=m.onAnimationStart,h=void 0===g?d:g,S=m.onNewStepAnimationStart,C=void 0===S?d:S,A;if(!!!document.body.animate)return h(t,n),t&&t.element.classList.remove(p),t&&t.element.classList.add(f),C(t,n),n&&n.element.classList.add(p),n&&n.element.classList.remove(f),void _(t,n);function y(t){var n=t.animate([{opacity:0,pointerEvents:"none"},{opacity:1,pointerEvents:"auto"}],{duration:a,fill:r,easing:s});return n.pause(),n}function x(t){var n=t.animate([{transform:"translateY(15px)",opacity:0,pointerEvents:"none"},{transform:"translateY(0px)",opacity:1,pointerEvents:"auto"}],{duration:a,fill:r,easing:s});return n.pause(),n}function L(t){var n=t.animate([{opacity:1,pointerEvents:"none"},{opacity:0,pointerEvents:"auto"}],{duration:o,fill:r,easing:s});return n.pause(),n}function w(t){var n=t.animate([{transform:"translateY(0)",opacity:1,pointerEvents:"none"},{transform:"translateY(20px)",opacity:0,pointerEvents:"auto"}],{duration:o,fill:r,easing:s});return n.pause(),n}function E(){_(t,n)}function b(){t.element.classList.remove(p),t.element.classList.add(f),q()}function q(){if(!n)return C(t,n),void E();var e;(e="fade-with-move"===l.in?x(n.element):y(n.element)).finished.then(E),n.element.classList.remove(f),n.element.classList.add(p),e.play(),C(t,n)}function z(){var n;(n="fade-with-move"===l.out?w(t.element):L(t.element)).finished.then(b),n.play()}return h(t,n),t?z():q()}function t_quiz__isElementHidden(t){return"true"===t.getAttribute("data-hidden-by-condition")||t.classList.contains("t-form__hidden")}
