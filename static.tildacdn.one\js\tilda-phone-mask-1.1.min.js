function t_ready(e){"loading"!=document.readyState?e():document.addEventListener("DOMContentLoaded",e)}function t_siblings(e,t){return e.parentNode?Array.prototype.filter.call(e.parentNode.children,(function(e){return e.classList.contains(t)})):[]}function t_remove(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function t_form_phonemask__initPhoneMask(){var e;Array.from(document.querySelectorAll(".js-phonemask-input")).forEach((function(e){var t;"true"===e.getAttribute("data-phonemask-skip-auto-init")||t_form_phonemask_load(e)}))}function t_form_phonemask_load_one(e){var t=document.querySelectorAll("#rec"+e+" .js-phonemask-input");Array.prototype.forEach.call(t,t_form_phonemask_load)}function t_form_phonemask_load(e){var t=e;if(t instanceof Element||(t=t[0]),t&&void 0!==t.getAttribute){var n=t.getAttribute("data-phonemask-id"),a=t.getAttribute("data-phonemask-lid"),o=t.getAttribute("data-phonemask-maskcountry")||"";t.setAttribute("data-phonemask-init","yes"),t_form_phonemask_init(n,a,o)}}function t_form_phonemask__mulitple__load(e){if(e.length){var t=e[0],n,a,o;t_form_phonemask_init(t.getAttribute("data-phonemask-id"),t.getAttribute("data-phonemask-lid"),t.getAttribute("data-phonemask-maskcountry")||"",{multiple:!0}),e.forEach((function(e){e.setAttribute("data-phonemask-init","yes")}))}}function t_form_phonemask_init(e,t,n,a){var o,p=(a||{}).multiple,r,i=void 0!==p&&p?t_form_phonemask__multiple__initPhoneMaskForm:t_form_phonemask_initPhoneMaskForm,s=n;if(s&&"undefined"!==s)i(e,t,s);else if(window.geoCountry)i(e,t,window.geoCountry);else if(window.geoCountryRequested)document.addEventListener("geoCountryLoaded",(function(){i(e,t,window.geoCountry)}));else{var m=new XMLHttpRequest,c=t_form_phonemask_getRootZone();m.open("GET","https://geo.tildaapi."+c+"/geo/country/",!0),m.onreadystatechange=function(){if(4===m.readyState)if(m.status>=200&&m.status<400){s=m.responseText,window.geoCountry=s;var n=document.createEvent("Event");n.initEvent("geoCountryLoaded",!0,!1),document.dispatchEvent(n),i(e,t,s)}else{var a,o=Object.keys(t_form_phonemask__getCountriesList()),p=null==(a=t_form_phonemask__getProjectCountryIso())?void 0:a.toLowerCase(),r;r=p&&o.includes(p)?p:o[0],i(e,t,r)}},m.send(),window.geoCountryRequested=!0}}function t_form_phonemask__getProjectCountryIso(){var e=document.getElementById("allrecords");return e&&e.getAttribute("data-tilda-project-country")||""}function t_form_phonemask_initPhoneMaskForm(e,t,n){var a=document.querySelector("#rec"+e);if(a){var o=t_form_phonemask__getInputGroup(a,t);if(o&&!o.getAttribute("data-init-mask")){document.getElementById("phone-mask-style")||t_form_phonemask__addStyle();var p=t_form_phonemask__prepareISOandCountry(n);t_form_phonemask__saveISOtoLocalStorage(p.iso),t_form_phonemask__prepareInputGroup({rec:a,group:o,countryConfig:p,lid:t}),t_form_phonemask__triggerInitMask(o),t_form_phonemask__initResetListener(o,p)}}}function t_form_phonemask__multiple__initPhoneMaskForm(e,t,n){void 0===n&&(n="");var a=document.querySelector("#rec"+e);if(a){var o=t_form_phonemask__getInputGroup(a,t);if(o&&!o.getAttribute("data-init-mask")){document.getElementById("phone-mask-style")||t_form_phonemask__addStyle();var p=t_form_phonemask__prepareISOandCountry(n),r;t_form_phonemask__saveISOtoLocalStorage(p.iso),o.querySelectorAll(".t-phonemask-input-group").forEach((function(e){t_form_phonemask__prepareInputGroup({group:e,countryConfig:p,rec:a,lid:t}),t_form_phonemask__initResetListener(e,p)})),t_form_phonemask__triggerInitMask(o)}}}function t_form_phonemask__replaceInput(e){var t=e.querySelector(".js-phonemask-input");if(t){var n=t.getAttribute("name"),a=t.getAttribute("data-tilda-req")?'data-tilda-req="1"':"",o=!!t.classList.contains("t-input_bbonly"),p=t.getAttribute("style"),r=t.value;t.outerHTML='<div class="t-input t-input-phonemask__wrap" style="'+p+'"><div class="t-input-phonemask__select"><span class="t-input-phonemask__select-flag" data-phonemask-flag=""></span><span class="t-input-phonemask__select-triangle"></span><span class="t-input-phonemask__select-code"></span></div><input type="hidden" class="js-phonemask-result-iso" name="tildaspec-phone-part[]-iso" value="" tabindex="-1"><input type="tel" class="t-input t-input-phonemask" name="tildaspec-phone-part[]" value="'+r+'" placeholder=""><input type="hidden" class="js-phonemask-result js-tilda-rule" data-tilda-rule="phone" name="'+n+'" value="'+r+'" '+a+' tabindex="-1"></div>';var i=e.querySelector(".t-input-phonemask__wrap");o&&i.classList.add("t-input_bbonly");var s=t_siblings(i,"t-input__vis-ph"),m=document.querySelector(".t-input-phonemask__options-wrap");m||((m=document.createElement("div")).classList.add("t-input-phonemask__options-wrap"),m.setAttribute("data-nosnippet",""),document.body.append(m)),0!==s.length&&Array.prototype.forEach.call(s,(function(e){t_remove(e)}))}}function t_form_phonemask__prepareInputGroup(e){var t=e.group,n=e.countryConfig,a=e.lid,o=e.rec,p=n.iso,r=n.countries,i=n.currentCountry;t_form_phonemask__replaceInput(t);var s=t.querySelector(".t-input-phonemask");if(s){var m=t_form_phonemask__preparePhoneMaskInput(s,{lid:a,iso:p,country:i}),c=m.code,u=m.placeholder,l=t.querySelector(".t-input-phonemask__select-flag"),_=t.querySelector(".t-input-phonemask__select-code");l.setAttribute("data-phonemask-flag",p),_.innerHTML=c,t_form_phonemask__changeMinLength(u,c,t),t_form_phonemask__addCurrentStyle(t),t_form_phonemask__initSelectEvents(o,t,r)}}function t_form_phonemask__getInputGroup(e,t){var n=e.querySelector('[data-input-lid="'+t+'"]'),a;return n||(window.tildamode&&"zero"===window.tildamode?e.querySelector('.tn-artboard [data-input-lid="'+t+'"]')||e.querySelector(".tn-artboard"):document.body)}function t_form_phonemask__prepareISOandCountry(e){void 0===e&&(e="");var t={n:"Russian Federation (Российская Федерация)",c:"+7",m:"+7(999) 999-99-99"},n="ru",a=e;a.length>0&&(a=a.toLowerCase()),a.length>2&&(a="");var o=t_form_phonemask__getCountriesList(),p=o[a];return p||(p=t,a=n),{iso:a,countries:o,currentCountry:p}}function t_form_phonemask__saveISOtoLocalStorage(e){try{localStorage.setItem("iso_code",e)}catch(t){console.error("Your web browser does not support storing a ISO Phone Code data locally.",t)}}function t_form_phonemask__preparePhoneMaskInput(e,t){var n=t.iso,a=t.country,o=t.lid,p=a.c||"",r=a.m||"",i=r?r.substr(p.length).replace(/^-+/,""):"";return e.setAttribute("id","input_"+o),e.setAttribute("data-phonemask-iso",n),e.setAttribute("data-phonemask-code",p),e.setAttribute("data-phonemask-mask",r),e.setAttribute("placeholder",i),t_form_phonemask__calcMaxlength(e),{code:p,mask:r,placeholder:i}}function t_form_phonemask__calcMaxlength(e){var t=e.getAttribute("data-phonemask-mask").substr(e.getAttribute("data-phonemask-code").length);"-"===t.charAt(0)&&(t=t.substring(1)),e.setAttribute("maxlength",t.length),e.setAttribute("data-phonemask-without-code",t)}function t_form_phonemask__triggerInitMask(e){e.dispatchEvent(new CustomEvent("phoneMaskReady")),e.dispatchEvent(new CustomEvent("inputReady")),e.setAttribute("data-init-mask","yes"),e.setAttribute("data-input-ready","true")}function t_form_phonemask__initResetListener(e,t){e.addEventListener("reset",(function(){t_form_phonemask__setValue(e,"",t.iso,{silent:!0,noFocus:!0})}))}function t_form_phonemask__addCurrentStyle(e){var t=e.querySelector(".t-input-phonemask__wrap"),n=e.querySelector(".t-input-phonemask"),a=e.querySelector(".t-input-block"),o=e.querySelector(".t-input-phonemask__select-code"),p=t.style.color,r=t.style.fontSize,i=t.style.fontWeight,s=t.style.fontFamily;a.style.overflow="visible","rgb(0, 0, 0)"!==p&&(n.style.color=p),n.style.fontSize=r,n.style.fontWeight=i,n.style.fontFamily=s,o.style.fontSize=r,o.style.fontWeight=i}function t_form_phonemask__getDrawSelector(e){var t="";for(var n in t+="<noindex>",e)t+='<div class="t-input-phonemask__options-item" id="t-phonemask_'+n+'" data-phonemask-name="'+e[n].n+'" data-phonemask-mask="'+e[n].m+'" data-phonemask-country-code="'+n+'" data-phonemask-code="'+e[n].c+'">',t+='<div class="t-input-phonemask__options-name">'+e[n].n+"</div>",t+='<div class="t-input-phonemask__options-right">',t+='<span class="t-input-phonemask__options-code">'+e[n].c+"</span>",t+='<span class="t-input-phonemask__options-flag t-input-phonemask__options-flag_'+n+'"></span>',t+="</div>",t+="</div>";return t+="</noindex>"}function t_form_phonemask__initSelectEvents(e,t,n){var a=t.querySelector(".t-input-phonemask"),o=t.querySelector(".t-input-phonemask__select"),p=document.querySelector(".t-input-phonemask__options-wrap"),r=t.closest(".t396__elem"),i=document.querySelector(".t-popup"),s="",m="";function c(){clearTimeout(m),m=setTimeout((function(){s=""}),1500)}function u(){p.innerHTML=t_form_phonemask__getDrawSelector(n)}function l(){var e=p.querySelectorAll(".t-input-phonemask__options-item");Array.prototype.forEach.call(e,(function(e){e.addEventListener("click",S)}))}function _(){var e=t.getBoundingClientRect();p.style.top=e.y+e.height+5+"px",p.style.left=e.x+"px"}function d(){return r?parseFloat(getComputedStyle(r).zoom):1}function x(){var e=t.getBoundingClientRect(),n=document.body.getBoundingClientRect(),a=d();p.style.position="absolute",p.style.top=(e.top-n.top+e.height)*a+5+"px",p.style.left=e.x+"px"}function h(){var e,t=1!==d();return(window.isiOS||window.isSafari)&&t}var k=t_throttle(_,1e3/30);function g(){window.addEventListener("resize",k),document.addEventListener("scroll",k),i&&i.addEventListener("scroll",k)}function f(){window.removeEventListener("resize",k),document.removeEventListener("scroll",k),i&&i.removeEventListener("scroll",k)}function b(){u(),_(),l(),f(),h()?x():(p.style.position="",g())}function y(){p.classList.contains("t-input-phonemask__options-wrap_open")?v():(v(),p.classList.add("t-input-phonemask__options-wrap_open"),b())}function v(){var e=document.querySelector(".t-input-phonemask__options-wrap_open");e&&e.classList.remove("t-input-phonemask__options-wrap_open"),f()}function w(){t_form_phonemask__handleChangeInputValue(t,this)}function S(){t_form_phonemask__chooseCountry(t,this),t_form_phonemask__calcMaxlength(a),v()}function C(e){var t=e.target.closest(".t-input-phonemask__options-wrap"),n=e.target.closest(".t-input-phonemask__select");t||n||v()}function A(e){var t=document.querySelector(".t-input-phonemask__options-wrap_open");if(t){var a=/[-[\]/{}()*+?.\\^$|]/g,o=t.querySelector(".t-input-phonemask__options-item_chosen"),p=[];o&&o.classList.remove("t-input-phonemask__options-item_chosen");var r=/^[0-9]$/.test(e.key),i=e.code&&e.code.startsWith("Numpad")&&r;if(r||i){var m,u={7:"ru"}[s=(s+=e.key).replace(a,"\\$&")];if(u&&n[u])t_form_phonemask__searchCountry(t,s,p,u,n[u].c);else for(var l in n)t_form_phonemask__searchCountry(t,s,p,l,n[l].c);return t_form_phonemask__scrollToCountry(t,p),void c()}var _=/^[a-zA-Z]$/.test(e.key),d=/^[а-яА-Я]$/.test(e.key);if(_||d){s=(s+=e.key).replace(a,"\\$&");var x=new RegExp("(^|\\()"+s,"i");for(var h in n)t_form_phonemask__searchCountry(t,x,p,h,n[h].n);t_form_phonemask__scrollToCountry(t,p),c()}}}t_form_phonemask__changeVal(t,a),o.addEventListener("click",y),a.addEventListener("paste",(function(e){var t=(e.clipboardData||window.clipboardData).getData("text");if(t){t=t.replace(/[^0-9+]/g,"");var n=e.target;n&&(n.value=t,w.call(n),n.dispatchEvent(new Event("change",{bubbles:!0})),e.preventDefault())}})),a.addEventListener("input",w),document.addEventListener("mouseup",C),document.addEventListener("keyup",A),document.querySelector('[data-init-mask="yes"]')||(document.addEventListener("mouseup",C),document.addEventListener("keyup",A))}function t_form_phonemask__copypasteHandling(e,t){var n=e.value,a=n.match(/[0-9]/g)?n.match(/[0-9]/g):n;return"object"==typeof a&&a.length>t.match(/[0-9]/g).join("").length&&(n=t_form_phonemask__removeCountryCodeFromValue(e.value,e.getAttribute("data-phonemask-code"))),n}function t_form_phonemask__addNumberMask(e,t){var n=t.getAttribute("data-phonemask-code"),a=t.getAttribute("data-phonemask-mask").substr(n.length),o=e.match(/\d+/g),p=(o=o?o.join(""):"").split(""),r=a.match(/(\+|\d+|[\s()-]|9+)/g),i="";if(r){for(var s=0;s<r.length;s++)"9"===r[s][0]?(i+=p.slice(0,r[s].length).join(""),p.splice(0,r[s].length)):i+=r[s];i=(i=i.replace(/[\s()-]+$/,"")).replace(/^-+/,""),t.value=i}}function t_form_phonemask__changeVal(e,t){t.value&&(e.querySelector(".js-phonemask-result").value=t.getAttribute("data-phonemask-code")+" "+t.value,e.querySelector(".js-phonemask-result-iso").value=t.getAttribute("data-phonemask-code"))}function t_form_phonemask__setValue(e,t,n,a){if(e){var o=n||t_form_phonemask__phoneToISO(t);if(o){var p=a||{},r=p.noFocus,i=void 0===r||r,s=p.silent,m=void 0!==s&&s,c=t_form_phonemask__prepareISOandCountry(o),u=c.currentCountry,l;t_form_phonemask__handleUpdateCountry(e,{iso:c.iso,code:u.c,mask:u.m,noFocus:i,silent:m});var _=e.querySelector('[name="tildaspec-phone-part[]"]');_&&(_.value=t_form_phonemask__removeCountryCodeFromValue(t,u.c),t_form_phonemask__calcMaxlength(_),t_form_phonemask__handleChangeInputValue(e,_))}}}function t_form_phonemask__removeCountryCodeFromValue(e,t){var n=null==e?void 0:e.replace("+","").replace(/^8/,"7"),a=null==t?void 0:t.replace("+","");return n&&a&&n.startsWith(a)?n.substring(a.length).trim():e}function t_form_phonemask__chooseCountry(e,t){var n,a,o;t_form_phonemask__handleUpdateCountry(e,{code:t.getAttribute("data-phonemask-code"),mask:t.getAttribute("data-phonemask-mask"),iso:t.getAttribute("data-phonemask-country-code"),silent:!1,noFocus:!1})}function t_form_phonemask__handleUpdateCountry(e,t){var n=t.code,a=t.mask,o=t.iso,p=t.noFocus,r=t.silent,i=e.querySelector(".t-input-phonemask"),s=e.querySelector(".t-input-phonemask__select-code"),m=e.querySelector(".t-input-phonemask__select-flag"),c=i.getAttribute("data-phonemask-current");i.setAttribute("data-phonemask-iso",o),i.setAttribute("data-phonemask-code",n),i.setAttribute("data-phonemask-mask",a);var u=a.substr(n.length).replace(/^-+/,"");i.setAttribute("placeholder",u),s.innerHTML=n,m.setAttribute("data-phonemask-flag",o);try{localStorage.setItem("iso_code",o)}catch(l){console.error("Your web browser does not support storing a ISO Phone Code data locally.")}c&&t_form_phonemask__addNumberMask(c,i),t_form_phonemask__changeVal(e,i),t_form_phonemask__changeMinLength(u,n,e),p||i.focus(),r||i.dispatchEvent(new Event("change",{bubbles:!0}))}function t_form_phonemask__handleChangeInputValue(e,t){var n=t.value;"("!==n&&n||(t.value="",e.querySelector(".js-phonemask-result").value="",e.querySelector(".js-phonemask-result-iso").value="");var a="+7"===t.getAttribute("data-phonemask-code"),o=1===n.indexOf("89")||1===n.indexOf("79"),p;a&&o&&(t.value=n.replace(/^.(8|7)9/,"9")),t_form_phonemask__addNumberMask(t_form_phonemask__copypasteHandling(t,t.getAttribute("data-phonemask-without-code")),t),t_form_phonemask__changeVal(e,t),t.setAttribute("data-phonemask-current",t.value)}function t_form_phonemask__searchCountry(e,t,n,a,o){var p=new RegExp(t,"i");-1!==o.search(p)&&(n.push(e.querySelector("#t-phonemask_"+a)),n[0].classList.add("t-input-phonemask__options-item_chosen"))}function t_form_phonemask__scrollToCountry(e,t){if(0!==t.length){var n,a=Array.prototype.sort.call(t,(function(e,t){return e.dataset.phonemaskCode.length-t.dataset.phonemaskCode.length}))[0].getBoundingClientRect().top,o=e.getBoundingClientRect().top,p=document.body.scrollTop+e.scrollTop;e.scrollTop=a-o+p}}function t_form_phonemask__changeMinLength(e,t,n){var a=n.querySelector(".js-phonemask-result"),o,p=t_form_phonemask__getMinMaxLength(e,t).minLength;a.setAttribute("data-tilda-rule-minlength",p)}function t_form_phonemask__getMinMaxLength(e,t){var n=1,a=20;if(e&&t){var o=1,p={"+49":1,"+372":1,"+355":1,"+213":1,"+975":1,"+267":1,"+359":1,"+53":1,"+36":1,"+961":1,"+54":1,"+82":1,"+880":1,"+970":1,"+385":1,"+43":1,"+357":1,"+61":1,"+507":1,"+55":1,"+33":1,"+593":1,"+383":1,"+39":1,"+86":1,"+977":1,"+230":1,"+63":1,"+258":1,"+972":2,"+234":2,"+62":3,"+44":4,"+358":4,"+60":2,"+252":1,"+64":2,"+971":1};n=(a=t.length+1+e.length)-(p[t]||0)}return{maxLength:a,minLength:n}}function t_form_phonemask_getRootZone(){var e=document.getElementById("allrecords"),t;return e&&e.getAttribute("data-tilda-root-zone")||"com"}function t_form_phonemask__validatePhone(e){if(!e||"string"!=typeof e)return!1;var t=function e(t){return t.replace(/[^\d]/g,"")},n=t(e);if(n.length<6||n.length>20)return!1;var a=t_form_phonemask__phoneToISO(n);if(!a)return!1;var o,p=t_form_phonemask__getCountriesList()[a];if(!p)return!1;var r=p.c,i,s,m,c="+ "+n,u=t_form_phonemask__getMinMaxLength(t(p.m.substring(r.length)),r),l=u.minLength,_=u.maxLength,d=c.length;return!(d<l||d>_)}function t_form_phonemask__addStyle(){var e=".t-input-group.js-error-control-box .t-input-phonemask{border:0!important}.t-input_pvis.t-input-phonemask__wrap{padding-top:0;padding-bottom:0}.t-input-phonemask__wrap{position:relative;display:-ms-flexbox;display:-webkit-box;display:flex}.t-input .t-input-phonemask,.t-input-phonemask{height:auto;padding:0;background-color:transparent}.t-input-phonemask__options-wrap{display:none;z-index:9999999;position:fixed;min-width:410px;max-height:200px;padding-top:5px;padding-bottom:5px;background-color:#fff;border:1px solid #eee;border-radius:7px;-webkit-box-shadow:0 0 1px rgba(0,0,0,.1);box-shadow:0 0 1px rgba(0,0,0,.1);overflow-y:scroll}.t-input-phonemask__options-wrap.t-input-phonemask__options-wrap_open{display:block}.t-input-phonemask__options-item{display:-ms-flexbox;display:-webkit-box;display:flex;-ms-flex-pack:end;-webkit-box-pack:justify;justify-content:space-between;-ms-flex-align:center;-webkit-box-align:center;align-items:center;padding:8px 10px;font-family:sans-serif;font-size:14px;color:#000!important;cursor:pointer}.t-input-phonemask__options-right{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.t-input-phonemask__options-item.t-input-phonemask__options-item_chosen,.t-input-phonemask__options-item:hover{background-color:#eee}.t-input-phonemask__select{display:-ms-flexbox;display:-webkit-box;display:flex;-ms-flex-align:center;-webkit-box-align:center;align-items:center;-ms-flex-negative:0;flex-shrink:0;margin-right:5px;margin-left:0;font-size:16px;cursor:pointer}.t-input-phonemask__select-triangle{position:relative;margin-left:6px;border-style:solid;border-width:5px 4px 0;border-color:#9a9a9a transparent transparent}.t-input-phonemask__select-code{white-space:nowrap}.t-input-phonemask__options-code,.t-input-phonemask__select-code{margin-left:10px}.t-input-phonemask__options-flag,.t-input-phonemask__select-flag{width:18px;min-width:18px;height:13px;background-color:#c5c5c5;-webkit-box-shadow:0 0 5px rgba(0,0,0,.3);box-shadow:0 0 5px rgba(0,0,0,.3)}.t-input-phonemask__options-flag{margin-left:8px}.t-input-phonemask__options-wrap::-webkit-scrollbar{width:8px;height:15px}.t-input-phonemask__options-wrap::-webkit-scrollbar-thumb{border-radius:7px;background:#c2c9d2}@media screen and (max-width:640px){.t-input-phonemask__options-wrap{min-width:auto}}.t-input-phonemask__options-flag,.t-input-phonemask__select-flag{background-image:url(https://static.tildacdn."+t_form_phonemask_getRootZone()+'/lib/flags/flags7.png);background-repeat:no-repeat;display:inline-block}.t-input-phonemask__options-flag_np,.t-input-phonemask__select-flag[data-phonemask-flag="np"]{width:16px;min-width:16px}',t={ad:"-5px -5px",ae:"-33px -5px",af:"-61px -5px",ag:"-89px -5px",al:"-117px -5px",am:"-145px -5px",ao:"-173px -5px",ar:"-201px -5px",at:"-229px -5px",au:"-257px -5px",az:"-285px -5px",ba:"-313px -5px",bb:"-5px -28px",bd:"-33px -28px",be:"-61px -28px",bf:"-89px -28px",bg:"-117px -28px",bh:"-145px -28px",bi:"-173px -28px",bj:"-201px -28px",bm:"-229px -28px",bn:"-257px -28px",bo:"-285px -28px",bq:"-89px -258px",br:"-313px -28px",bs:"-5px -51px",bt:"-33px -51px",bw:"-61px -51px",by:"-89px -51px",bz:"-117px -51px",ca:"-145px -51px",ky:"-367px -28px",cd:"-173px -51px",cf:"-201px -51px",cg:"-229px -51px",ch:"-257px -51px",ci:"-285px -51px",ck:"-313px -51px",cl:"-5px -74px",cm:"-33px -74px",cn:"-61px -74px",co:"-89px -74px",cr:"-117px -74px",cu:"-145px -74px",cv:"-173px -74px",cz:"-229px -74px",cy:"-201px -74px",de:"-257px -74px",dj:"-285px -74px",dk:"-313px -74px",dm:"-5px -97px",do:"-33px -97px",dz:"-61px -97px",ec:"-89px -97px",ee:"-117px -97px",eg:"-145px -97px",eh:"-173px -97px",er:"-201px -97px",es:"-229px -97px",et:"-257px -97px",fi:"-285px -97px",fj:"-313px -97px",fm:"-5px -120px",fr:"-33px -120px",ga:"-61px -120px",gb:"-89px -120px",gd:"-117px -120px",ge:"-145px -120px",gh:"-173px -120px",gm:"-201px -120px",gn:"-229px -120px",gq:"-257px -120px",gr:"-285px -120px",gt:"-313px -120px",gw:"-5px -143px",gy:"-33px -143px",hk:"-61px -143px",hn:"-89px -143px",hr:"-117px -143px",ht:"-145px -143px",hu:"-173px -143px",id:"-201px -143px",ie:"-229px -143px",il:"-257px -143px",in:"-285px -143px",iq:"-313px -143px",ir:"-5px -166px",is:"-33px -166px",it:"-61px -166px",jm:"-89px -166px",jo:"-117px -166px",jp:"-145px -166px",ke:"-173px -166px",kg:"-201px -166px",kh:"-229px -166px",ki:"-257px -166px",km:"-285px -166px",kn:"-313px -166px",kp:"-5px -189px",kr:"-33px -189px",ks:"-61px -189px",kw:"-89px -189px",kz:"-117px -189px",la:"-145px -189px",lb:"-173px -189px",lc:"-201px -189px",li:"-229px -189px",lk:"-257px -189px",lr:"-285px -189px",ls:"-313px -189px",lt:"-5px -212px",lu:"-33px -212px",lv:"-61px -212px",ly:"-89px -212px",ma:"-117px -212px",mc:"-145px -212px",md:"-173px -212px",me:"-201px -212px",mg:"-229px -212px",mh:"-257px -212px",mk:"-285px -212px",ml:"-313px -212px",mm:"-5px -235px",mn:"-33px -235px",mo:"-61px -235px",mr:"-89px -235px",mt:"-117px -235px",mu:"-145px -235px",mv:"-173px -235px",mw:"-201px -235px",mb:"-229px -235px",mx:"-229px -235px",my:"-257px -235px",mz:"-285px -235px",na:"-313px -235px",ne:"-5px -258px",ng:"-33px -258px",ni:"-61px -258px",nl:"-89px -258px",no:"-117px -258px",np:"-341px -5px",nr:"-145px -258px",nu:"-173px -258px",nc:"-229px -350px",nz:"-201px -258px",om:"-229px -258px",pa:"-257px -258px",pe:"-285px -258px",pg:"-313px -258px",ph:"-5px -281px",pk:"-33px -281px",pl:"-61px -281px",ps:"-89px -281px",pt:"-117px -281px",pw:"-145px -281px",py:"-173px -281px",qa:"-201px -281px",ro:"-229px -281px",rs:"-257px -281px",ru:"-285px -281px",rw:"-313px -281px",sa:"-5px -304px",sb:"-33px -304px",sc:"-61px -304px",sd:"-89px -304px",se:"-117px -304px",sg:"-145px -304px",si:"-173px -304px",sk:"-201px -304px",sl:"-229px -304px",sm:"-257px -304px",sn:"-285px -304px",so:"-313px -304px",sr:"-5px -327px",ss:"-33px -327px",st:"-61px -327px",sv:"-89px -327px",sy:"-117px -327px",sz:"-145px -327px",td:"-173px -327px",tg:"-201px -327px",th:"-229px -327px",tj:"-257px -327px",tl:"-285px -327px",tm:"-313px -327px",tn:"-367px -5px",to:"-341px -28px",tr:"-341px -51px",tt:"-341px -74px",tv:"-341px -97px",tw:"-341px -120px",tz:"-341px -143px",ua:"-341px -166px",ug:"-341px -189px",us:"-341px -212px",uy:"-341px -235px",uz:"-341px -258px",va:"-341px -281px",vc:"-341px -304px",ve:"-341px -327px",vn:"-5px -350px",vu:"-33px -350px",ws:"-61px -350px",xk:"-89px -350px",ye:"-117px -350px",za:"-145px -350px",zm:"-173px -350px",zw:"-201px -350px"};Object.keys(t).forEach((function(n){e+=".t-input-phonemask__options-flag_"+n+",.t-input-phonemask__select-flag[data-phonemask-flag="+n+"] {background-position:"+t[n]+"}"}));var n=document.createElement("style");n.id="phone-mask-style",n.innerHTML=e,document.head.appendChild(n)}function t_form_phonemask__getCountriesList(){return{af:{n:"Afghanistan (افغانستان)",c:"+93",m:"+93-99-999-9999"},al:{n:"Albania (Shqipëri)",c:"+355",m:"+355(999) 999-999"},dz:{n:"Algeria (الجزائر)",c:"+213",m:"+213-99-999-9999"},ad:{n:"Andorra",c:"+376",m:"+***********"},ao:{n:"Angola",c:"+244",m:"+244(999) 999-999"},am:{n:"Armenia (Հայաստան)",c:"+374",m:"+374-99-999-999"},ag:{n:"Antigua and Barbuda",c:"+1 (268)",m:"+1 (268)999-9999"},ar:{n:"Argentina",c:"+54",m:"+54(999) 9999-9999"},au:{n:"Australia",c:"+61",m:"+61-99-9999-9999"},at:{n:"Austria (Österreich)",c:"+43",m:"+43**************9"},az:{n:"Azerbaijan (Azərbaycan)",c:"+994",m:"+994-99-999-99-99"},bs:{n:"Bahamas",c:"+1 (242)",m:"+1 (242)999-9999"},bh:{n:"Bahrain (البحرين)",c:"+973",m:"+973-9999-9999"},bd:{n:"Bangladesh (বাংলাদেশ)",c:"+880",m:"+880-9999-999999"},bb:{n:"Barbados",c:"+1 (246)",m:"+1 (246)999-9999"},by:{n:"Belarus (Беларусь)",c:"+375",m:"+375(99) 999-99-99"},be:{n:"Belgium (België)",c:"+32",m:"+32(999) 999-999"},bz:{n:"Belize",c:"+501",m:"+501-999-9999"},bj:{n:"Benin (Bénin)",c:"+229",m:"+229-99-99-9999"},bt:{n:"Bhutan (འབྲུག)",c:"+975",m:"+975-9-999-9999"},bo:{n:"Bolivia",c:"+591",m:"+591-9-999-9999"},ba:{n:"Bosnia and Herzegovina",c:"+387",m:"+387-99-999-999"},bw:{n:"Botswana",c:"+267",m:"+267-99-999-999"},br:{n:"Brazil (Brasil)",c:"+55",m:"+55(99) 99999-9999"},bn:{n:"Brunei",c:"+673",m:"+673-999-9999"},bg:{n:"Bulgaria (България)",c:"+359",m:"+359(999) 999-999"},bf:{n:"Burkina Faso",c:"+226",m:"+226-99-99-9999"},bi:{n:"Burundi (Uburundi)",c:"+257",m:"+257-99-99-9999"},kh:{n:"Cambodia (កម្ពុជា)",c:"+855",m:"+855-99-999-999"},cm:{n:"Cameroon (Cameroun)",c:"+237",m:"+237-9-99-99-99-99"},ca:{n:"Canada",c:"+1",m:"+1**************"},cv:{n:"Cape Verde (Kabu Verdi)",c:"+238",m:"+238(999) 99-99"},bq:{n:"Caribbean Netherlands",c:"+599",m:"+599-9-999-9999"},ky:{n:"Cayman Islands",c:"+1",m:"+1**************"},cf:{n:"Central African Republic (République centrafricaine)",c:"+236",m:"+236-99-99-9999"},td:{n:"Chad (Tchad)",c:"+235",m:"+235-99-99-99-99"},cl:{n:"Chile",c:"+56",m:"+56-9-9999-9999"},cn:{n:"China (中国)",c:"+86",m:"+86(999) 9999-9999"},co:{n:"Colombia",c:"+57",m:"+57**************"},km:{n:"Comoros (جزر القمر)",c:"+269",m:"+269-99-99999"},cd:{n:"Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)",c:"+243",m:"+243(999) 999-999"},cg:{n:"Congo (Republic) (Congo-Brazzaville)",c:"+242",m:"+242-99-999-9999"},ck:{n:"Cook Islands",c:"+682",m:"+682-99-999"},cr:{n:"Costa Rica",c:"+506",m:"+506-9999-9999"},ci:{n:"Cote d’Ivoire",c:"+225",m:"+225-99-999-999"},hr:{n:"Croatia (Hrvatska)",c:"+385",m:"+385-99-999-9999"},cu:{n:"Cuba",c:"+53",m:"+53-9-999-9999"},cy:{n:"Cyprus (Κύπρος)",c:"+357",m:"+357-99-999-999"},cz:{n:"Czech Republic (Česká republika)",c:"+420",m:"+420(999) 999-999"},dk:{n:"Denmark (Danmark)",c:"+45",m:"+45-99-99-99-99"},dj:{n:"Djibouti",c:"+253",m:"+253-99-99-99-99"},dm:{n:"Dominica",c:"+1 (767)",m:"+1 (767)999-9999"},do:{n:"Dominican Republic (República Dominicana)",c:"+1",m:"+1**************"},ec:{n:"Ecuador",c:"+593",m:"+593-99-999-9999"},eg:{n:"Egypt (مصر)",c:"+20",m:"+20**************"},sv:{n:"El Salvador",c:"+503",m:"+503-99-99-9999"},gq:{n:"Equatorial Guinea (Guinea Ecuatorial)",c:"+240",m:"+240-99-999-9999"},er:{n:"Eritrea",c:"+291",m:"+291-9-999-999"},ee:{n:"Estonia (Eesti)",c:"+372",m:"+372-9999-9999"},et:{n:"Ethiopia",c:"+251",m:"+251-99-999-9999"},fj:{n:"Fiji",c:"+679",m:"+679-999-9999"},fi:{n:"Finland (Suomi)",c:"+358",m:"+358-999-9999999"},fr:{n:"France",c:"+33",m:"+33**************"},ga:{n:"Gabon",c:"+241",m:"+241-9-99-99-99"},gm:{n:"Gambia",c:"+220",m:"+220(999) 99-99"},ge:{n:"Georgia (საქართველო)",c:"+995",m:"+995(999) 999-999"},de:{n:"Germany (Deutschland)",c:"+49",m:"+49**************9"},gh:{n:"Ghana (Gaana)",c:"+233",m:"+233(999) 999-999"},gr:{n:"Greece (Ελλάδα)",c:"+30",m:"+30**************"},gd:{n:"Grenada",c:"+1 (473)",m:"+1 (473)999-9999"},gt:{n:"Guatemala",c:"+502",m:"+502-9-999-9999"},gn:{n:"Guinea (Guinée)",c:"+224",m:"+224-999-99-99-99"},gw:{n:"Guinea-Bissau (Guiné Bissau)",c:"+245",m:"+245-9-999999"},gy:{n:"Guyana",c:"+592",m:"+592-999-9999"},ht:{n:"Haiti",c:"+509",m:"+509-99-99-9999"},hn:{n:"Honduras",c:"+504",m:"+504-9999-9999"},hk:{n:"Hong Kong (香港)",c:"+852",m:"+852-9999-9999"},hu:{n:"Hungary (Magyarország)",c:"+36",m:"+36(999) 999-999"},is:{n:"Iceland (Ísland)",c:"+354",m:"+354-999-9999"},in:{n:"India (भारत)",c:"+91",m:"+91(9999) 999-999"},id:{n:"Indonesia",c:"+62",m:"+62(999) 999-99-9999"},ir:{n:"Iran (ایران)",c:"+98",m:"+98**************"},iq:{n:"Iraq (العراق)",c:"+964",m:"+964**************"},ie:{n:"Ireland",c:"+353",m:"+353(999) 999-999"},il:{n:"Israel (ישראל)",c:"+972",m:"+972-999-999-9999"},it:{n:"Italy (Italia)",c:"+39",m:"+39(999) 9999-999"},jm:{n:"Jamaica",c:"+1",m:"+1**************"},jp:{n:"Japan (日本)",c:"+81",m:"+81-99-9999-9999"},jo:{n:"Jordan (الأردن)",c:"+962",m:"+962-9-9999-9999"},kz:{n:"Kazakhstan (Казахстан)",c:"+7",m:"+7(999) 999-99-99"},ke:{n:"Kenya",c:"+254",m:"+254-999-999999"},ki:{n:"Kiribati",c:"+686",m:"+686-9999-9999"},xk:{n:"Kosovo (Republic)",c:"+383",m:"+383-99-999-999"},kw:{n:"Kuwait (الكويت)",c:"+965",m:"+965-9999-9999"},kg:{n:"Kyrgyzstan (Кыргызстан)",c:"+996",m:"+996(999) 999-999"},la:{n:"Laos (ລາວ)",c:"+856",m:"+856-99-999-999"},lv:{n:"Latvia (Latvija)",c:"+371",m:"+371-99-999-999"},lb:{n:"Lebanon (لبنان)",c:"+961",m:"+961-99-999-999"},ls:{n:"Lesotho",c:"+266",m:"+266-9-999-9999"},lr:{n:"Liberia",c:"+231",m:"+231-99-999-9999"},ly:{n:"Libya (ليبيا)",c:"+218",m:"+218-99-999-999"},li:{n:"Liechtenstein",c:"+423",m:"+423-999-99-99"},lt:{n:"Lithuania (Lietuva)",c:"+370",m:"+370(999) 99-999"},lu:{n:"Luxembourg",c:"+352",m:"+352(999) 999-999"},mo:{n:"Macao",c:"+853",m:"+853-9999-9999"},mk:{n:"Macedonia (FYROM) (Македонија)",c:"+389",m:"+389-99-999-999"},mg:{n:"Madagascar (Madagasikara)",c:"+261",m:"+261-99-99-99999"},mw:{n:"Malawi",c:"+265",m:"+265-9-9999-9999"},my:{n:"Malaysia",c:"+60",m:"+60-99-9999-9999"},mv:{n:"Maldives",c:"+960",m:"+960-999-9999"},ml:{n:"Mali",c:"+223",m:"+223-99-99-9999"},mt:{n:"Malta",c:"+356",m:"+356-9999-9999"},mh:{n:"Marshall Islands",c:"+692",m:"+692-999-9999"},mr:{n:"Mauritania (موريتانيا)",c:"+222",m:"+222-99-99-9999"},mu:{n:"Mauritius (Moris)",c:"+230",m:"+230-999-99999"},mx:{n:"Mexico (México)",c:"+52",m:"+52**************"},mb:{n:"Mexico (México)",c:"+521",m:"+521**************"},fm:{n:"Micronesia",c:"+691",m:"+691-999-9999"},md:{n:"Moldova (Republica Moldova)",c:"+373",m:"+373-9999-9999"},mc:{n:"Monaco",c:"+377",m:"+377-99-999-999"},mn:{n:"Mongolia (Монгол)",c:"+976",m:"+976-99-99-9999"},me:{n:"Montenegro (Crna Gora)",c:"+382",m:"+382-99-999-999"},ma:{n:"Morocco (المغرب)",c:"+212",m:"+212-99-9999-999"},mz:{n:"Mozambique (Moçambique)",c:"+258",m:"+258-999-999-999"},mm:{n:"Myanmar (Burma) (မြန်မာ)",c:"+95",m:"+95-99-999-999"},na:{n:"Namibia (Namibië)",c:"+264",m:"+264-99-999-9999"},nr:{n:"Nauru",c:"+674",m:"+674-999-9999"},np:{n:"Nepal (नेपाल)",c:"+977",m:"+977-999-999-9999"},nl:{n:"Netherlands (Nederland)",c:"+31",m:"+31-99-999-9999"},nc:{n:"New Caledonia",c:"+687",m:"+687 99-99-99"},nz:{n:"New Zealand",c:"+64",m:"+64(999)999-9999"},ni:{n:"Nicaragua",c:"+505",m:"+505-9999-9999"},ne:{n:"Niger (Nijar)",c:"+227",m:"+227-99-99-9999"},ng:{n:"Nigeria",c:"+234",m:"+234-999-999-9999"},nu:{n:"Niue",c:"+683",m:"+683-9999"},kp:{n:"North Korea (조선 민주주의 인민 공화국)",c:"+850",m:"+850-99-999-999"},no:{n:"Norway (Norge)",c:"+47",m:"+47(999) 99-999"},om:{n:"Oman (عُمان)",c:"+968",m:"+968-99-999-999"},pa:{n:"Panama",c:"+507",m:"+507-9999-9999"},pk:{n:"Pakistan (پاکستان)",c:"+92",m:"+92**************"},pw:{n:"Palau",c:"+680",m:"+680-999-9999"},ps:{n:"Palestinian Territory",c:"+970",m:"+970-99 999 9999"},pg:{n:"Papua New Guinea",c:"+675",m:"+675(999) 99-999"},py:{n:"Paraguay",c:"+595",m:"+595(999) 999-999"},pe:{n:"Peru (Perú)",c:"+51",m:"+51(999) 999-999"},ph:{n:"Philippines",c:"+63",m:"+63**************"},pl:{n:"Poland (Polska)",c:"+48",m:"+48(999) 999-999"},pt:{n:"Portugal",c:"+351",m:"+351-99-999-9999"},qa:{n:"Qatar (قطر)",c:"+974",m:"+974-9999-9999"},ro:{n:"Romania (România)",c:"+40",m:"+40-99-999-9999"},ru:{n:"Russian Federation (Российская Федерация)",c:"+7",m:"+7(999) 999-99-99"},rw:{n:"Rwanda",c:"+250",m:"+250(999) 999-999"},kn:{n:"Saint Kitts and Nevis",c:"+1 (869)",m:"+1 (869)999-9999"},lc:{n:"Saint Lucia",c:"+1 (758)",m:"+1 (758)999-9999"},vc:{n:"Saint Vincent and the Grenadines",c:"+1 (784)",m:"+1 (784)999-9999"},ws:{n:"Samoa",c:"+685",m:"+685-99-9999"},sm:{n:"San Marino",c:"+378",m:"+378-9999-999999"},st:{n:"Sao Tome and Principe (São Tomé e Príncipe)",c:"+239",m:"+239-99-99999"},sa:{n:"Saudi Arabia (المملكة العربية السعودية)",c:"+966",m:"+966-9-9999-9999"},sn:{n:"Senegal (Sénégal)",c:"+221",m:"+221-99-999-9999"},rs:{n:"Serbia (Србија)",c:"+381",m:"+381-99-999-9999"},sc:{n:"Seychelles",c:"+248",m:"+248-9-999-999"},sl:{n:"Sierra Leone",c:"+232",m:"+232-99-999999"},sg:{n:"Singapore",c:"+65",m:"+65-9999-9999"},sk:{n:"Slovakia (Slovensko)",c:"+421",m:"+421(999) 999-999"},si:{n:"Slovenia (Slovenija)",c:"+386",m:"+386-99-999-999"},sb:{n:"Solomon Islands",c:"+677",m:"+677-999-9999"},so:{n:"Somalia (Soomaaliya)",c:"+252",m:"+252-999-999-999"},za:{n:"South Africa",c:"+27",m:"+27-99-999-9999"},kr:{n:"South Korea (대한민국)",c:"+82",m:"+82-99-9999-9999"},ss:{n:"South Sudan (جنوب السودان)",c:"+211",m:"+211-99-999-9999"},es:{n:"Spain (España)",c:"+34",m:"+34(999) 999-999"},lk:{n:"Sri Lanka (ශ්‍රී ලංකාව)",c:"+94",m:"+94-99-999-9999"},sd:{n:"Sudan (السودان)",c:"+249",m:"+249-99-999-9999"},sr:{n:"Suriname",c:"+597",m:"+597-999-9999"},sz:{n:"Swaziland",c:"+268",m:"+268-99-99-9999"},se:{n:"Sweden (Sverige)",c:"+46",m:"+46-99-999-9999"},ch:{n:"Switzerland (Schweiz)",c:"+41",m:"+41-99-999-9999"},sy:{n:"Syria (سوريا)",c:"+963",m:"+963-99-9999-999"},tw:{n:"Taiwan (台灣)",c:"+886",m:"+886-9999-9999"},tj:{n:"Tajikistan",c:"+992",m:"+992-99-999-9999"},tz:{n:"Tanzania",c:"+255",m:"+255-99-999-9999"},th:{n:"Thailand (ไทย)",c:"+66",m:"+66-99-999-9999"},tg:{n:"Togo",c:"+228",m:"+228-99-999-999"},to:{n:"Tonga",c:"+676",m:"+676-99999"},tt:{n:"Trinidad and Tobago",c:"+1 (868)",m:"+1 (868)999-9999"},tn:{n:"Tunisia (تونس)",c:"+216",m:"+216-99-999-999"},tr:{n:"Turkey (Türkiye)",c:"+90",m:"+90**************"},tm:{n:"Turkmenistan",c:"+993",m:"+993-9-999-9999"},tv:{n:"Tuvalu",c:"+688",m:"+688-999999"},ug:{n:"Uganda",c:"+256",m:"+256(999) 999-999"},ua:{n:"Ukraine (Україна)",c:"+380",m:"+380(99) 999-99-99"},ae:{n:"United Arab Emirates (الإمارات العربية المتحدة)",c:"+971",m:"+971-99-999-99999"},gb:{n:"United Kingdom",c:"+44",m:"+44-99-9999-99999"},us:{n:"USA",c:"+1",m:"+1**************"},uy:{n:"Uruguay",c:"+598",m:"+598-9-999-99-99"},uz:{n:"Uzbekistan (Oʻzbekiston)",c:"+998",m:"+998-99-999-9999"},vu:{n:"Vanuatu",c:"+678",m:"+678-99-99999"},va:{n:"Vatican City (Città del Vaticano)",c:"+39",m:"+39-9-999-99999"},ve:{n:"Venezuela",c:"+58",m:"+58**************"},vn:{n:"Vietnam (Việt Nam)",c:"+84",m:"+84-99-9999-999"},ye:{n:"Yemen (اليمن)",c:"+967",m:"+967-9-999-999"},zm:{n:"Zambia",c:"+260",m:"+260-99-999-9999"},zw:{n:"Zimbabwe",c:"+263",m:"+263-9-999999"}}}function t_form_phonemask__phoneToISO(e){if(e.length<1)return"";var t=e.substring(0,17).replace(/[^\d]/g,"");if(t.startsWith("0"))return"";var n={1:{1268:"ag",1242:"bs",1246:"bb",1767:"dm",1473:"gd",1869:"kn",1758:"lc",1784:"vc",1868:"tt",1876:"jm",1345:"ky",1809:"do",1829:"do",1849:"do",1905:"ca",1201:"us",1:"us"},2:{213:"dz",244:"ao",229:"bj",267:"bw",226:"bf",257:"bi",237:"cm",238:"cv",236:"cf",235:"td",269:"km",243:"cd",242:"cg",225:"ci",253:"dj",240:"gq",291:"er",251:"et",241:"ga",220:"gm",233:"gh",224:"gn",245:"gw",254:"ke",266:"ls",231:"lr",218:"ly",261:"mg",265:"mw",223:"ml",222:"mr",230:"mu",212:"ma",258:"mz",264:"na",227:"ne",234:"ng",250:"rw",239:"st",221:"sn",248:"sc",232:"sl",252:"so",211:"ss",249:"sd",268:"sz",255:"tz",228:"tg",216:"tn",256:"ug",260:"zm",263:"zw",20:"eg",27:"za"},3:{355:"al",376:"ad",374:"am",375:"by",387:"ba",359:"bg",385:"hr",357:"cy",372:"ee",358:"fi",354:"is",353:"ie",383:"xk",371:"lv",370:"lt",352:"lu",389:"mk",356:"mt",373:"md",377:"mc",382:"me",351:"pt",378:"sm",381:"rs",386:"si",380:"ua",379:"va",32:"be",33:"fr",30:"gr",36:"hu",31:"nl",34:"es",39:"it"},4:{420:"cz",423:"li",421:"sk",43:"at",45:"dk",49:"de",47:"no",48:"pl",40:"ro",46:"se",41:"ch",44:"gb"},5:{5993:"bq",5994:"bq",5997:"bq",501:"bz",591:"bo",506:"cr",593:"ec",503:"sv",502:"gt",592:"gy",509:"ht",504:"hn",521:"mb",505:"ni",507:"pa",595:"py",597:"sr",598:"uy",599:"bq",54:"ar",55:"br",56:"cl",57:"co",53:"cu",52:"mx",51:"pe",58:"ve"},6:{673:"bn",682:"ck",679:"fj",686:"ki",692:"mh",691:"fm",674:"nr",687:"nc",683:"nu",680:"pw",675:"pg",685:"ws",677:"sb",676:"to",688:"tv",678:"vu",61:"au",62:"id",60:"my",64:"nz",63:"ph",65:"sg",66:"th"},7:{76:"kz",77:"kz",7:"ru"},8:{880:"bd",855:"kh",852:"hk",856:"la",853:"mo",850:"kp",886:"tw",86:"cn",81:"jp",82:"kr",84:"vn",89:"ru"},9:{994:"az",973:"bh",975:"bt",995:"ge",964:"iq",972:"il",962:"jo",965:"kw",996:"kg",961:"lb",960:"mv",976:"mn",977:"np",968:"om",970:"ps",974:"qa",966:"sa",963:"sy",992:"tj",993:"tm",971:"ae",998:"uz",967:"ye",93:"af",91:"in",98:"ir",95:"mm",92:"pk",94:"lk",90:"tr"}},a=t[0];a&&(n=n[a]);for(var o=0,p=Object.entries(n);o<p.length;o++){var r=p[o],i=r[0],s=r[1];if(t.startsWith(i))return s}return""}Array.prototype.some||(Array.prototype.some=function(e){"use strict";if(null==this)throw new TypeError("Array.prototype.some called on null or undefined");if("function"!=typeof e)throw new TypeError;for(var t=Object(this),n=t.length>>>0,a=arguments.length>=2?arguments[1]:void 0,o=0;o<n;o++)if(o in t&&e.call(a,t[o],o,t))return!0;return!1}),function(e){var t=e.matches||e.matchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector;e.matches=e.matchesSelector=t||function e(t){var e=document.querySelectorAll(t),n=this;return Array.prototype.some.call(e,(function(e){return e===n}))}}(Element.prototype),Element.prototype.closest||(Element.prototype.closest=function(e){for(var t=this;t&&1===t.nodeType;){if(Element.prototype.matches.call(t,e))return t;t=t.parentElement||t.parentNode}return null}),t_ready(t_form_phonemask__initPhoneMask);
