function t_posWidget__init(){var t;if("edit"!==document.getElementById("allrecords").getAttribute("data-tilda-mode")){var e=document.querySelector('.t706__carticon:not([class*="t-menuwidgeticons__button_hidden"])'),i=document.querySelector('.t1002__wishlisticon:not([class*="t-menuwidgeticons__button_hidden"])'),d=document.querySelector('.t985__search-widget-button:not([class*="t-menuwidgeticons__button_hidden"])'),o=window.tildaMembers||document.querySelector('script[src*="tilda-buyer-dashboard"]');if(e||i||d||o)if(window.tPosWidget={widgetCount:0,widgets:[],widgetRect:{},margin:20},e&&t_posWidget__addWidget({widgetIsInit:!1,widgetDataInit:"tcart_initted",widget:e,widgetId:"t706-widget-style",widgetClass:"t706__carticon",widgetShowClass:"t706__carticon_showed"}),i&&t_posWidget__addWidget({widgetIsInit:!1,widgetDataInit:"twishlist_initted",widget:i,widgetId:"t1002-widget-style",widgetClass:"t1002__wishlisticon",widgetShowClass:"t1002__wishlisticon_showed"}),d&&t_posWidget__addWidget({widgetIsInit:!1,widgetDataInit:"tsearchwidget_initted",widget:d,widgetId:"t985-widget-style",widgetClass:"t985__search-widget-button",widgetShowClass:"t-search-widget__button_showed"}),tPosWidget.widgetCount<2&&!o)window.tPosWidget={};else{tPosWidget.widgetCount>3&&(tPosWidget.height=60),document.head.insertAdjacentHTML("beforeend","<style>.t-pos-widget__hide { opacity: 0 !important; z-index: -1; }</style>"),t_posWidget__updateStyleWidget();var n=window.innerWidth;window.addEventListener("resize",t_posWidget__debounce((function(){(window.innerWidth<=960&&n>960||window.innerWidth>960&&n<=960||window.innerWidth>=640&&n<=640||window.innerWidth<=640&&n<=960)&&(n=window.innerWidth,t_posWidget__updateStyleWidget())})))}}}function t_posWidget__updateStyleWidget(){if(window.tPosWidget&&tPosWidget.widgetCount&&!(tPosWidget.widgetCount<2)){var t=window.innerWidth<=960;t_posWidget__hideWidget(),t_posWidget__checkWidgetsInit().finally((function(){t_posWidget__showWidget()})).then((function(){tPosWidget.widgets.forEach((function(e){t_posWidget__addStyleWidget(e,t)})),tPosWidget.widgetRect={}}))}}function t_posWidget__addStyleWidget(t,e){var i=document.getElementById(t.widgetId);i&&i.remove();var d=tPosWidget.widgetRect,o=window.getComputedStyle(t.widget),n=parseInt(o.top),s=parseInt(o.right),g=parseInt(o.height),a=window.scrollBarWidthCompensator;if(a&&a.isInited){var w=a.scrollBarWidth;0===a.scrollBarWidth&&document.body.style.paddingRight&&(w=parseInt(document.body.style.paddingRight)),s-=w}if(!(!t.widget.classList.contains(t.widgetShowClass)&&"none"===o.display||100!==n||50!==s&&20!==s))if(g||"none"!==o.display||(t.widget.style.display="block",t.widget.style.opacity=0,g=parseInt(o.height),t.widget.style.display="",t.widget.style.opacity=1),d.top){var _=t.widget.closest(".t-rec"),r=_.id,c=parseInt(d.top)+parseInt(d.height)+tPosWidget.margin,l=parseInt(d.right),W,h="",u="";u+="#"+r+" ."+t.widgetClass+"{",u+="top:"+c+"px;",u+="right:"+l+"px;",u+="}",h+='<style id="'+t.widgetId+'">',e?(h+="@media screen and (max-width: 960px) {",h+=u,h+="}"):(h+="@media screen and (min-width: 961px) {",h+=u,h+="}"),h+="</style>",_.insertAdjacentHTML("beforeend",h),t_posWidget__updateDataPositionWidget({top:c,right:l,height:g})}else t_posWidget__updateDataPositionWidget({top:n,right:s,height:g})}function t_posWidget__updateDataPositionWidget(t){tPosWidget.widgetRect.top=t.top,tPosWidget.widgetRect.right=t.right,tPosWidget.widgetRect.height=t.height}function t_posWidget__showWidget(){tPosWidget.widgets&&tPosWidget.widgets.forEach((function(t){t.widget.classList.remove("t-pos-widget__hide")}))}function t_posWidget__hideWidget(){tPosWidget.widgets&&tPosWidget.widgets.forEach((function(t){t.widget.classList.add("t-pos-widget__hide")}))}function t_posWidget__checkWidgetsInit(){return new Promise((function(t,e){var i=50,d=0,o=t_posWidget__getIsAllWidgetsInit();if(o)return t();var n=setInterval((function(){return d++,(o=t_posWidget__getIsAllWidgetsInit())?(clearInterval(n),t()):d>=i?(clearInterval(n),e()):void 0}),100)}))}function t_posWidget__getIsAllWidgetsInit(){return!!tPosWidget.widgets&&(tPosWidget.widgets.forEach((function(t){window[t.widgetDataInit]&&!t.widgetIsInit&&(t.widgetIsInit=!0)})),tPosWidget.widgets.filter((function(t){return t.widgetIsInit})).length===tPosWidget.widgetCount);var t}function t_posWidget__addWidget(t,e){tPosWidget.widgetCount=tPosWidget.widgetCount+1,e?tPosWidget.widgets=[t].concat(tPosWidget.widgets):tPosWidget.widgets.push(t)}function t_posWidget__debounce(t){var e;return function(){var i=Array.prototype.slice.call(arguments);clearTimeout(e),e=setTimeout((function(){t.apply(null,i)}),200)}}t_onReady(t_posWidget__init);
